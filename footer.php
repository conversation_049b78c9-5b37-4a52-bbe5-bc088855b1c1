</main>

<?php
$footer_logo_url = get_theme_mod('footer_logo');
$logo_id = $footer_logo_url ? attachment_url_to_postid($footer_logo_url) : null;
$footer = get_field('footer_options', 'options');
$footer_menu = $footer['footer_menu'];
$dmca_image = $footer['dmca_image'];
$company_info = $footer['company_info'];
$fanpage = $footer['fanpage'];
$qr_code = $footer['qr_code'];
$documents = $footer['documents'];
$copyright = $footer['copyright'];
$socials = $footer['socials'];
?>
<footer class="bg-primary-1 section xl:pt-20 text-white rem:pb-[15px]">
	<div class="container">
		<div class="top grid grid-cols-2 lg:grid-cols-4 base-gap pb-12">
			<div class="column-1 w-full">
				<div class="ft-title"><?= $footer_menu['title_1'] ?></div>
				<?php if (has_nav_menu('footer-1')) : ?>
					<?php wp_nav_menu([
						'theme_location' => 'footer-1',
						'container' => 'ul',
						'container_class' => 'menu',
						'depth' => 1,
					]); ?>
				<?php endif; ?>
			</div>
			<div class="column-2 w-full">
				<div class="ft-title"><?= $footer_menu['title_2'] ?></div>
				<?php if (has_nav_menu('footer-2')) : ?>
					<?php wp_nav_menu([
						'theme_location' => 'footer-2',
						'container' => 'ul',
						'container_class' => 'menu',
						'depth' => 1,
					]); ?>
				<?php endif; ?>
			</div>
			<div class="column-3 w-full">
				<div class="ft-title"><?= $footer_menu['title_3'] ?></div>
				<?php if (has_nav_menu('footer-3')) : ?>
					<?php wp_nav_menu([
						'theme_location' => 'footer-3',
						'container' => 'ul',
						'container_class' => 'menu',
						'depth' => 1,
					]); ?>
				<?php endif; ?>
			</div>
			<div class="column-4 w-full">
				<div class="ft-title">&#8196 </div>
				<?php if (has_nav_menu('footer-4')) : ?>
					<?php wp_nav_menu([
						'theme_location' => 'footer-4',
						'container' => 'ul',
						'container_class' => 'menu',
						'depth' => 1,
					]); ?>
				<?php endif; ?>
			</div>
		</div>
		<div class="middle flex justify-between gap-3 border-y border-white/30 py-7">
			<div class="column-1 w-full">
				<div class="wrapper">
					<?php if ($logo_id) : ?>
						<div class="logo"><a class="ratio-[45/235] ratio-contain" href="<?= get_home_url() ?>">
								<?= custom_lozad_image($logo_id) ?>
							</a></div>
					<?php endif; ?>
					<?php if ($dmca_image) : ?>
						<div class="dmca mt-10"><span class="[&amp;_img]:h-10"><?= custom_lozad_image($dmca_image) ?></span></div>
					<?php endif; ?>
				</div>
			</div>
			<?php if ($company_info) : ?>
				<?php
				$google_map_url = $company_info['google_map_url'];
				?>
				<div class="column-2 w-full">
					<div class="ft-title"><?= $company_info['title'] ?></div>
					<?php if ($company_info['description']) : ?>
						<div class="description body-14 font-medium">
							<?= $company_info['description'] ?>
						</div>
					<?php endif; ?>
					<?php if ($google_map_url) : ?>
						<a
							href="<?= $google_map_url ?>"
							class="google-map flex items-center body-14 gap-1 mt-2">
							<i class="fa-regular fa-circle-location-arrow"></i><span class="underline">
								<?php _e('Xem vị trí Google map', 'canhcamtheme'); ?>
							</span></a>
					<?php endif; ?>
					<?php if ($company_info['description_2']) : ?>
						<div class="description body-14 font-medium mt-4">
							<?= $company_info['description_2'] ?>
						</div>
					<?php endif; ?>
				</div>
			<?php endif; ?>
			<?php if ($fanpage) : ?>
				<div class="column-3 w-full">
					<div class="ft-title"><?= $fanpage['title'] ?></div>
					<?php if ($fanpage['iframe']) : ?>
						<div class="fanpage-iframe">
							<?= $fanpage['iframe'] ?>
						</div>
					<?php endif; ?>
				</div>
			<?php endif; ?>
			<?php if ($qr_code) : ?>
				<div class="column-4 w-full">
					<div class="ft-title">QR CODE</div>
					<span class="ratio-[1/1]"><?= custom_lozad_image($qr_code) ?></span>
				</div>
			<?php endif; ?>
			<?php if ($documents) : ?>
				<div class="column-5 w-full">
					<div class="ft-title"><?php _e('TẢI TÀI LIỆU', 'canhcamtheme'); ?></div>
					<div class="buttons space-y-4">
						<?php foreach ($documents as $value): ?>
							<?php
							$file = $value['file'];
							$login_page = get_field('login_page', 'option');
							?>
							<a
								<?php if ($file) : ?>
								href="<?php echo is_user_logged_in() ? $file['url'] : get_permalink($login_page); ?>"
								target="_blank"
								<?php endif; ?>
								class="btn  btn-primary "><?= $value['title'] ?><i
									class="fa-regular fa-arrow-down-to-square"></i></a>
						<?php endforeach; ?>
					</div>
				</div>
			<?php endif; ?>
		</div>
		<div class="bottom py-6	 flex items-center justify-between gap-x-2 gap-y-5 -lg:flex-col">
			<div class="copyright text-neutral-200 body-14 flex-1">
				<?= $copyright ?>
			</div>
			<div class="col-right flex items-center gap-x-6 lg:gap-x-10 gap-y-4  flex-wrap">
				<?php if (has_nav_menu('footer-term')) : ?>
					<?php wp_nav_menu([
						'theme_location' => 'footer-term',
						'container' => 'ul',
						'container_class' => 'menu',
						'depth' => 1,
					]); ?>
				<?php endif; ?>
				<?php if ($socials) : ?>
					<div class="socials flex items-center gap-2">
						<?php foreach ($socials as $value): ?>
							<a
								<?php if ($value['href']) : ?>
								href="<?= $value['href'] ?>"
								<?php endif; ?>
								class="social-icon-2"><?= $value['icon'] ?></a>
						<?php endforeach; ?>
					</div>
				<?php endif; ?>
			</div>
		</div>
	</div>
</footer>


<div id="overlay"></div>
<?php
$fixed_tool = get_field('fixed_tool', "options");
$infos = $fixed_tool['infos'];
?>
<div class="fixed z-[100] right-0 pointer-events-none" id="fixed-tool">
	<ul class="flex flex-col gap-y-2">
		<li class="relative scrollToTop">
			<div class="cursor-pointer icon"><i class="fa-thin fa-up-to-line"></i></div>
		</li>
		<?php if ($infos) : ?>
			<?php foreach ($infos as $value): ?>
				<li class="relative">
					<?php if ($value['url']) : ?>
						<a href="<?= $value['url'] ?>" class="absolute inset-0"></a>
					<?php endif; ?>
					<div class="icon">
						<?= $value['icon'] ?>
					</div>
					<div class="content"><?= $value['content'] ?></div>
				</li>
			<?php endforeach; ?>
		<?php endif; ?>
	</ul>
</div>

<?php wp_footer(); ?>
<?= get_field('custom_code', "options")['footer']; ?>
<?php do_action('wp_adding_script') ?>

</body>

</html>