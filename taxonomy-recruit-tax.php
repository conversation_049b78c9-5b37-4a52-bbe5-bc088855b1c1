<!-- $GLOBALS['CANHCAM']['bg_body'] = 'bg-neutral-50'; -->
<?php get_header(); ?>

<?php
$terms = get_queried_object();
$taxonomy = $terms->taxonomy;
$taxonomyID = $terms->term_id;



$post_type = get_taxonomy($taxonomy)->object_type[0];
$posts_per_page = 3;
$paged = (get_query_var('paged')) ? absint(get_query_var('paged')) : 1;
$args = array(
    'post_type' => $post_type,
    'paged' => $paged,
    'posts_per_page' => $posts_per_page,
    'facetwp' => true,
    'tax_query' => array(
        array(
            'taxonomy' => $taxonomy,
            'field' => 'term_id',
            'terms' => $taxonomyID,
        ),
    ),
    'orderby' => 'date',
    'order' => 'DESC',
);
$the_query = new WP_Query($args);

$acf_key = $taxonomy . '_' . $taxonomyID;
$post_template = '/components/recruit/item';
?>
<?php
$section_1 = get_field('section_1', $acf_key);
$section_2 = get_field('section_2', $acf_key);
$section_3 = get_field('section_3', $acf_key);
$section_4 = get_field('section_4', $acf_key);
?>
<section class="recruit-list-1 section xl:py-20">
    <div class="container">
        <div class="grid lg:grid-cols-2 base-gap items-center">
            <div class="col-left">
                <div class="img"><span class="ratio-[382/680]"><?= custom_lozad_image($section_1['image']) ?></span>
                </div>
            </div>
            <div class="col-right">
                <div class="wrapper lg:rem:max-w-[528px] mx-auto">
                    <h1 class="block-title"><?= $section_1['title'] ?></h1>
                    <div class="description simple-prose mt-10 text-justify">
                        <?= $section_1['description'] ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="recruit-list-2 section text-center xl:py-20 bg-black" setbackground="<?= getImageUrl($section_2['background']) ?>">
    <div class="container">
        <div class="wrapper max-w-[1100px] mx-auto w-full">
            <div class="block-title text-white">
                <h2><?= $section_2['title'] ?></h2>
            </div>
            <div class="description simple-prose mt-10 text-white lg:subheader-24">
                <?= $section_2['description'] ?>
            </div>
        </div>
    </div>
</section>
<section class="recruit-list-3 section text-center xl:py-20 bg-neutral-50">
    <div class="container">
        <h2 class="block-title"><?= $section_3['title'] ?></h2>
        <div class="row mt-10 justify-center">
            <?php foreach ($section_3['repeater'] as $value): ?>
                <div class="col-lg-3 col-sm-4 col-6">
                    <div class="item rem:w-[200px] mx-auto">
                        <div class="img"><a class="ratio-[1/1] ratio-contain"><?= custom_lozad_image($value['image']) ?></a>
                        </div>
                        <div class="title subheader-20 font-bold text-black mt-5"><?= $value['title'] ?></div>
                        <div class="ctn mt-2 text-neutral-900"><?= $value['content'] ?></div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php if ($the_query->have_posts()) : ?>
    <section class="recruit-list-4 section text-center xl:py-20">
        <div class="container">
            <h2 class="block-title"><?= $section_4['title'] ?></h2>
            <div class="sub-title text-neutral-900 mt-3"><?= $section_4['sub_title'] ?></div>
            <table>
                <thead>
                    <tr>
                        <th><?php _e('STT', 'canhcamtheme'); ?></th>
                        <th><?php _e('Vị trí tuyển dụng', 'canhcamtheme'); ?></th>
                        <th><?php _e('Khu vực', 'canhcamtheme'); ?></th>
                        <th><?php _e('Hạn nộp hồ sơ', 'canhcamtheme'); ?></th>
                        <th></th>
                    </tr>
                </thead>
                <tbody id="list">
                    <?php if ($the_query->have_posts()) : while ($the_query->have_posts()) : $the_query->the_post(); ?>
                            <?php
                            $index = $the_query->current_post;
                            ?>
                            <?= get_template_part($post_template, '', ['id' => get_the_ID(), 'index' => $index < 10 ? '0' . $index + 1 : $index]) ?>
                    <?php endwhile;
                    endif;
                    wp_reset_postdata(); ?>
                </tbody>
            </table>
            <a class="btn btn-download mx-auto mt-10 btn-primary " id="expand-item"><?php _e('Xem thêm', 'canhcamtheme'); ?><i class="fa-regular fa-angle-down"></i></a>
        </div>
    </section>
<?php endif; ?>

<?php get_footer(); ?>


<script>
    window.addEventListener('load', function() {
        var taxonomy = "<?php echo $taxonomy; ?>";
        var taxonomyID = "<?php echo $taxonomyID; ?>";
        var postType = "<?= $post_type ?>";
        var postTemplate = "<?= $post_template ?>";
        var posts_per_page = "<?= $posts_per_page ?>";
        var paged = "<?= $paged ?>";
        $(document).on('click', "#expand-item", function() {
            paged++;
            $.ajax({
                type: 'POST',
                url: '/wp-admin/admin-ajax.php',
                dataType: 'json',
                data: {
                    action: 'weichie_dynamic_load_more',
                    postType,
                    taxonomyID,
                    taxonomy,
                    template: postTemplate,
                    posts_per_page,
                    paged,
                },
                beforeSend: function() {
                    $('#expand-item').addClass('btn-loading')
                },
                success: function(response) {
                    ajaxComplete(response);
                }
            })
        });


        function ajaxComplete(response) {
            setTimeout(() => {
                $('#expand-item').removeClass('btn-loading')
            }, 300);
            $(`#list`).append(response.html);
            $(`#list tr`).each(function(index) {
                const key = index < 9 ? '0' + (index + 1) : index + 1;
                $(this).find('td:first-child').text(key);
            });
            if (response.max == paged || response.max == 0) {
                $('#expand-item').hide();
            }
            // useVariables.recruit.init()
            FE.lozadInit()
        }

    });
</script>