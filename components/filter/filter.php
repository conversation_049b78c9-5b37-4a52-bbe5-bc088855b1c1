<?php

$arg_city = array(
    'post_type' => 'city',
    'posts_per_page' => -1,
    'suppress_filters' => false,
);
$cities = new WP_Query($arg_city);
// Building class
$arg_building_class = array(
    'post_type' => 'building-class',
    'posts_per_page' => -1,
    'suppress_filters' => false,
);
$building_classes = new WP_Query($arg_building_class);

// Area
$arg_area = array(
    'post_type' => 'area',
    'posts_per_page' => -1,
    'suppress_filters' => false,
);
$areas = new WP_Query($arg_area);


// building-orientation
$arg_building_orientation = array(
    'post_type' => 'building-orientation',
    'posts_per_page' => -1,
    'suppress_filters' => false,
);
$building_orientations = new WP_Query($arg_building_orientation);


$page_search = get_field('page_search', 'option');
$loc_dien_tich = get_field('loc_dien_tich', 'options');
?>

<form id="filter-form" action="<?php echo get_the_permalink($page_search); ?>" method="get">
    <div class="search">
        <input type="text" name="keyword" placeholder="Tìm kiếm theo từ khoá...">
        <button><i class="fa-regular fa-magnifying-glass"></i></button>
    </div>
    <div class="filters mt-2 relative z-2">
        <div class="checkbox-filter split-select" id="select-address">
            <div class="checkbox-filter-wrapper">
                <div class="checkbox-value"><span><?php _e('Khu vực', 'canhcam_theme'); ?></span><i class="fa-regular fa-angle-down"></i></div>
                <div class="checkbox-list size-medium">
                    <div class="checkbox-list-wrapper">
                        <div id="city" class="checkbox-item select-category select-city"><span><?php _e('Tỉnh/ thành phố', 'canhcam_theme'); ?></span>
                            <div class="inner-checkbox-list">
                                <div class="back-to-previous"><?php _e('Tỉnh/ thành phố', 'canhcam_theme'); ?></div>
                                <div class="inner-checkbox-wrapper">
                                    <?php if ($cities->have_posts()) : $key = 0; ?>
                                        <?php while ($cities->have_posts()) : $cities->the_post(); ?>
                                            <div class="checkbox-item radio city-item" data-value="<?php echo get_the_ID(); ?>"><?php the_title(); ?></div>
                                        <?php $key++;
                                        endwhile;
                                        wp_reset_postdata(); ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div id="district" class="checkbox-item select-category select-district"><span><?php _e('Quận/ Huyện', 'canhcam_theme'); ?></span>
                            <div class="inner-checkbox-list">
                                <div class="back-to-previous"><?php _e('Quận/ Huyện', 'canhcam_theme'); ?></div>
                                <div class="notification notification-warning"><?php _e('Vui lòng chọn tỉnh/ thành phố', 'canhcam_theme'); ?></div>
                                <div class="inner-checkbox-wrapper">

                                </div>
                            </div>
                        </div>
                        <div id="ward" class="checkbox-item select-category select-ward"><span><?php _e('Phường/ Xã', 'canhcam_theme'); ?></span>
                            <div class="inner-checkbox-list">
                                <div class="back-to-previous"><?php _e('Phường/ Xã', 'canhcam_theme'); ?></div>
                                <div class="notification notification-warning"><?php _e('Vui lòng chọn quận/ huyện', 'canhcam_theme'); ?></div>
                                <div class="inner-checkbox-wrapper">

                                </div>
                            </div>
                        </div>
                        <div id="street" class="checkbox-item select-category select-street"><span><?php _e('Đường/ Phố', 'canhcam_theme'); ?></span>
                            <div class="inner-checkbox-list">
                                <div class="back-to-previous"><?php _e('Đường/ Phố', 'canhcam_theme'); ?></div>
                                <div class="notification notification-warning"><?php _e('Vui lòng chọn quận/ huyện', 'canhcam_theme'); ?></div>
                                <div class="inner-checkbox-wrapper">

                                </div>
                            </div>
                        </div>
                        <div id="block" class="checkbox-item select-category select-block"><span><?php _e('Khu vực', 'canhcam_theme'); ?></span>
                            <div class="inner-checkbox-list">
                                <div class="back-to-previous"><?php _e('Khu vực', 'canhcam_theme'); ?></div>
                                <div class="notification notification-warning"><?php _e('Vui lòng chọn quận/ huyện', 'canhcam_theme'); ?></div>
                                <div class="inner-checkbox-wrapper">

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="clear-checkbox"><?php _e('Xoá tất cả', 'canhcam_theme'); ?></div>
                </div>
            </div>
        </div>

        <div class="checkbox-filter" id="select-type">
            <div class="checkbox-filter-wrapper">
                <div class="checkbox-value"><span><?php _e('Loại hình', 'canhcam_theme'); ?></span><i class="fa-regular fa-angle-down"></i></div>
                <div class="checkbox-list size-medium">
                    <div class="checkbox-list-wrapper">
                        <div class="checkbox-item" data-value="all"><?php _e('Tất cả', 'canhcam_theme'); ?></div>
                        <?php if ($building_classes->have_posts()) : ?>
                            <?php while ($building_classes->have_posts()) : $building_classes->the_post(); ?>
                                <div class="checkbox-item" data-value="<?php echo get_the_ID(); ?>"><?php the_title(); ?></div>
                            <?php endwhile;
                            wp_reset_postdata(); ?>
                        <?php endif; ?>
                    </div>
                    <div class="clear-checkbox"><?php _e('Xoá tất cả', 'canhcam_theme'); ?></div>
                </div>
            </div>
        </div>

        <div class="checkbox-filter" id="select-price">
            <div class="checkbox-filter-wrapper">
                <div class="checkbox-value"><span><?php _e('Giá', 'canhcam_theme'); ?></span><i class="fa-regular fa-angle-down"></i></div>
                <div class="checkbox-list">
                    <div class="price-range">
                        <div class="inputs">
                            <input class="input-from" type="number" name="price_from" placeholder="<?php _e('Từ', 'canhcam_theme'); ?>" data-value="0" min="0" max="45" />
                            <input class="input-to" type="number" name="price_to" placeholder="<?php _e('Đến', 'canhcam_theme'); ?>" data-value="45" min="0" max="45" />
                        </div>
                        <div class="range-slider">
                            <div class="range-slider-item"></div>
                        </div>
                    </div>
                    <div class="checkbox-list-wrapper">
                        <div class="checkbox-item radio" data-select-all="true" data-from="0" data-to="45"><?php _e('Tất cả mức giá', 'canhcam_theme'); ?></div>
                        <div class="checkbox-item radio" data-from="0" data-to="15">
                            < $15/m2</div>
                                <div class="checkbox-item radio" data-from="15" data-to="20">$15 - $20/m2</div>
                                <div class="checkbox-item radio" data-from="21" data-to="25">$21 - $25/m2</div>
                                <div class="checkbox-item radio" data-from="26" data-to="30">$26 - $30/m2</div>
                                <div class="checkbox-item radio" data-from="31" data-to="35">$31 - $35/m2</div>
                                <div class="checkbox-item radio" data-from="36" data-to="40">$36 - $40/m2</div>
                                <div class="checkbox-item radio" data-from="41" data-to="45">$41 - 45$/m2</div>
                        </div>
                        <div class="clear-checkbox"><?php _e('Xoá tất cả', 'canhcam_theme'); ?></div>
                    </div>
                </div>
            </div>

            <div class="checkbox-filter" id="select-area">
                <div class="checkbox-filter-wrapper">
                    <div class="checkbox-value"><span><?php _e('Diện tích', 'canhcam_theme'); ?></span><i class="fa-regular fa-angle-down"></i></div>
                    <div class="checkbox-list">
                        <div class="price-range">
                            <div class="inputs">
                                <input class="input-from" type="number" name="area_from" placeholder="Từ" data-value="0" min="0" max="2000" />
                                <input class="input-to" type="number" name="area_to" placeholder="Đến" data-value="2000" min="0" max="2000" />
                            </div>
                            <div class="range-slider">
                                <div class="range-slider-item"></div>
                            </div>
                        </div>
                        <div class="checkbox-list-wrapper">
                            <!-- <div class="checkbox-item radio" data-select-all="true" data-from="0" data-to="2000">Tất cả diện tích</div> -->
                            <?php 
                            if(!empty($loc_dien_tich)) {
                                foreach ($loc_dien_tich as $key => $value) {
                                    ?>
                                    <div class="checkbox-item radio area-item" 
                                        data-from="<?php echo $value['dien_tich_tu']; ?>" 
                                        data-to="<?php echo $value['dien_tich_den']; ?>">
                                        <?php echo $value['ten']; ?>
                                    </div>
                                    <?php
                                }
                            }
                            
                            ?>
                        </div>
                        <div class="clear-checkbox"><?php _e('Xoá tất cả', 'canhcam_theme'); ?></div>
                    </div>
                </div>
            </div>

            <div class="checkbox-filter" id="select-direction">
                <div class="checkbox-filter-wrapper">
                    <div class="checkbox-value"><span><?php _e('Hướng tòa nhà', 'canhcam_theme'); ?></span><i class="fa-regular fa-angle-down"></i></div>
                    <div class="checkbox-list">
                        <div class="checkbox-list-wrapper">
                            <div class="checkbox-item" data-value="all">Tất cả </div>
                            <?php if ($building_orientations->have_posts()) : ?>
                                <?php while ($building_orientations->have_posts()) : $building_orientations->the_post(); ?>
                                    <div class="checkbox-item" data-value="<?php echo get_the_ID(); ?>"><?php the_title(); ?></div>
                                <?php endwhile;
                                wp_reset_postdata(); ?>
                            <?php endif; ?>
                        </div>
                        <div class="clear-checkbox"><?php _e('Xoá tất cả', 'canhcam_theme'); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <a disabled class="btn mt-6 btn-primary btn-search"><?php _e('Tìm kiếm', 'canhcam_theme'); ?><i class="fa-regular fa-magnifying-glass"></i></a>
</form>