<?php
$title = get_sub_field('title');
$group_main = get_sub_field('group_main');
$slide_team = get_sub_field('slide_team');
?>
<section class="about-8 scroll-section section xl:py-20" data-title="<?= !empty($title) ? $title : '' ?>">
    <div class="container">
        <h2 class="block-title"><?= !empty($title) ? $title : '' ?></h2>
        <?php if (!empty($group_main)) : ?>
            <div class="boss-item p-5 bg-primary-1/5 xl:pr-30 grid lg:grid-cols-[calc(420/1260*100%)_1fr] items-center rounded-4 base-gap xl:gap-[calc(80/1260*100%)] mt-8">
                <div class="img"><a class="ratio-[360/420] rounded-4">
                        <?php if (!empty($group_main['image'])) : ?>
                            <img class="lozad" data-src="<?= $group_main['image']['url'] ?>" alt="<?= $group_main['image']['alt'] ?? 'image' ?>" />
                        <?php endif; ?>
                    </a>
                </div>
                <div class="content">
                    <div class="title header-32 font-extrabold text-primary-1"><?= !empty($group_main['name']) ? $group_main['name'] : '' ?></div>
                    <div class="position mt-4 body-18 font-medium text-black after:h-0.5 after:w-15 after:bg-primary-2 flex flex-col after:my-5"><?= !empty($group_main['position']) ? $group_main['position'] : '' ?></div>
                    <div class="description font-medium"><?= !empty($group_main['content']) ? $group_main['content'] : '' ?></div>
                </div>
            </div>
        <?php endif; ?>
        <?php if (!empty($slide_team)) : ?>
            <div class="swiper-column-auto relative auto-5-column mt-10 show-half-mobile  allow-touchMove">
                <div class="swiper">
                    <div class="swiper-wrapper">
                        <?php foreach ($slide_team as $key => $item) : ?>
                            <div class="swiper-slide">
                                <div class="item text-center relative">
                                    <div class="img">
                                        <a class="ratio-[1/1] rounded-4">
                                            <?php if (!empty($item['image'])) : ?>
                                                <img class="lozad" data-src="<?= $item['image']['url'] ?>" alt="<?= $item['image']['alt'] ?? 'image' ?>" />
                                            <?php endif; ?>
                                        </a>
                                    </div>
                                    <div class="content">
                                        <div class="title subheader-20 font-bold mt-8 text-primary-1"><?= !empty($item['name']) ? $item['name'] : '' ?></div>
                                        <div class="mt-3 position body-14 font-medium text-neutral-700"><?= !empty($item['position']) ? $item['position'] : '' ?></div>
                                    </div>
                                    <a data-fancybox data-popup="popup-member" data-src="#popup-member-<?= $key ?>" class="absolute inset-0 z-1"></a>
                                    <div hidden>
                                        <div id="popup-member-<?= $key ?>">
                                            <div class="grid grid-cols-12 base-gap">
                                                <div class="img lg:col-span-4 col-span-full lg:max-w-full rem:max-w-[420px] mx-auto lg:mx-0 w-full"><a class="ratio-[1/1] rounded-4">
                                                        <?= custom_lozad_image($item['image']) ?>
                                                    </a>
                                                </div>
                                                <div class="content lg:col-span-8 col-span-full">
                                                    <div class="title subheader-20 font-bold mt-8 text-primary-1"><?= !empty($item['name']) ? $item['name'] : '' ?></div>
                                                    <div class="mt-3 position body-14 font-medium text-neutral-700"><?= !empty($item['position']) ? $item['position'] : '' ?></div>
                                                    <div class="description font-medium"><?= !empty($item['content']) ? $item['content'] : '' ?></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="arrow-button">
                    <div class="button-prev"></div>
                    <div class="button-next"></div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>