<?php
$title = get_sub_field('title');
$content = get_sub_field('content');
$album = get_sub_field('album');
?>
<section class="about-11 section xl:py-20">
    <div class="container">
        <div class="wrap-top-nav">
            <h2 class="block-title"><?= !empty($title) ? $title : '' ?></h2>
            <div class="arrow-button close-arrow">
                <div class="button-prev"></div>
                <div class="button-next"></div>
            </div>
        </div>
        <div class="description font-medium text-justify mt-10"><?= !empty($content) ? $content : '' ?></div>
        <div class="wrapper mt-10">
            <div class="swiper">
                <div class="swiper-wrapper">
                    <?php if (!empty($album)) : ?>
                        <?php foreach ($album as $key => $item) : ?>
                            <div class="swiper-slide dynamic-slide">
                                <div class="item zoom-img">
                                    <div class="img"><a class="ratio-[293/440] rounded-4 bg-neutral-100">
                                            <?php if (!empty($item['gallery'])) : ?>
                                                <?php foreach ($item['gallery'] as $keyImage => $image) : ?>
                                                    <img class="lozad <?= $keyImage != 0 ? 'hidden' : '' ?>" data-fancybox="gallery-<?= $key ?>" data-src="<?= $image['url'] ?>" alt="<?= $image['alt'] ?? 'image' ?>" />
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </a>
                                    </div>
                                    <div class="content absolute bottom-0 w-full left-0 z-1 flex items-end p-6 pointer-events-none">
                                        <div class="title subheader-20 font-bold text-white w-full"><?= !empty($item['name']) ? $item['name'] : '' ?></div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>