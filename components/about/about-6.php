<?php
$image = get_sub_field('image');
$title = get_sub_field('title');
$logo = get_sub_field('logo');
$description = get_sub_field('description');
$subtitle = get_sub_field('subtitle');
$text_bg = get_sub_field('text_bg');
$core_values = get_sub_field('core_values');
?>
<section class="about-6 scroll-section  section xl:pb-20 xl:pt-[90px]" data-title="<?= !empty($title) ? $title : '' ?>">
    <div class="container">
        <div class="grid lg:grid-cols-2 base-gap lg:gap-0 items-center">
            <div class="col-left lg:pr-10 xl:pr-20">
                <?php if ($image) : ?>
                    <div class="img -lg:max-w-[80%] mx-auto"><a class="ratio-[1/1] ratio-contain"><img class="lozad" data-src="<?= $image['url'] ?>" alt="<?= $image['alt'] ?? 'image' ?>" /></a>
                    </div>
                <?php endif; ?>
            </div>
            <div class="col-right lg:pl-10 xl:pl-20">
                <div class="wrapper xl:rem:max-w-[477px] lg:text-left text-center">
                    <h2 class="block-title"><?= !empty($title) ? $title : '' ?></h2>
                    <?php if ($logo) : ?>
                        <div class="logo rem:max-w-[379px] mt-4 lg:mt-6 w-full lg:ml-unset mx-auto"><a class="ratio-[72/379] ratio-contain"><img class="lozad" data-src="<?= $logo['url'] ?>" alt="<?= $logo['alt'] ?? 'image' ?>" /></a>
                        </div>
                    <?php endif; ?>
                    <div class="description subheader-20 mt-4 lg:mt-14"><?= !empty($description) ? $description : '' ?></div>
                    <div class="sub-title subheader-20 font-bold text-black mt-4 lg:mt-13"><?= !empty($subtitle) ? $subtitle : '' ?></div>
                    <div class="quote bg-primary-2 rounded-2 mt-5 w-fit py-3 px-5 text-white font-bold subheader-20 lg:ml-unset mx-auto"><?= !empty($text_bg) ? $text_bg : '' ?></div>
                </div>
            </div>
        </div>
        <div class="list grid sm:grid-cols-2 lg:grid-cols-4 mt-25 base-gap">
            <?php if ($core_values) : ?>
                <?php foreach ($core_values as $item) : ?>
                    <div class="item space-y-5 bg-primary-1/5 hover:bg-neutral-50 transition-all duration-300 rounded-4 py-10 px-5">
                        <div class="icon w-16 rounded-1 bg-primary-1 [&amp;_img]:p-[19%]">
                            <?php if ($item['icon']) : ?>
                                <a class="ratio-[1/1] ratio-contain"><img class="lozad" data-src="<?= $item['icon']['url'] ?>" alt="<?= $item['icon']['alt'] ?? 'image' ?>" /></a>
                            <?php endif; ?>
                        </div>
                        <div class="title subheader-20 font-bold text-primary-1"><?= !empty($item['title']) ? $item['title'] : '' ?></div>
                        <div class="ctn text-neutral-800"><?= !empty($item['description']) ? $item['description'] : '' ?></div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</section>