<?php
$post_id = $args['post_id'];

$post_title = get_the_title($post_id);
$post_thumbnail = get_the_post_thumbnail_url($post_id, 'full');
$city_id = get_post_meta($post_id, '_office_city', true);
$district_id = get_post_meta($post_id, '_office_district', true);
$ward_id = get_post_meta($post_id, '_office_ward', true);
$street_id = get_post_meta($post_id, '_office_street', true);
$house_number = get_post_meta($post_id, '_office_house_number', true);


$full_address = '';
if (!empty($house_number)) {
  $full_address .= $house_number;
}
if (!empty($street_id)) {
  $full_address .= (!empty($full_address) ? ' - ' : '') . get_the_title($street_id);
}
if (!empty($ward_id)) {
  $full_address .= (!empty($full_address) ? ' - ' : '') . get_the_title($ward_id);
}
if (!empty($district_id)) {
  $full_address .= (!empty($full_address) ? ' - ' : '') . get_the_title($district_id);
}
if (!empty($city_id)) {
  $full_address .= (!empty($full_address) ? ' - ' : '') . get_the_title($city_id);
}

if (get_post_type($post_id) == 'office-rent-full') {
  $area = get_field('dynamic_field_dich_vu_tron_goi_phong-lam-viec-rieng', $post_id);
} else {
  $area = get_field('dynamic_field_thong_tin_dien_tich_va_gia_thue_dien-tich-trong', $post_id);
}


$hangtoanha = get_field('dynamic_field_thong_so_toa_nha_hang-toa-nha', $post_id);
$price_product = get_field('price_product', $post_id);

if ($hangtoanha) {
  $hangtoanha = get_the_title($hangtoanha);
}else{
  $hangtoanha = get_field('dynamic_field_ts_toa_nha_tron_goi_hang-toa-nha', $post_id);
  $hangtoanha = get_the_title($hangtoanha);
}

?>

<div class="bpd-2 zoom-img relative border rounded-4 overflow-hidden bg-white border-neutral-100 flex flex-col h-full ">
  <div class="img"><a href="<?php echo get_the_permalink($post_id); ?>" class="ratio-[208/320] bg-neutral-100"><img class="lozad" data-src="<?php echo $post_thumbnail; ?>" /></a>
  </div>
  <div class="content px-4 pt-4 pb-6 flex-1 h-full flex flex-col">
    <div class="wrapper px-1 flex-1 h-full">
      <div class="flex items-center justify-between gap-3 py-1">
        <time class="body-14 text-primary-1"><?php echo get_the_date('d.m.Y', $post_id); ?></time>
        <?php
        if (is_post_favorite($post_id)) {
          $class = 'is-favorite';
        } else {
          $class = '';
        }
        ?>
        <div class="favorite <?php echo $class; ?>" data-id="<?php echo $post_id; ?>"><i class="fa-light fa-heart"></i></div>
      </div>
      <div class="title rem:mt-[5px] font-bold text-black uppercase"><a href="<?php echo get_the_permalink($post_id); ?>" class="line-clamp-1"><?php echo $post_title; ?></a></div>
      <div class="infos body-14 text-neutral-500 space-y-3 mt-2.5">
        <?php 
        if(!empty($full_address)){
        ?>
        <div class="info flex items-center gap-2.5"><i class="fa-light fa-location-dot"></i>
          <div class="ctn line-clamp-1 flex-1"><span><?php echo $full_address; ?></span></div>
        </div>
        <?php
        }
        ?>
        <?php
        if(!empty($area)){
        ?>
        <div class="info flex items-center gap-2.5"><i class="fa-light fa-draw-square"></i>
          <div class="ctn line-clamp-1 flex-1"><span><?php echo $area; ?></span></div>
        </div>
        <?php 
        }
        ?>
        <?php
        if(!empty($hangtoanha)){
        ?>
        <div class="info flex items-center gap-2.5"><i class="fa-light fa-building"></i>
          <div class="ctn line-clamp-1 flex-1"><span><?php echo $hangtoanha; ?></span></div>
        </div>
        <?php
        }
        ?>
      </div>
    </div>
    <div class="buttons flex justify-between mt-2.5">
      <a class="btn btn-light btn-primary ">$<?php echo (!empty($price_product) ? $price_product : '0'); ?>++/m2</a>
      <?php
      if (is_post_in_compare($post_id)) {
      ?>
        <a class="btn btn-border-gray btn-primary office-compare" data-id="<?php echo $post_id; ?>"><i class="fa-solid fa-trash-can"></i><?php echo __('Xóa', 'canhcamtheme'); ?></a>
      <?php
      } else {
      ?>
        <a class="btn btn-border-gray btn-primary office-compare" data-id="<?php echo $post_id; ?>"><i class="fa-solid fa-circle-plus"></i><?php echo __('So sánh', 'canhcamtheme'); ?></a>
      <?php
      }

      if (is_page_template('templates/Account-Favorites.php')) {
      ?>
        <a class="btn btn-border-gray btn-primary col-span-full office-add-to-cart" data-id="<?php echo $post_id; ?>"><i class="fa-solid fa-cart-plus"></i><?php echo __('Thêm vào giỏ hàng', 'canhcamtheme'); ?></a>
      <?php
      } elseif (is_page_template('templates/Account-Cart.php')) {
      ?>
        <a class="btn btn-border-gray btn-primary col-span-full office-remove-cart" data-id="<?php echo $post_id; ?>"><i class="fa-solid fa-trash-can"></i><?php echo __('Xóa sản phẩm', 'canhcamtheme'); ?></a>
      <?php
      }
      ?>

    </div>
  </div>
</div>