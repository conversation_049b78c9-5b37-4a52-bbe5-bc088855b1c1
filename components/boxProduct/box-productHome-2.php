<?php
$post_id = $args['post_id'];

$post_title = get_the_title($post_id);
$post_thumbnail = get_the_post_thumbnail_url($post_id, 'full');
$city_id = get_post_meta($post_id, '_office_city', true);
$district_id = get_post_meta($post_id, '_office_district', true);
$ward_id = get_post_meta($post_id, '_office_ward', true);
$street_id = get_post_meta($post_id, '_office_street', true);
$house_number = get_post_meta($post_id, '_office_house_number', true);


$full_address = '';
if (!empty($house_number)) {
    $full_address .= $house_number;
}
if (!empty($street_id)) {
    $full_address .= (!empty($full_address) ? ' - ' : '') . get_the_title($street_id);
}
if (!empty($ward_id)) {
    $full_address .= (!empty($full_address) ? ' - ' : '') . get_the_title($ward_id);
}
if (!empty($district_id)) {
    $full_address .= (!empty($full_address) ? ' - ' : '') . get_the_title($district_id);
}
if (!empty($city_id)) {
    $full_address .= (!empty($full_address) ? ' - ' : '') . get_the_title($city_id);
}

$area = get_field('dynamic_field_dich_vu_tron_goi_phong-lam-viec-rieng', $post_id);
$hangtoanha = get_field('dynamic_field_thong_so_toa_nha_hang-toa-nha', $post_id);
$price_product = get_field('price_product', $post_id);

if ($price_product) {
    $price_product = $price_product;
} else {
    $price_product = 0;
}

$chung_chi = get_field('chung_chi', $post_id);
if ($chung_chi) {
    $thumbnail_chung_chi_id = get_post_thumbnail_id($chung_chi);
}

if ($hangtoanha) {
    $hangtoanha = get_the_title($hangtoanha);
}



?>

<div class="bpd-1 zoom-img relative text-white rounded-4 overflow-hidden undefined">
    <div class="img"><a class="ratio-[600/400] bg-neutral-100"><img class="lozad" data-src="<?php echo (has_post_thumbnail($post_id)) ? get_the_post_thumbnail_url($post_id, 'full') : '' ?>" alt="<?php echo $post_title; ?>" /></a>
    </div>
    <div class="content absolute inset-0 flex items-end z-1">
        <div class="wrapper p-6 relative w-full">
            <?php if ($thumbnail_chung_chi_id) : ?>
                <div class="icon w-16 border-2 border-white rounded-full overflow-hidden bg-neutral-200">

                    <a class="ratio-[1/1]">
                        <?php echo custom_lozad_image($thumbnail_chung_chi_id, 'full') ?>
                    </a>
                </div>

            <?php endif; ?>
            <div class="title subheader-24 font-bold mt-3"><a class="line-clamp-2" href="<?php echo get_the_permalink($post_id); ?>"><?php echo $post_title; ?></a></div>
            <div class="first-info">
                <div class="location mt-1 line-clamp-1"><?php echo $full_address; ?></div>
            </div>
            <div class="second-info mt-3 hidden">
                <div class="info flex gap-2.5 rem:py-2.5 px-2 border-b border-white/40 last:border-b-0 items-center first:border-t"><i class="fa-regular fa-location-dot"></i><span><?php echo $full_address; ?></span></div>
                <div class="info flex gap-2.5 rem:py-2.5 px-2 border-b border-white/40 last:border-b-0 items-center first:border-t"><i class="fa-light fa-draw-square"></i> <span><?php echo $area; ?></span></div>
                <div class="info flex gap-2.5 rem:py-2.5 px-2 border-b border-white/40 last:border-b-0 items-center first:border-t"><i class="fa-light fa-building"></i> <span><?php echo $hangtoanha; ?></span></div>
            </div><a class="btn btn-light mt-3 btn-primary " href="<?php echo get_the_permalink($post_id); ?>">$<?php echo $price_product; ?>++/m2</a>
        </div>
    </div>
</div>