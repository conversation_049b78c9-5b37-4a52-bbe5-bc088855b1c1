<?php
$chu_de = !empty($args['chu_de']) ? $args['chu_de'] : '';
$district_id = get_field('district_id', get_the_ID());

$officebyDistrict = new WP_Query(array(
    'post_type' => $chu_de,
    'posts_per_page' => -1,
    'post_status' => 'publish',
    'meta_key' => '_office_district',
    'meta_value' => $district_id,
));
?>
<div class="product-cat-1 zoom-img relative text-white rounded-4 overflow-hidden item">
    <div class="img"><a class="ratio-[1/1]" href="<?php echo get_the_permalink(get_the_ID()); ?>"><img class="lozad" data-src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>" /></a>
    </div>
    <div class="content absolute bottom-0 left-0 w-full flex items-end z-1 pointer-events-none">
        <div class="wrapper w-full p-4">
            <div class="title sm:body-18 lg:subheader-20 font-bold"><span class="line-clamp-2" a href="<?php echo get_the_permalink(get_the_ID()); ?>"><?php echo get_the_title($district_id); ?></span></div>
            <div class="ctn line-clamp-1 mt-2 sm:body-16 body-14"><?php echo $officebyDistrict->found_posts; ?><?php echo __('+ tòa nhà', 'canhcamtheme'); ?></div>
        </div>
    </div>
</div>