<?php
$title = get_sub_field('title');
$description = get_sub_field('description');
$tabs = get_sub_field('tabs');
?>

<section class="home-item-animation home-2 section xl:py-20" tab-wrapper="parent">
    <div class="container">
        <div class="wrap-top-nav">
            <h2 class="block-title"><?php echo !empty($title) ? $title : ''; ?></h2>
            <nav class="primary-nav">
                <ul>
                    <?php
                    if (!empty($tabs)) :
                        foreach ($tabs as $key => $tab) :
                    ?>
                            <li tab-item="parent" tab-item-value="<?php echo $key; ?>"><a><?php echo $tab['title']; ?></a></li>
                    <?php
                        endforeach;
                    endif;
                    ?>
                </ul>
            </nav>
        </div>
        <div class="description font-medium mt-5"><?php echo !empty($description) ? $description : ''; ?></div>
        <div class="tab-content mt-10">
            <?php
            if (!empty($tabs)) :
                foreach ($tabs as $key => $tab) :


            ?>
                    <div class="tab-content-item group" tab-content="parent" tab-content-value="<?php echo $key; ?>">
                        <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                            <?php
                            if (!empty($tab['locations'])):
                                $count_post = 0;
                                foreach ($tab['locations'] as $key => $location):
                                    $query_page = get_location_posts($location, 'office-rent');
                                    if ($query_page->have_posts()):
                                        while ($query_page->have_posts()): $query_page->the_post();
                                            get_template_part('components/boxProduct/box-district', '', array(
                                                'chu_de' => 'office-rent',
                                            ));
                                            $count_post++;
                                        endwhile;
                                        wp_reset_postdata();
                                    endif;
                                endforeach;
                                if ($count_post == 0) {
                                    echo '<div class="col-span-full">' . __('Dữ liệu đang cập nhật', 'canhcam-theme') . '</div>';
                                } ?>
                            <?php
                            else:
                                echo '<div class="col-span-full">' . __('Dữ liệu đang cập nhật', 'canhcam-theme') . '</div>';
                            endif;
                            ?>
                        </div>
                        <?php
                        if (!empty($tab['see_more'])):
                        ?>
                            <a class="btn mx-auto mt-10 btn-primary " href="<?php echo $tab['see_more']['url']; ?>"><?php echo $tab['see_more']['title']; ?><i class="fa-solid fa-right-long"></i></a>
                        <?php
                        endif;
                        ?>
                    </div>
            <?php
                endforeach;
            endif;
            ?>
        </div>
    </div>
</section>