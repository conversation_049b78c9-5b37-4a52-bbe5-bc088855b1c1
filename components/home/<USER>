<?php
$title = get_sub_field('title');
$see_more = get_sub_field('see_more');
$posts = get_sub_field('posts');


?>
<section class="home-12 section xl:py-20 bg-primary-1/5">
    <div class="container">
        <div class="wrap-top-nav">
            <h2 class="block-title"><?php echo !empty($title) ? $title : ''; ?></h2>
            <?php
            if (!empty($see_more)):
            ?>
                <a class=" view-link-long " href="<?php echo !empty($see_more['url']) ? $see_more['url'] : ''; ?>" target="<?php echo !empty($see_more['target']) ? $see_more['target'] : ''; ?>"><span><?php echo !empty($see_more['title']) ? $see_more['title'] : ''; ?></span><i class="fa-regular fa-angle-right"></i></a>
            <?php
            endif;
            ?>
        </div>
        <div class="grid lg:grid-cols-2 base-gap 2xl:gap-[120px] mt-10">
            <?php
            if (!empty($posts)) :
                foreach ($posts as $key => $post) :
                    setup_postdata($post);
            ?>
                    <?php if ($key === 0) : ?>
                        <?= get_template_part('/components/boxNews/boxNews-big', '', ['id' => get_the_ID()]) ?>
                    <?php endif; ?>
                    <?php if ($key === 1) : ?>
                        <div class="small space-y-4">
                        <?php endif; ?>
                        <?php if ($key >= 1) : ?>
                            <?= get_template_part('/components/boxNews/boxNews-small', '', ['id' => get_the_ID()]) ?>
                        <?php endif; ?>
                        <?php if ($key === count($posts) - 1) : ?>
                        </div>
                    <?php endif; ?>
            <?php endforeach;
            endif;
            wp_reset_postdata(); ?>
        </div>
    </div>
</section>