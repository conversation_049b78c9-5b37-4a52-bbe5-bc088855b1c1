<?php
$title = get_sub_field('title');
$description = get_sub_field('description');
$button = get_sub_field('button');
$logos = get_sub_field('logos');
$images = get_sub_field('images');
$numbers = get_sub_field('numbers');
?>
<section class="home-6 section xl:py-20 overflow-hidden">
    <div class="container">
        <h2 class="block-title"><?php echo !empty($title) ? $title : ''; ?></h2>
        <div class="grid grid-cols-2 xl:gap-[calc(120/1400*100%)] mt-10 base-gap">
            <div class="col-left lg:col-span-1 col-span-full">
                <div class="description space-y-5 text-justify">
                    <?php echo !empty($description) ? $description : ''; ?>
                </div>
                <?php
                if (!empty($button)):
                ?>
                    <a class="btn mt-10 btn-primary " href="<?php echo $button['url']; ?>"><?php echo $button['title']; ?><i class="fa-solid fa-right-long"></i></a>
                <?php
                endif;
                ?>
                <div class="swiper-column-auto auto-4-column allow-touchMove auto-play swiper-loop rem:mt-[60px]" data-time="3500">
                    <?php
                    if (!empty($logos)):
                    ?>
                        <div class="swiper">
                            <div class="swiper-wrapper">
                                <?php
                                foreach ($logos as $logo):
                                ?>
                                    <div class="swiper-slide">
                                        <div class="img zoom-img rounded-2 border border-neutral-100">
                                            <a class="ratio-[96/148] rounded-2 ratio-contain">
                                                <?= custom_lozad_image($logo['ID']) ?>
                                            </a>
                                        </div>
                                    </div>
                                <?php
                                endforeach;
                                ?>
                            </div>
                        </div>
                    <?php
                    endif;
                    ?>
                </div>
            </div>
            <div class="col-right lg:pr-10 lg:col-span-1 col-span-full">
                <?php
                if (!empty($images)):
                ?>
                    <div class="swiper">
                        <div class="swiper-wrapper">
                            <?php
                            foreach ($images as $image):
                            ?>
                                <div class="swiper-slide">
                                    <div class="img zoom-img"><a class="ratio-[450/600] rounded-4 bg-neutral-50">
                                            <?= custom_lozad_image($image['ID']) ?>
                                        </a>
                                    </div>
                                </div>
                            <?php
                            endforeach;
                            ?>
                        </div>
                    </div>
                <?php
                endif;
                ?>
            </div>
        </div>
        <div class="list-number flex flex-wrap -mx-5 rem:mt-[120px] gap-y-5">
            <?php
            if (!empty($numbers)):
                foreach ($numbers as $number):
            ?>
                    <div class="item w-1/2 sm:w-1/3 lg:w-1/5 flex-auto px-5">
                        <div class="wrapper pb-6 border-b border-primary-1 w-full h-full">
                            <div class="value">
                                <div class="counter after:content-[attr(data-suffix)] rem:text-[58px] text-primary-1 font-bold leading-normal" data-count="<?php echo !empty($number['number']) ? $number['number'] : ''; ?>" data-suffix="<?php echo !empty($number['suffix']) ? $number['suffix'] : ''; ?>"></div>
                                <div class="content font-medium mt-1 text-black"><?php echo !empty($number['content']) ? $number['content'] : ''; ?></div>
                            </div>
                        </div>
                    </div>
            <?php
                endforeach;
            endif;
            ?>
        </div>
    </div>
</section>