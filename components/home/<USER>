<?php

$arg_city = array(
    'post_type' => 'city',
    'posts_per_page' => -1,
);
$cities = new WP_Query($arg_city);
// Building class
$arg_building_class = array(
    'post_type' => 'building-class',
    'posts_per_page' => -1,
);
$building_classes = new WP_Query($arg_building_class);

// Area
$arg_area = array(
    'post_type' => 'area',
    'posts_per_page' => -1,
);
$areas = new WP_Query($arg_area);


// building-orientation
$arg_building_orientation = array(
    'post_type' => 'building-orientation',
    'posts_per_page' => -1,
);
$building_orientations = new WP_Query($arg_building_orientation);

global $post;
$background_banner = get_field('background_banner', $post->ID);
$title_banner = get_field('title_banner', $post->ID);

?>

<div class="main-banner relative z-5 bg-neutral-500 flex items-end">
    <div class="wrapper">
        <div class="swiper">
            <div class="swiper-wrapper">
                <?php
                if (!empty($background_banner)) :
                    foreach ($background_banner as $item) :
                ?>
                        <div class="swiper-slide">
                            <div class="img absolute inset-0 [&amp;_img]:size-full [&amp;_img]:object-cover">
                                <?php if (!empty($item['url'])) : ?>
                                    <a>
                                        <?= custom_lozad_image($item['ID']) ?>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                <?php
                    endforeach;
                endif;
                ?>

            </div>
        </div>
        <div class="container container-arrow absolute left-1/2 -xl:bottom-0 -translate-x-1/2 xl:top-1/2 xl:-translate-y-1/2 z-5 pointer-events-none">
            <div class="swiper-pagination xl:!hidden"></div>
        </div>
    </div>

    <div class="container container-content relative z-1 w-full pb-12">
        <div class="wrapper clamp:max-w-[1004px] w-full">
            <div class="title header-36 text-white uppercase font-heading"><?php echo !empty($title_banner) ? $title_banner : ''; ?></div>
            <div class="filter-wrapper mt-6">
                <?php get_template_part('components/filter/filter'); ?>
            </div>
        </div>
    </div>

    <?php echo display_rating_html(get_the_ID(), true); ?>

</div>