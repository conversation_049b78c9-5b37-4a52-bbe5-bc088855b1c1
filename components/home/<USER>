<?php
$title = get_sub_field('title');
$group = get_sub_field('group');
$small_image = get_sub_field('small_image');
$large_image = get_sub_field('large_image');
?>
<section class="home-item-animation home-7 relative overflow-hidden bg-primary-1 text-white">
    <div class="background-image mix-blend-multiply rem:blur-[20px] pointer-events-none"><img class="lozad" data-src="/wp-content/uploads/2025/05/i-7-bg.png" />
    </div>
    <div class="container relative z-1">
        <div class="grid grid-cols-12 base-gap">
            <div class="col-left col-span-full lg:col-span-5 section xl:py-20">
                <h2 class="font-normal header-48 font-heading uppercase text-white"><?php echo !empty($title) ? $title : ''; ?></h2>
                <div class="list module-toggle mt-11" data-open-atleast-one="true" data-open-first-item="true">
                    <?php
                    if (!empty($group)):
                        foreach ($group as $key => $item):
                    ?>
                            <div class="item group border-b border-secondary-5/80">
                                <div class="top flex items-center justify-between gap-3 text-secondary-5 transition-all duration-500 group-[.active]:text-primary-2 py-8 group-[.active]:pb-4 cursor-pointer">
                                    <div class="title subheader-24 font-bold"><?php echo $key < 10 ? '0' . $key + 1 : $key + 1; ?> . <?php echo !empty($item['title']) ? $item['title'] : ''; ?></div><i class="fa-solid fa-angle-down group-[.active]:rotate-180 transition-all duration-500"></i>
                                </div>
                                <div class="bottom hidden pb-8">
                                    <div class="sub-title body-18 font-bold"><?php echo !empty($item['subtitle']) ? $item['subtitle'] : ''; ?></div>
                                    <div class="content space-y-4 mt-4">
                                        <?php echo !empty($item['content']) ? $item['content'] : ''; ?>
                                    </div>
                                </div>
                            </div>
                    <?php
                        endforeach;
                    endif;
                    ?>

                </div>
            </div>
            <div class="col-empty lg:block hidden lg:col-span-1"></div>
            <div class="col-right col-span-full lg:col-span-6 relative bg-neutral-50">
                <?php
                if (!empty($large_image)):
                ?>
                    <div class="img h-full"><a class="ratio-[800/680] h-full">
                            <?= custom_lozad_image($large_image['ID']) ?>
                        </a>
                    </div>
                <?php
                endif;
                ?>
                <?php
                if (!empty($small_image)):
                ?>
                    <div class="small-img top-[calc(80/800*100%)] left-[calc(-80/680*100%)] w-[calc(240/680*100%)] absolute z-1 rounded-4 [&amp;_img]:rounded-4"><span class="ratio-[180/240]">
                            <?= custom_lozad_image($small_image['ID']) ?>
                        </span>
                    </div>
                <?php
                endif;
                ?>
            </div>
        </div>
</section>