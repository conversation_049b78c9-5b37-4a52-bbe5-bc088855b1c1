<?php
$background = get_sub_field('background');
$title = get_sub_field('title');
$description = get_sub_field('description');
$list = get_sub_field('list');
?>

<section class="home-item-animation home-11 overflow-hidden">
    <div class="top bg-primary-1 section xl:pt-20 relative">

        <div class="background-image">
            <?php
            if (!empty($background)):
            ?>
                <?= custom_lozad_image($background['ID']) ?>
            <?php
            endif;
            ?>
        </div>
        <div class="container relative z-1">
            <div class="wrapper xl:rem:max-w-[700px] w-full ">
                <div class="block-title text-white"><?php echo !empty($title) ? $title : ''; ?></div>
                <div class="description text-white mt-10">
                    <?php echo !empty($description) ? $description : ''; ?>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom relative z-1 section pt-0 xl:pb-20 group">
        <div class="relative">
            <div class="hover-helper absolute right-5 rem:top-[98px] w-[calc(104/1920*100%)] shadow-medium rounded-full transition-all opacity-100 pointer--none group-hover:opacity-0 z-3"><a class="ratio-[44/104] image-svg image-absolute">
                    <img class="lozad" data-src="<?php bloginfo("template_directory"); ?>/img/home/<USER>" />
                </a>
            </div>
        </div>
        <div class="container">
            <div class="swiper-column-auto relative auto-3-column visible-slide pt-10 auto-play allow-touchMove">
                <div class="swiper">
                    <div class="swiper-wrapper">
                        <?php
                        if (!empty($list)):
                            foreach ($list as $item):
                        ?>
                                <div class="swiper-slide">
                                    <div class="item rounded-4 h-full bg-white p-6 sm:py-9 sm:px-10 flex flex-col">
                                        <div class="quote-icon w-7"><span class="ratio-[18/28]"><img class="lozad" data-src="<?php bloginfo("template_directory"); ?>/img/home/<USER>" /></span>
                                        </div>
                                        <div class="content text-neutral-800 mt-5 flex-1 xl:pr-1 pb-5">
                                            <?php echo !empty($item['content']) ? $item['content'] : ''; ?>
                                        </div>
                                        <div class="rating mt-3 subheader-20 text-[#F6C100] flex gap-2">
                                            <?php
                                            for ($i = 0; $i < $item['number']; $i++):
                                            ?>
                                                <i class="fa-solid fa-star"></i>
                                            <?php
                                            endfor;
                                            ?>
                                        </div>
                                        <div class="author flex items-center gap-4 mt-4">
                                            <div class="avatar w-16 sm:w-20"><span class="ratio-[1/1] rounded-full">
                                                    <?php
                                                    if (!empty($item['image'])):
                                                    ?>
                                                        <?= custom_lozad_image($item['image']['ID']) ?>
                                                    <?php
                                                    endif;
                                                    ?>
                                                </span>
                                            </div>
                                            <div class="info">
                                                <div class="name body-16 sm:subheader-20 font-bold text-primary-1">
                                                    <?php echo !empty($item['name']) ? $item['name'] : ''; ?>
                                                </div>
                                                <div class="position mt-1 label-12 sm:body-14 font-medium">
                                                    <?php echo !empty($item['position']) ? $item['position'] : ''; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        <?php
                            endforeach;
                        endif;
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>