<?php
$title = get_sub_field('title');
$description = get_sub_field('description');
$tabs = get_sub_field('tabs');
?>
<section class="home-item-animation home-5 section xl:py-20 bg-primary-1/5" tab-wrapper="parent">
    <div class="container">
        <div class="wrap-top-nav">
            <h2 class="block-title"><?php echo !empty($title) ? $title : ''; ?></h2>
            <nav class="primary-nav">
                <ul>
                    <?php
                    if (!empty($tabs)):
                        foreach ($tabs as $key => $tab):
                    ?>
                            <li tab-item="parent" tab-item-value="<?php echo $key; ?>"><a><?php echo $tab['title']; ?></a></li>
                    <?php
                        endforeach;
                    endif;
                    ?>

                </ul>
            </nav>
        </div>
        <div class="description font-medium mt-5"><?php echo !empty($description) ? $description : ''; ?></div>
        <div class="tab-content mt-10">
            <?php
            if (!empty($tabs)) :
                foreach ($tabs as $key => $tab) :


            ?>
                    <div class="tab-content-item group" tab-content="parent" tab-content-value="<?php echo $key; ?>">
                        <div class="grid lg:grid-cols-2 gap-6 mt-10">
                            <?php if (!empty($tab['locations']) && !empty(get_location_posts($tab['locations'][0], 'real-estate'))): ?>
                                <?php
                                $id_location_first = 0;
                                if (!empty($tab['locations'])):

                                    foreach ($tab['locations'] as $key => $location):
                                        $query_page = get_location_posts($location, 'real-estate');
                                        while ($query_page->have_posts()): $query_page->the_post();
                                            if ($id_location_first == 0) {
                                                get_template_part('components/boxProduct/box-district', '', array(
                                                    'chu_de' => 'real-estate',
                                                ));
                                                $id_location_first = get_the_ID();
                                                break;
                                            }

                                        endwhile;
                                        wp_reset_postdata();
                                    endforeach;
                                endif;
                                if ($id_location_first == 0) {
                                    echo '<div class="col-span-full">' . __('Dữ liệu đang cập nhật', 'canhcam-theme') . '</div>';
                                }
                                ?>
                                <div class="grid grid-cols-2 gap-6">
                                    <?php
                                    if (!empty($tab['locations'])):
                                        foreach ($tab['locations'] as $key => $location):
                                            $query_page = get_location_posts($location, 'real-estate');
                                            while ($query_page->have_posts()): $query_page->the_post();
                                                if ($id_location_first != get_the_ID()) {
                                                    get_template_part('components/boxProduct/box-district', '', array(
                                                        'chu_de' => 'real-estate',
                                                    ));
                                                }
                                            endwhile;
                                            wp_reset_postdata();
                                        endforeach;
                                    endif;
                                    ?>
                                </div>
                            <?php else: ?>
                                <div class="col-span-full"><?php _e('Dữ liệu đang cập nhật', 'canhcam-theme'); ?></div>
                            <?php endif; ?>

                        </div>
                        <?php
                        if (!empty($tab['see_more'])):
                        ?>
                            <a class="btn mx-auto mt-10 btn-primary " href="<?php echo $tab['see_more']['url']; ?>"><?php echo $tab['see_more']['title']; ?><i class="fa-solid fa-right-long"></i></a>
                        <?php
                        endif;
                        ?>
                    </div>
            <?php
                endforeach;
            endif;
            ?>
        </div>
    </div>
</section>