<?php
$title = get_sub_field('title');
$description = get_sub_field('description');
$group_youtube = get_sub_field('group_youtube');
$list = get_sub_field('list');
?>

<section class="home-item-animation home-8 section xl:py-20 bg-primary-1/5 overflow-hidden">
    <div class="container">
        <div class="grid grid-cols-12 base-gap">
            <div class="col-left col-span-full lg:col-span-3 xl:-mr-10 flex flex-col justify-between">
                <div class="top flex-1">
                    <h2 class="block-title"><?php echo !empty($title) ? $title : ''; ?></h2>
                    <div class="description mt-6 text-black"><?php echo !empty($description) ? $description : ''; ?></div>
                </div>
                <div class="bottom flex items-center gap-4 mt-10">
                    <div class="icon w-12 shadow-light rounded-tl-4 rounded-br-4"><span class="ratio-[1/1]">
                            <?php
                            if (!empty($group_youtube['logo'])):
                            ?>
                                <?= custom_lozad_image($group_youtube['logo']['ID']) ?>
                            <?php
                            endif;
                            ?>
                        </span>
                    </div>
                    <div class="content">
                        <a class="title body-18 font-bold" href="<?php echo !empty($group_youtube['link']) ? $group_youtube['link'] : ''; ?>" target="_blank"><?php echo !empty($group_youtube['name']) ? $group_youtube['name'] : ''; ?></a>
                        <div class="sub-title body-14 text-primary-1"><?php echo !empty($group_youtube['address']) ? $group_youtube['address'] : ''; ?></div>
                    </div>
                </div>
            </div>
            <div class="col-right col-span-full lg:col-span-9 xl:pl-8">
                <div class="wrapper" stick-to-edge="right">
                    <div class="swiper-column-auto relative auto-4-column  auto-play allow-touchMove">
                        <div class="swiper">
                            <div class="swiper-wrapper">
                                <?php
                                if (!empty($list)):
                                    foreach ($list as $item):
                                ?>
                                        <div class="swiper-slide">
                                            <div class="item group">
                                                <div class="img xl:group-hover:-translate-y-4 transition-all ">
                                                    <a class="ratio-[304/180] rounded-2" href="<?php echo !empty($item['link']) ? $item['link'] : ''; ?>" target="_blank">
                                                        <?= custom_lozad_image($item['image']['ID']) ?>
                                                    </a>
                                                </div>
                                                <div class="content mt-2">
                                                    <a class="title font-medium transition-all  group-hover:text-primary-1 group-hover:underline " href="<?php echo !empty($item['link']) ? $item['link'] : ''; ?>" target="_blank"><?php echo !empty($item['title']) ? $item['title'] : ''; ?></a>
                                                </div>
                                            </div>
                                        </div>
                                <?php
                                    endforeach;
                                endif;
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>