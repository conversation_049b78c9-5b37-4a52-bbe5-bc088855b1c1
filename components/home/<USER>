<?php
$title = get_sub_field('title');
$tab_1 = get_sub_field('tab_1');
$tab_2 = get_sub_field('tab_2');
$tab_3 = get_sub_field('tab_3');
$tab_4 = get_sub_field('tab_4');
?>
<section class="home-1 section xl:py-20 bg-primary-1/5 overflow-hidden" tab-wrapper="parent">
    <div class="container">
        <div class="wrap-top-nav">
            <h2 class="block-title"><?php echo $title; ?></h2>
            <nav class="primary-nav">
                <ul>
                    <li tab-item="parent" tab-item-value="1"><a><?php echo $tab_1['title']; ?></a></li>
                    <li tab-item="parent" tab-item-value="2"><a><?php echo $tab_2['title']; ?></a></li>
                    <li tab-item="parent" tab-item-value="3"><a><?php echo $tab_3['title']; ?></a></li>
                    <li tab-item="parent" tab-item-value="4"><a><?php echo $tab_4['title']; ?></a></li>
                </ul>
            </nav>
        </div>
    </div>
    <div class="relative">
        <div class="container-fluid mt-10">
            <div class="tab-content">
                <div class="tab-content-item group" tab-content="parent" tab-content-value="1">
                    <div class="swiper-column-auto relative auto-3-column visible-slide allow-mouseWheel  allow-touchMove">
                        <?php
                        if (!empty($tab_1['items'])) :
                        ?>
                            <div class="swiper">
                                <div class="swiper-wrapper">
                                    <?php
                                    foreach ($tab_1['items'] as $post) {
                                        echo '<div class="swiper-slide">';

                                        get_template_part('components/boxProduct/box-productHome-1', null, array('post_id' => $post));
                                        echo '</div>';
                                        wp_reset_postdata();
                                    }
                                    ?>
                                </div>
                            </div>
                            <div class="swiper-scrollbar"></div>
                        <?php
                        endif;
                        ?>
                    </div>
                    <div class="hover-helper absolute right-5 top-[calc(104/600*100%)] w-[calc(104/1920*100%)] z-1 shadow-medium rounded-full transition-all opacity-100 pointer-events-none group-hover:opacity-0"><a class="ratio-[44/104] image-svg image-absolute">
                            <img class="lozad" data-src="<?php bloginfo("template_directory"); ?>/img/home/<USER>" />
                        </a>
                    </div>
                </div>
                <div class="tab-content-item group" tab-content="parent" tab-content-value="2">
                    <?php
                    if (!empty($tab_2['items'])) :
                    ?>
                        <div class="swiper-column-auto relative auto-3-column visible-slide allow-mouseWheel  allow-touchMove">
                            <div class="swiper">
                                <div class="swiper-wrapper">
                                    <?php
                                    foreach ($tab_2['items'] as $post) {
                                        echo '<div class="swiper-slide">';
                                        get_template_part('components/boxProduct/box-productHome-1', null, array('post_id' => $post));
                                        echo '</div>';
                                        wp_reset_postdata();
                                    }
                                    ?>
                                </div>
                            </div>
                            <div class="swiper-scrollbar"></div>
                        </div>
                    <?php
                    endif;
                    ?>
                    <div class="hover-helper absolute right-5 top-[calc(104/600*100%)] w-[calc(104/1920*100%)] z-1 shadow-medium rounded-full transition-all opacity-100 pointer-events-none group-hover:opacity-0"><a class="ratio-[44/104] image-svg image-absolute">
                            <img class="lozad" data-src="<?php bloginfo("template_directory"); ?>/img/home/<USER>" />
                        </a>
                    </div>
                </div>
                <div class="tab-content-item group" tab-content="parent" tab-content-value="3">
                    <div class="swiper-column-auto relative auto-3-column visible-slide allow-mouseWheel  allow-touchMove">
                        <?php
                        if (!empty($tab_3['items'])) :
                        ?>
                            <div class="swiper">
                                <div class="swiper-wrapper">
                                    <?php
                                    foreach ($tab_3['items'] as $post) {
                                        echo '<div class="swiper-slide">';
                                        get_template_part('components/boxProduct/box-productHome-1', null, array('post_id' => $post));
                                        echo '</div>';
                                        wp_reset_postdata();
                                    }
                                    ?>
                                </div>
                            </div>
                            <div class="swiper-scrollbar"></div>
                        <?php
                        endif;
                        ?>
                    </div>
                    <div class="hover-helper absolute right-5 top-[calc(104/600*100%)] w-[calc(104/1920*100%)] z-1 shadow-medium rounded-full transition-all opacity-100 pointer-events-none group-hover:opacity-0"><a class="ratio-[44/104] image-svg image-absolute">
                            <img class="lozad" data-src="<?php bloginfo("template_directory"); ?>/img/home/<USER>" />
                        </a>
                    </div>
                </div>
                <div class="tab-content-item group" tab-content="parent" tab-content-value="4">
                    <div class="swiper-column-auto relative auto-3-column visible-slide allow-mouseWheel  allow-touchMove">
                        <?php
                        if (!empty($tab_4['items'])) :
                        ?>
                            <div class="swiper">
                                <div class="swiper-wrapper">

                                    <?php
                                    foreach ($tab_4['items'] as $post) {
                                        echo '<div class="swiper-slide">';
                                        get_template_part('components/boxProduct/box-productHome-1', null, array('post_id' => $post));
                                        echo '</div>';
                                        wp_reset_postdata();
                                    }
                                    ?>

                                </div>
                            </div>
                            <div class="swiper-scrollbar"></div>
                        <?php
                        endif;
                        ?>
                    </div>
                    <div class="hover-helper absolute right-5 top-[calc(104/600*100%)] w-[calc(104/1920*100%)] z-1 shadow-medium rounded-full transition-all opacity-100 pointer-events-none group-hover:opacity-0"><a class="ratio-[44/104] image-svg image-absolute">
                            <img class="lozad" data-src="<?php bloginfo("template_directory"); ?>/img/home/<USER>" />
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>