<?php
$title = get_sub_field('title');
$logos = get_sub_field('logos');

?>
<section class="home-item-animation home-grid-item home-9 section xl:py-20">
    <div class="container">
        <div class="wrap-top-nav">
            <h2 class="block-title"><?php echo !empty($title) ? $title : ''; ?></h2>
            <div class="arrow-button close-arrow">
                <div class="button-prev"></div>
                <div class="button-next"></div>
            </div>
        </div>
        <div class="wrapper mt-10">
            <div class="swiper">
                <div class="swiper-wrapper">
                    <?php
                    if (!empty($logos)):
                        foreach ($logos as $logo):

                    ?>
                            <div class="swiper-slide dynamic-slide">
                                <div class="item">
                                    <div class="img zoom-img rounded-2 border border-neutral-100 bg-white transition-all hover:border-primary-1 [&amp;_img]:rounded-2">
                                        <a class="ratio-[112/200] ratio-contain">
                                            <?= custom_lozad_image($logo['ID']) ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                    <?php
                        endforeach;
                    endif;
                    ?>
                </div>
            </div>
        </div>
    </div>
</section>