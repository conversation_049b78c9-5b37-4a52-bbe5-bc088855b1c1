<?php
$title_footer_form = get_field('title_footer_form', 'option');
$group_box_icon_footer_form = get_field('group_box_icon_footer_form', 'option');
$group_number_footer_form = get_field('group_number_footer_form', 'option');
$short_code_footer_form = get_field('short_code_footer_form', 'option');
?>
<section class="home-item-animation contact-form section xl:py-20 overflow-hidden">
    <div class="container">
        <div class="block-title"><?php echo !empty($title_footer_form) ? $title_footer_form : ''; ?></div>
        <div class="swiper-column-auto relative auto-5-column mt-10  allow-touchMove">
            <?php if (!empty($group_box_icon_footer_form)) : ?>
                <!-- <div class="swiper">
                    <div class="swiper-wrapper">

                        <?php foreach ($group_box_icon_footer_form as $item) : ?>
                            <div class="swiper-slide">
                                <div class="item bg-white border border-neutral-100 py-6 px-5 rounded-4">
                                    <div class="icon w-16 mx-auto"><a class="ratio-[1/1] ratio-contain rounded-full bg-primary-1 [&amp;_img]:p-[18%]">
                                            <?php if (!empty($item['icon']['url'])) : ?>
                                                <img class="lozad" data-src="<?php echo $item['icon']['url']; ?>" alt="<?php echo !empty($item['icon']['alt']) ? $item['icon']['alt'] : 'icon'; ?>" />
                                            <?php endif; ?>
                                        </a>
                                    </div>
                                    <div class="title body-18 font-medium text-center mt-4"><?php echo !empty($item['content']) ? $item['content'] : ''; ?></div>
                                </div>
                            </div>
                        <?php endforeach; ?>

                    </div>
                </div>
                <div class="swiper-pagination"></div> -->
                <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4  xl:grid-cols-5 base-gap mt-10">
                    <?php foreach ($group_box_icon_footer_form as $item) : ?>
                        <div class="item bg-white border border-neutral-100 py-6 px-5 rounded-4">
                            <div class="icon w-16 mx-auto"><a class="ratio-[1/1] ratio-contain rounded-full bg-primary-1 [&amp;_img]:p-[18%]">
                                    <?php if (!empty($item['icon']['url'])) : ?>
                                        <img class="lozad" data-src="<?php echo $item['icon']['url']; ?>" alt="<?php echo !empty($item['icon']['alt']) ? $item['icon']['alt'] : 'icon'; ?>" />
                                    <?php endif; ?>
                                </a>
                            </div>
                            <div class="title body-14 sm:body-18 font-medium text-center mt-4"><?php echo !empty($item['content']) ? $item['content'] : ''; ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
        <div class="pb-8">
            <div class="py-10 grid grid-cols-12 base-gap mt-10">
                <div class="col-left col-span-full lg:col-span-7 text-primary-1 pt-2">
                    <?php if (!empty($group_number_footer_form)) : ?>
                        <div class="wrapper flex gap-x-11 -md:flex-col">
                            <div class="number">
                                <div class="value counter text-[5rem] lg:rem:text-[128px] leading-none font-black after:content-[attr(data-suffix)]" data-count="<?php echo !empty($group_number_footer_form['number']) ? $group_number_footer_form['number'] : ''; ?>" data-suffix="<?php echo !empty($group_number_footer_form['suffix']) ? $group_number_footer_form['suffix'] : ''; ?>">0</div>
                            </div>
                            <div class="content relative pt-2">
                                <div class="prefix rem:text-[48px] leading-none font-extralight absolute right-full top-0 mr-2">+</div>
                                <div class="title header-40 leading-[1.2] font-extrabold"><?php echo !empty($group_number_footer_form['title']) ? $group_number_footer_form['title'] : ''; ?></div>
                                <div class="ctn rem:text-[22px] leading-[1.27273] rem:tracking-[0.88px] mt-2"><?php echo !empty($group_number_footer_form['description']) ? $group_number_footer_form['description'] : ''; ?></div>
                            </div>
                        </div>
                        <?php if (!empty($group_number_footer_form['button'])) : ?>
                            <a class="btn mt-6 btn-secondary " href="<?php echo !empty($group_number_footer_form['button']['url']) ? $group_number_footer_form['button']['url'] : ''; ?>"><?php echo !empty($group_number_footer_form['button']['title']) ? $group_number_footer_form['button']['title'] : ''; ?><i class="fa-solid fa-angle-right"></i></a>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
                <div class="col-right col-span-full lg:col-span-5 xl:pl-3">
                    <?php if (!empty($short_code_footer_form)) : ?>
                        <?php echo do_shortcode($short_code_footer_form); ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>