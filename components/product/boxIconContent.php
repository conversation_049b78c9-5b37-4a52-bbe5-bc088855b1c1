<?php
$section_title = $args['section_title'];
$section_content = $args['section_content'];
$section_options = $args['section_options'];
$dynamic_field_name = $args['dynamic_field_name'];
$post_id = $args['post_id'];

?>

<div class="box-content scroll-section" data-title="<?php echo !empty($section_title) ? $section_title : '' ?>">
    <div class="title subheader-24 font-heading uppercase leading-[1.41667] text-primary-1"><?php echo !empty($section_title) ? $section_title : '' ?></div>
    <?php if (!empty($section_content)) : ?>
        <div class="description text-justify simple-prose mt-10">
            <?php echo !empty($section_content) ? $section_content : '' ?>
        </div>
    <?php endif; ?>
    <div class="box-list-item-icon">
        <div class="grid sm:grid-cols-2 mt-5 gap-y-0 base-gap xl:rem:gap-x-[60px]">
            <?php foreach ($section_options as $thong_so) :
                // Skip specific item keys
                if (in_array($thong_so['item_key'], ['dich-vu-ho-tro-it', 'dang-ky-dia-chi-kinh-doanh-nhanh-chong-de-dang'])) {
                    continue;
                }

                // Tạo key từ tên thông số
                $item_id = $thong_so['item_key'];
                $meta_key = 'dynamic_field_' . $dynamic_field_name . '_' . $item_id;
                if($item_id == 'ten-toa-nha') { 
                    $url_toa_nha = get_field('dynamic_field_' . $dynamic_field_name . '_' . 'url-toa-nha', $post_id);
                }
                // Lấy giá trị trực tiếp từ post meta
                $value = get_field($meta_key, $post_id);

                // Xử lý hiển thị cho các trường đặc biệt
                if ($thong_so['item_key'] == 'hang-toa-nha' && !empty($value) && is_numeric($value)) {
                    $post = get_post($value);
                    if ($post) {
                        $value = $post->post_title;
                    }
                }
                
                if ($thong_so['item_key'] == 'huong-toa-nha' && !empty($value)) {
                    if (is_array($value)) {
                        $titles = array();
                        foreach ($value as $post_id_value) {
                            if (is_numeric($post_id_value)) {
                                $post = get_post($post_id_value);
                                if ($post) {
                                    $titles[] = $post->post_title;
                                }
                            }
                        }
                        $value = !empty($titles) ? implode(', ', $titles) : $value;
                    } elseif (is_numeric($value)) {
                        $post = get_post($value);
                        if ($post) {
                            $value = $post->post_title;
                        }
                    }
                } 

                
                // Lấy icon từ options nếu có
                $icon = isset($thong_so['icon']) ? $thong_so['icon'] : '';
                
                // Xử lý icon nếu là ID
                if (!empty($icon) && is_numeric($icon)) {
                    $icon = wp_get_attachment_image_src($icon, 'full');
                }

                // Bỏ qua nếu không có giá trị
                if (empty($value) && $value !== '0') continue;
                ?>
                <div class="item flex gap-4 py-4 border-t border-neutral-100">
                    <?php if (!empty($icon)) : ?>
                        <div class="icon-box">
                            <?php if (is_array($icon)) : ?>
                                <img class="lozad" data-src="<?php echo $icon[0] ?? $icon['url'] ?>" alt="<?php echo !empty($icon['alt']) ? $icon['alt'] : $thong_so['ten_thong_so'] ?>">
                            <?php else : ?>
                                <img class="lozad" data-src="<?php echo $icon ?>" alt="<?php echo $thong_so['ten_thong_so'] ?>">
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    <div class="content">
                        <div class="title body-18 font-bold text-black"><?php echo $thong_so['ten_thong_so'] ?></div>
                        <div class="ctn mt-2 text-neutral-700">
                            <?php
                            if (is_array($value)) {
                                echo implode(', ', $value);
                            } else {
                                if ($thong_so['item_key'] == 'dien-tich-trong') {
                                    echo $value . (ICL_LANGUAGE_CODE == 'vi' ? ' m2' : ' sqm');
                                } elseif ($thong_so['item_key'] == 'so-cho-ngoi') {
                                    echo $value . ' ' . (ICL_LANGUAGE_CODE == 'vi' ? 'Nhân viên' : 'Workstation');
                                } elseif ($thong_so['item_key'] == 'ten-toa-nha') {
                                    if (!empty($url_toa_nha)) {
                                        echo '<a href="' . $url_toa_nha . '">' . $value . '</a>';
                                    } else {
                                        echo $value;
                                    }
                                } else {
                                    echo $value;
                                }
                            }
                            ?> 
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>