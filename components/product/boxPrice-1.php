<?php
$post_id = $args['post_id'];
$post_type = get_post_type($post_id);
$price_product = get_field('price_product', $post_id);
$price_management = get_field('price_management', $post_id);
$hotline = get_field('hotline', $post_id);
$area = get_field('dynamic_field_thong_tin_dien_tich_va_gia_thue_dien-tich-trong', $post_id);
$vat = get_field('dynamic_field_thong_tin_dien_tich_va_gia_thue_vat', $post_id);
$total_price = (floatval($price_product) + floatval($price_management)) * 1.1;

$tieu_de_tu_van = get_field('tieu_de_tu_van', 'option');
$button_tinh_diện_tich_cho_thue = get_field('button_tinh_diện_tich_cho_thue', 'option');
$ds_tu_van = get_field('ds_tu_van', 'option');

$price_range = get_field('price_range', $post_id);
$land_area = get_field('land_area', $post_id);
$construction_area = get_field('construction_area', $post_id);
$tieu_de_tu_van = get_field('tieu_de_tu_van_cho_thue', 'option');


?>

<div class="pd-sidebar rounded-4 bg-white shadow-light border border-neutral-100 py-8 px-5">
    <div class="box">
        <div class="infos space-y-3">
            <?php
            if ($post_type == 'office-rent') {
            ?>
                <div class="info flex items-center justify-between gap-3">
                    <div class="label font-bold"><?php _e('Giá thuê', 'canhcamtheme'); ?></div>
                    <div class="price subheader-24 text-primary-2 font-black">$<?php echo !empty($price_product) ? $price_product : '0' ?>++/m2</div>
                </div>
                <div class="info flex items-center justify-between gap-3">
                    <div class="label font-bold"><?php _e('Phí quản lý', 'canhcamtheme'); ?></div>
                    <div class="content">$<?php echo !empty($price_management) ? $price_management : '0' ?>/m2</div>
                </div>
                <div class="info flex items-center justify-between gap-3">
                    <div class="label font-bold"><?php _e('Thuế VAT', 'canhcamtheme'); ?></div>
                    <div class="content"><?php echo !empty($vat) ? $vat : '' ?></div>
                </div>
                <div class="info flex items-center justify-between gap-3">
                    <div class="label font-bold"><?php _e('Tổng giá thuê dự kiến', 'canhcamtheme'); ?></div>
                    <div class="content">$<?php echo !empty($total_price) ? $total_price : '0' ?>/m2</div>
                </div>

            <?php
            } else {
            ?>
                <div class="info flex items-center justify-between gap-3">
                    <div class="label font-bold"><?php _e('Giá bán', 'canhcamtheme'); ?></div>
                    <div class="price subheader-24 text-primary-2 font-black"><?php echo !empty($price_range) ? $price_range : '0' ?></div>
                </div>
                <div class="info flex items-center justify-between gap-3">
                    <div class="label font-bold"><?php _e('Diện tích đất', 'canhcamtheme'); ?></div>
                    <div class="content"><?php echo !empty($land_area) ? $land_area : '' ?>m2</div>
                </div>
                <div class="info flex items-center justify-between gap-3">
                    <div class="label font-bold"><?php _e('Diện tích xây dựng', 'canhcamtheme'); ?></div>
                    <div class="content"><?php echo !empty($construction_area) ? $construction_area : '' ?>m2</div>
                </div>

            <?php
            }
            ?>

        </div>
    </div>
    <?php if ($post_type == 'office-rent') { ?>
        <?php if ($button_tinh_diện_tich_cho_thue) : ?>
            <div class="box box-square">
                <div class="square flex items-center gap-2.5 justify-center text-black">
                    <i class="fa-light fa-draw-square"></i>
                    <span><?php echo !empty($area) ? $area : '' ?></span>
                </div>
                <a
                    href="<?= $button_tinh_diện_tich_cho_thue ?>"
                    class="btn btn-border-orange mt-5 w-full btn-primary "><?php echo _e('Tính diện tích cho thuê', 'canhcamtheme'); ?><i class="fa-solid fa-calculator-simple"></i></a>
            </div>
        <?php endif; ?>
    <?php } ?>
    <div class="box box-contact">
        <div class="description body-14 font-medium text-center text-black"><?php echo !empty($tieu_de_tu_van) ? $tieu_de_tu_van : '' ?></div>
        <a class="btn btn-border-green mt-5 w-full btn-primary " href="tel:<?php echo !empty($hotline) ? $hotline : '' ?>"><?php echo _e('Hotline', 'canhcamtheme'); ?><i class="fa-light fa-phone"></i></a>
        <a class="btn btn-primary mt-3 w-full" data-fancybox data-src="#popup-contact" data-popup="popup-contact-template">
            <?php echo _e('Yêu cầu tư vấn nhanh', 'canhcamtheme'); ?> <i class="fa-regular fa-headset"></i></a>
        <div class="hidden">
            <div id="popup-contact">
                <div class="grid md:grid-cols-2 items-center base-gap">
                    <div class="img"><a class="ratio-[282/424] rounded-4"><img class="lozad" data-src="<?php echo get_the_post_thumbnail_url($post_id, 'full'); ?>" alt="<?php echo get_the_title($post_id); ?>" /></a>
                    </div>
                    <div class="content">
                        <div class="block-title"><?php echo __('Bạn quan tâm đến', 'canhcamtheme'); ?></div>
                        <div class="sub-title subheader-20 font-bold text-primary-2 mt-5"><?php echo get_the_title($post_id); ?></div>
                        <div class="mt-3 ctn font-medium"><?php echo __('Vui lòng điền thông tin để nhận tư vấn nhanh hoặc đặt lịch xem thực tế', 'canhcamtheme'); ?></div>
                    </div>
                </div>
                <div class="mt-8"></div>
                <?php echo do_shortcode('[contact-form-7 id="bd20446" title="Form Tư Vấn"]'); ?>
            </div>
        </div>
        <div class="description body-14 font-medium text-center mt-5 text-black"><?php echo __('Chuyên viên tư vấn sẵn sàng hỗ trợ', 'canhcamtheme'); ?></div>

        <?php
        if (!empty($ds_tu_van)) {
        ?>
            <div class="supports mt-5 clamp:max-w-[184px] mx-auto w-full">
                <div class="swiper-column-auto relative auto-3-column  auto-play allow-touchMove">
                    <div class="swiper">
                        <div class="swiper-wrapper">
                            <?php
                            foreach ($ds_tu_van as $item) {
                            ?>
                                <div class="swiper-slide">
                                    <a class="ratio-[1/1] rounded-full bg-neutral-100"><img class="lozad" data-src="<?php echo !empty($item['url']) ? $item['url'] : '' ?>" alt="<?php echo !empty($item['title']) ? $item['title'] : '' ?>" /></a>
                                </div>
                            <?php
                            }
                            ?>

                        </div>
                    </div>
                </div>
            </div>
        <?php
        }
        ?>
    </div>
</div>