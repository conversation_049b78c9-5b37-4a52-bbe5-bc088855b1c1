<?php
$post_id = $args['post_id'];

$price_product = get_field('price_product', $post_id);
$hotline = get_field('hotline', $post_id);


$tieu_de_tu_van = get_field('tieu_de_tu_van', 'option');
$ds_tu_van = get_field('ds_tu_van', 'option');
$dynamic_field_dich_vu_tron_goi_so_cho_ngoi = get_post_meta($post_id, 'dynamic_field_dich_vu_tron_goi_so-cho-ngoi', true);
?>


<div class="pd-sidebar rounded-4 bg-white shadow-light border border-neutral-100 py-8 px-5">
    <div class="box box-square">
        <?php if (!empty($dynamic_field_dich_vu_tron_goi_so_cho_ngoi)) : ?>
            <div class="square flex items-center gap-2.5 justify-center text-black"><i class="fa-light fa-chair"></i><span><?php echo !empty($dynamic_field_dich_vu_tron_goi_so_cho_ngoi) ? $dynamic_field_dich_vu_tron_goi_so_cho_ngoi . ' ' . __('chỗ ngồi', 'canhcamtheme') : '' ?></span></div>
        <?php endif; ?>
        <?php if (!empty($price_product)) : ?>
            <div class="price subheader-24 font-black text-primary-2 text-center mt-5">$<?php echo !empty($price_product) ? $price_product : '' ?>/<?php echo __('chỗ ngồi', 'canhcamtheme'); ?></div>
        <?php endif; ?>
        <div class="note text-center mt-3"><?php echo __('Chưa bao gồm thuế VAT', 'canhcamtheme'); ?></div>
    </div>
    <div class="box box-contact">
        <div class="description body-14 font-medium text-center text-black"><?php echo !empty($tieu_de_tu_van) ? $tieu_de_tu_van : '' ?></div>
        <a class="btn btn-border-green mt-5 w-full btn-primary " href="tel:<?php echo !empty($hotline) ? $hotline : '' ?>"><?php echo _e('Hotline', 'canhcamtheme'); ?><i class="fa-light fa-phone"></i></a>
        <a class="btn btn-primary mt-3 w-full" data-fancybox data-src="#popup-contact" data-popup="popup-contact-template">
            <?php echo _e('Yêu cầu tư vấn nhanh', 'canhcamtheme'); ?> <i class="fa-regular fa-headset"></i></a>
        <div class="hidden">
            <div id="popup-contact">
                <div class="grid md:grid-cols-2 items-center base-gap">
                    <div class="img"><a class="ratio-[282/424] rounded-4"><img class="lozad" data-src="<?php echo get_the_post_thumbnail_url($post_id, 'full'); ?>" alt="<?php echo get_the_title($post_id); ?>" /></a>
                    </div>
                    <div class="content">
                        <div class="block-title"><?php echo __('Bạn quan tâm đến', 'canhcamtheme'); ?></div>
                        <div class="sub-title subheader-20 font-bold text-primary-2 mt-5"><?php echo get_the_title($post_id); ?></div>
                        <div class="mt-3 ctn font-medium"><?php echo __('Vui lòng điền thông tin để nhận tư vấn nhanh hoặc đặt lịch xem thực tế', 'canhcamtheme'); ?></div>
                    </div>
                </div>
                <div class="mt-8"></div>
                <?php echo do_shortcode('[contact-form-7 id="bd20446" title="Form Tư Vấn"]'); ?>
            </div>
        </div>
        <div class="description body-14 font-medium text-center mt-5 text-black"><?php echo __('Chuyên viên tư vấn sẵn sàng hỗ trợ', 'canhcamtheme'); ?></div>

        <?php
        if (!empty($ds_tu_van)) {
        ?>
            <div class="supports mt-5 clamp:max-w-[184px] mx-auto w-full">
                <div class="swiper-column-auto relative auto-3-column  auto-play allow-touchMove">
                    <div class="swiper">
                        <div class="swiper-wrapper">
                            <?php
                            foreach ($ds_tu_van as $item) {
                            ?>
                                <div class="swiper-slide">
                                    <a class="ratio-[1/1] rounded-full bg-neutral-100"><img class="lozad" data-src="<?php echo !empty($item['url']) ? $item['url'] : '' ?>" alt="<?php echo !empty($item['title']) ? $item['title'] : '' ?>" /></a>
                                </div>
                            <?php
                            }
                            ?>

                        </div>
                    </div>
                </div>
            </div>
        <?php
        }
        ?>
    </div>
</div>