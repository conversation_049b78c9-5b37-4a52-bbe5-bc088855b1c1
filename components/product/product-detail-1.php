<?php
// $location = get_field('location', get_the_ID());

$post_id = $args['post_id'];
$gallery = get_field('gallery', $post_id);

$full_address = $args['full_address'];
$chu_de_1 = get_field('chu_de_1', $post_id);
$chu_de_2 = get_field('chu_de_2', $post_id);
$chu_de_3 = get_field('chu_de_3', $post_id);

if (!empty($chu_de_2)) {
    $full_address = '';
    $city_id = get_post_meta($post_id, '_office_city', true);
    $district_id = get_post_meta($post_id, '_office_district', true);
    $ward_id = get_post_meta($post_id, '_office_ward', true);
    $street_id = get_post_meta($post_id, '_office_street', true);
    $house_number = get_post_meta($post_id, '_office_house_number', true);

    if (!empty($house_number)) {
        $full_address .= $house_number;
    }
    // if (!empty($street_id)) {
    //     $full_address .= (!empty($full_address) ? ' - ' : '') . '<a href="' . get_the_permalink($street_id) . '">' . get_the_title($street_id) . '</a>';
    // }
    // if (!empty($ward_id)) {
    //     if (!empty($chu_de_3)) {
    //         $full_address .= (!empty($full_address) ? ' - ' : '') . '<a href="' . get_the_permalink($chu_de_3) . '">' . get_the_title($ward_id) . '</a>';
    //     } else {
    //         $full_address .= (!empty($full_address) ? ' - ' : '') . get_the_title($ward_id);
    //     }
    // }
    // if (!empty($district_id)) {
    //     if (!empty($chu_de_2)) {
    //         $full_address .= (!empty($full_address) ? ' - ' : '') . '<a href="' . get_the_permalink($chu_de_2) . '">' . get_the_title($district_id) . '</a>';
    //     } else {
    //         $full_address .= (!empty($full_address) ? ' - ' : '') . get_the_title($district_id);
    //     }
    // }
    // if (!empty($city_id)) {
    //     if (!empty($chu_de_1)) {
    //         $full_address .= (!empty($full_address) ? ' - ' : '') . '<a href="' . get_the_permalink($chu_de_1) . '">' . get_the_title($city_id) . '</a>';
    //     } else {
    //         $full_address .= (!empty($full_address) ? ' - ' : '') . get_the_title($city_id);
    //     }
    // }
}



if (is_post_favorite($post_id)) {
    $class = 'is-favorite';
} else {
    $class = '';
}

?>
<section class="product-detail-1-1 section pt-10 pb-0 scroll-section" data-title="<?php echo __('Tổng quan', 'canhcamtheme'); ?>">
    <div class="container">
        <div class="relative">
            <h1 class="block-title uppercase"><?php the_title(); ?></h1>
            <div class="flex items-center justify-between gap-3 mt-6 flex-wrap">
                <div class="location flex gap-2.5 items-center [&_i]:text-primary-1"><i class="fa-light fa-location-dot"></i><span><?php echo !empty($full_address) ? $full_address : ''; ?></span></div>
                <div class="tool flex items-center gap-6">
                    <?php echo display_rating_html(get_the_ID(), true); ?>

                    <div class="favorite <?php echo $class; ?>" data-id="<?php echo $post_id; ?>"><i class="fa-light fa-heart"></i></div>
                </div>
            </div>
            <div class="share">
                <div class="share-wrapper"><a id="facebook-share"><i class="fa-brands fa-facebook-f"></i></a><a id="linkedin-share"><i class="fa-brands fa-linkedin-in"></i></a>
                </div>
            </div>
            <div class="gallery grid md:grid-cols-2 gap-5 mt-10">
                <?php if (!empty($gallery[0])) :
                    $video_url = get_field('link_video_media', $gallery[0]['ID']);
                    $link = !empty($video_url) ? $video_url : $gallery[0]['url'];
                ?>
                    <div class="img relative">
                        <a class="ratio-[466/690] rounded-4" data-fancybox="popup-gallery-desktop" href="<?php echo !empty($link) ? $link : ''; ?>">
                            <img class="lozad" data-src="<?php echo $gallery[0]['url'] ?>" />
                        </a>
                    </div>
                <?php endif; ?>
                <div class="grid grid-cols-2 gap-5">
                    <?php if (!empty($gallery)) :
                        foreach ($gallery as $key => $item) :
                    ?>
                            <?php if ($key > 0 && $key <= 4) : ?>
                                <div class="img relative">
                                    <a class="ratio-[223/335] rounded-4" data-fancybox="popup-gallery-desktop" href="<?php echo $item['url']; ?>">
                                        <img class="lozad" data-src="<?php echo $item['url'] ?>" />
                                    </a>
                                    <?php if ($key == 4) :
                                        $number_of_images = count($gallery) - 5;
                                        if ($number_of_images > 0) :
                                    ?>
                                            <div class="content text-center text-white absolute inset-0 flex flex-col items-center justify-center p-3 z-3 [&amp;_i]:header-32 pointer-events-none">
                                                <i class="fa-light fa-folder-image"></i>
                                                <span class="body-16 lg:subheader-24 font-bold">
                                                    <?php
                                                    echo sprintf(
                                                        __('Xem thêm <br> %s ảnh', 'my-textdomain'), // Chuỗi với %s và textdomain
                                                        $number_of_images // Giá trị thay vào %s
                                                    ); ?>
                                                </span>
                                            </div>
                                        <?php endif;
                                        break;
                                        ?>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php endif; ?>

                </div>
                <div class="hidden">
                    <?php foreach ($gallery as $key => $item) :
                        if ($key > 4) :
                            $video_url = get_field('link_video_media', $item['ID']);
                            $link = !empty($video_url) ? $video_url : $item['url'];
                    ?>
                            <a data-fancybox="popup-gallery-desktop" href="<?php echo $link ?>"></a>
                        <?php endif; ?>
                    <?php endforeach; ?>

                </div>
            </div>
        </div>
    </div>
</section>

<?php
function shareSocial()
{
?>
    <script>
        var puWidth = 600;
        var puHeight = 400;

        var shareOptions = {
            width: puWidth,
            height: puHeight,
            left: (screen.width / 2) - (puWidth / 2),
            top: (screen.height / 2) - (puHeight / 2)
        };
        const Features = 'width=' + shareOptions.width + ',height=' + shareOptions.height + ',left=' + shareOptions.left + ',top=' + shareOptions.top;
        $('#facebook-share').on("click", function(e) {
            e.preventDefault();
            const fbShareUrl = 'https://www.facebook.com/sharer/sharer.php?u=<?php esc_html(the_permalink()); ?>&title=' + encodeURIComponent('<?php echo esc_html(get_the_title()); ?>') + '&description=' + encodeURIComponent('<?php echo esc_html(get_the_excerpt()); ?>');
            window.open(fbShareUrl, '_blank', Features);
        });
        $('#twitter-share').on("click", function(e) {
            e.preventDefault();
            const twShareUrl = "http://twitter.com/share?text=" + encodeURIComponent('<?= esc_html(the_title()) ?>') + "&url=" + encodeURIComponent('<?= esc_html(the_permalink()) ?>');
            window.open(twShareUrl, '_blank', Features);
        });
        $('#linkedin-share').on("click", function(e) {
            e.preventDefault();
            const linkedinShareUrl = "https://www.linkedin.com/sharing/share-offsite/?url=" + encodeURIComponent('<?= esc_html(the_permalink()) ?>');
            window.open(linkedinShareUrl, '_blank', Features);
        });
    </script>
<?php
};
add_action('wp_adding_script', 'shareSocial');
?>