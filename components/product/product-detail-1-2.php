<?php

$post_id = get_the_ID();
$post_type = $args['post_type'];

$title_parameter = get_field('title_parameter', get_the_ID());
// $specifications = get_field('specifications', get_the_ID());
$title_info = get_field('title_info', get_the_ID());
// $info_and_price = get_field('info_and_price', get_the_ID());
$td_bai_viet_gioi_thieu = get_field('td_bai_viet_gioi_thieu', get_the_ID());
$nd_bai_viet_gioi_thieu = get_field('nd_bai_viet_gioi_thieu', get_the_ID());
$td_bai_viet_chi_tiet = get_field('td_bai_viet_chi_tiet', get_the_ID());
$nd_bai_viet_chi_tiet = get_field('nd_bai_viet_chi_tiet', get_the_ID());
$ds_tien_ich_lan_can = get_field('ds_tien_ich_lan_can', get_the_ID());

$title_utilities = get_field('title_utilities', get_the_ID());
$content_utilities = get_field('content_utilities', get_the_ID());
// $utilities = get_field('utilities', get_the_ID());

$title_location = get_field('title_location', get_the_ID());
$toa_do = get_field('toa_do', get_the_ID());
$google_map = get_field('google_map', get_the_ID());
$google_street_view = get_field('google_street_view', get_the_ID());

// 
$args_office_rent_info = array(
    'taxonomy' => 'office-rent-info',
    'posts_per_page' => -1,
    'hide_empty' => false,

);
$terms_office_rent_info = get_terms($args_office_rent_info);
// 

$args_office_rent_area = array(
    'taxonomy' => 'office-rent-area',
    'posts_per_page' => -1,
    'hide_empty' => false,

);
$terms_office_rent_area = get_terms($args_office_rent_area);

// 

$args_office_rent_facility = array(
    'taxonomy' => 'office-rent-facility',
    'posts_per_page' => -1,
    'hide_empty' => false,

);
$terms_office_rent_facility = get_terms($args_office_rent_facility);


$thong_so_toa_nha_options = get_field('thong_so_toa_nha', 'option');
$thong_tin_dien_tich_va_gia_thue_options = get_field('thong_tin_dien_tich_va_gia_thue', 'option');
$tien_ich_toa_nha_options = get_field('tien_ich_toa_nha', 'option');


?>
<section class="product-detail-1-2 section pt-10">

    <div class="container">
        <div class="grid lg:grid-cols-[calc(960/1400*100%)_1fr] base-gap xl:gap-[calc(60/1400*100%)]">
            <div class="col-left">
                <!-- Tạo flexible content-->
                <!-- Office Rent Info -->
                <?php
                // Lấy thông số từ option và giá trị từ thông số tòa nhà
                $post_id = get_the_ID();

                if ($thong_so_toa_nha_options) :
                    get_template_part('components/product/boxIconContent', null, array(
                        'section_title' => $title_parameter,
                        'section_content' => '',
                        'section_options' => $thong_so_toa_nha_options,
                        'dynamic_field_name' => 'thong_so_toa_nha',
                        'post_id' => $post_id
                    ));

                endif; ?>

                <?php if ($thong_tin_dien_tich_va_gia_thue_options) :
                    get_template_part('components/product/boxIconContent', null, array(
                        'section_title' => $title_info,
                        'section_content' => '',
                        'section_options' => $thong_tin_dien_tich_va_gia_thue_options,
                        'dynamic_field_name' => 'thong_tin_dien_tich_va_gia_thue',
                        'post_id' => $post_id
                    ));
                endif; ?>

                <!-- Service -->
                <div class="box-content scroll-section" data-title="<?php echo !empty($td_bai_viet_gioi_thieu) ? $td_bai_viet_gioi_thieu : '' ?>">
                    <div class="title subheader-24 font-heading uppercase leading-[1.41667] text-primary-1"><?php echo !empty($td_bai_viet_gioi_thieu) ? $td_bai_viet_gioi_thieu : '' ?></div>
                    <div class="description text-justify simple-prose mt-5">
                        <?php echo !empty($nd_bai_viet_gioi_thieu) ? $nd_bai_viet_gioi_thieu : '' ?>
                    </div>
                </div>


                <!-- Office Rent Facility -->
                <?php if ($tien_ich_toa_nha_options) :
                    get_template_part('components/product/boxIconContent', null, array(
                        'section_title' => $title_utilities,
                        'section_content' => '',
                        'section_options' => $tien_ich_toa_nha_options,
                        'dynamic_field_name' => 'tien_ich_toa_nha',
                        'post_id' => $post_id
                    ));
                endif; ?>


                <!-- Location -->
                <div class="box-content scroll-section" data-title="<?php echo !empty($title_location) ? $title_location : __('Vị trí', 'canhcamtheme') ?>">
                    <div class="title subheader-24 font-heading uppercase leading-[1.41667] text-primary-1"><?php echo !empty($title_location) ? $title_location : __('Vị trí', 'canhcamtheme') ?></div>
                    <div class="box-tab mt-5" tab-wrapper="parent">
                        <nav>
                            <ul>
                                <?php if (!empty($toa_do)) : ?>
                                    <li tab-item="parent" tab-item-value="1"><a><?php _e('Tab Google map', 'canhcamtheme'); ?></a></li>
                                <?php endif; ?>
                                <?php if (!empty($google_street_view)) : ?>
                                    <li tab-item="parent" tab-item-value="2"><a><?php _e('Tab Google street view', 'canhcamtheme'); ?></a></li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        <div class="tab-content mt-5">
                            <?php if (!empty($google_map)) : ?>
                                <div class="tab-content-item" tab-content="parent" tab-content-value="1">
                                    <div class="map-ratio ratio-[520/960]">
                                        <div class="ratio-frame">
                                        <?php echo $google_map; ?>

                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <?php if (!empty($google_street_view)) : ?>
                                <div class="tab-content-item" tab-content="parent" tab-content-value="2">
                                    <div class="map-ratio ratio-[520/960]">
                                        <div class="ratio-frame">
                                            <?php echo $google_street_view; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="box-content scroll-section" data-title="<?php echo !empty($td_bai_viet_chi_tiet) ? $td_bai_viet_chi_tiet : '' ?>">
                    <div class="title subheader-24 font-heading uppercase leading-[1.41667] text-primary-1"><?php echo !empty($td_bai_viet_chi_tiet) ? $td_bai_viet_chi_tiet : '' ?></div>
                    <div class="description text-justify simple-prose mt-5">
                        <?php echo !empty($nd_bai_viet_chi_tiet) ? $nd_bai_viet_chi_tiet : '' ?>
                    </div>
                    <div class="bottom-layout-table mt-5">
                        <?php
                        if (!empty($ds_tien_ich_lan_can)) {
                        ?>
                            <table>
                                <?php foreach ($ds_tien_ich_lan_can as $tien_ich) : ?>
                                    <tr>
                                        <td><?php echo $tien_ich['ten']; ?></td>
                                        <td><?php echo $tien_ich['khoang_cach']; ?></td>
                                        <td><?php echo $tien_ich['thoi_gian']; ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </table>
                        <?php
                        }
                        ?>
                    </div>
                </div>




            </div>
            <div class="col-right">
                <?php get_template_part('components/product/boxPrice-1', null, array('post_id' => $post_id, 'post_type' => $post_type)); ?>
            </div>
        </div>
    </div>
</section>