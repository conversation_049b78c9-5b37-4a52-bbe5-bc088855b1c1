<?php get_header(); ?>
<?php
get_template_part('./modules/banner/page-banner');
?>

<?php
global $post;
$taxonomy = get_post_taxonomies($post);
$term = wp_get_post_terms($post->ID, $taxonomy);
$cats = array_map(function ($o) {
    return $o->term_id;
}, $term);
$the_query = new WP_query(get_other_post_type($post, $cats, 5));
$post_categories = get_post_primary_category($id, $taxonomy[0]);
$primary_category = $post_categories['primary_category'];
?>

<section class="news-detail section xl:py-20">
    <div class="container">
        <div class="grid grid-cols-12 base-gap">
            <div class="col-left col-span-12 lg:col-span-9 relative">
                <div class="article-header">
                    <h1 class="title small-block-title">
                        <?= get_the_title($id) ?>
                    </h1>
                    <div class="article-info flex items-center gap-4 mt-6">
                        <?php if ($primary_category) : ?>
                            <div class="category text-primary-2">
                                <?= $primary_category->name ?>
                            </div>
                        <?php endif; ?>
                        <time class="body-14 text-neutral-500 block md:mx-10">
                            <?= dateFormatOnLayout($id) ?>
                        </time>
                        <div class="line flex-1 bg-neutral-200 w-full h-0.5"></div>
                    </div>
                    <div class="share-wrapper absolute top-0 -left-18 h-full">
                        <div class="share sticky top-28"><a id="facebook-share" class="size-12 rounded-full border border-neutral-200 flex items-center justify-center hover:scale-105 hover:shadow-md hover:bg-primary-2 hover:text-white"><i class="fa-brands fa-facebook-f"></i></a></div>
                    </div>
                    <div class="article-content mt-8">
                        <div class="prose body-16"><?= the_content() ?></div>
                    </div>
                </div>
            </div>
            <div class="col-right col-span-12 lg:col-span-3">
                <div class="wrapper sticky rem:top-[110px]">
                    <div class="title subheader-24 font-bold text-primary-2"><?php _e('Tin tức khác', 'canhcamtheme'); ?></div>
                    <div class="list mt-5">
                        <?php if ($the_query->have_posts()) : while ($the_query->have_posts()) : $the_query->the_post(); ?>
                                <?= get_template_part('/components/boxNews/boxNews-small', '', ['id' => get_the_ID()]) ?>
                        <?php endwhile;
                        endif;
                        wp_reset_postdata(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php get_footer(); ?>


<script>
    var puWidth = 600;
    var puHeight = 400;

    var shareOptions = {
        width: puWidth,
        height: puHeight,
        left: (screen.width / 2) - (puWidth / 2),
        top: (screen.height / 2) - (puHeight / 2)
    };
    const Features = 'width=' + shareOptions.width + ',height=' + shareOptions.height + ',left=' + shareOptions.left + ',top=' + shareOptions.top;
    $('#facebook-share').on("click", function(e) {
        e.preventDefault();
        const fbShareUrl = 'https://www.facebook.com/sharer/sharer.php?u=<?php esc_html(the_permalink()); ?>&title=' + encodeURIComponent('<?php echo esc_html(get_the_title()); ?>') + '&description=' + encodeURIComponent('<?php echo esc_html(get_the_excerpt()); ?>');
        window.open(fbShareUrl, '_blank', Features);
    });
    $('#twitter-share').on("click", function(e) {
        e.preventDefault();
        const twShareUrl = "http://twitter.com/share?text=" + encodeURIComponent('<?= esc_html(the_title()) ?>') + "&url=" + encodeURIComponent('<?= esc_html(the_permalink()) ?>');
        window.open(twShareUrl, '_blank', Features);
    });
    $('#linkedin-share').on("click", function(e) {
        e.preventDefault();
        const linkedinShareUrl = "https://www.linkedin.com/sharing/share-offsite/?url=" + encodeURIComponent('<?= esc_html(the_permalink()) ?>');
        window.open(linkedinShareUrl, '_blank', Features);
    });
</script>