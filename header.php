<!DOCTYPE html>
<html <?php language_attributes(); ?>>


<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link
		href="https://fonts.googleapis.com/css2?family=Anton&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
		rel="stylesheet">
	<!-- Style-->
	<?php wp_head(); ?>
	<!-- Script-->
	<?php $headerCode = get_field('custom_code', "options")['header']; ?>
	<?php if (!is_null($headerCode)) : ?> <?= $headerCode ?> <?php endif; ?>
</head>

<body <?php
		$body_class = isset($GLOBALS['CANHCAM']['bg_body']) ? $GLOBALS['CANHCAM']['bg_body'] : ''; ?> <?php echo body_class($body_class); ?>>
	<?php
	$header = get_field('header_options', 'options');
	$primary_logo = get_theme_mod('custom_logo');
	$socials = $header['socials'];
	$hotline = $header['hotline'];
	$email = $header['email'];
	$internal_links = $header['internal_links'];
	$login_page = get_field('login_page', 'option');
	$register_page = get_field('register_page', 'option');
	$page_cart = get_field('page_cart', 'option');
	$user_info = getUserInfo();
	?>
	<header class="z-[150] fixed top-0 left-0 w-full bg-white select-none">
		<div class="container-fluid">
			<div class="header-wrapper flex justify-center xl:justify-between -xl:relative">
				<div class="xl:hidden absolute left-0 top-1/2 -translate-y-1/2">
					<div class="relative pointer-events-auto cursor-pointer" id="burger"><svg width="32" height="32"
							viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
							<path class="line line-top-bottom"
								d="M27 10 13 10C10.8 10 9 8.2 9 6 9 3.5 10.8 2 13 2 15.2 2 17 3.8 17 6L17 26C17 28.2 18.8 30 21 30 23.2 30 25 28.2 25 26 25 23.8 23.2 22 21 22L7 22">
							</path>
							<path class="line" d="M7 16 27 16"></path>
						</svg></div>
				</div>
				<div class="logo"><a

						class="ratio-[1/1] h-full" href="<?= get_home_url() ?>"><?= custom_lozad_image($primary_logo, false) ?></a></div>
				<div class="col-right">
					<div id="autoClone-WrapTop">
						<div class="wrap-top xl:border-b border-neutral-200 flex justify-end">
							<div class="socials flex">
								<?php if ($hotline) : ?>
									<a
										<?php if ($hotline['href']) : ?>
										href="<?= $hotline['href'] ?>"
										target="_blank"
										<?php endif; ?>
										class="social-icon highlight-bottom sm:hidden contact-link"><i class="fa-regular fa-phone"></i></a>
								<?php endif; ?>
								<?php if ($email) : ?>
									<a
										<?php if ($email['href']) : ?>
										href="<?= $email['href'] ?>"
										target="_blank"
										<?php endif; ?>
										class="social-icon highlight-bottom sm:hidden contact-link"><i
											class="fa-regular fa-envelope"></i></a>
								<?php endif; ?>
								<?php foreach ($socials as $value): ?>
									<a
										<?php if ($value['href']) : ?>
										href="<?= $value['href'] ?>"
										target="_blank"
										<?php endif; ?>
										class="social-icon highlight-bottom"><?= $value['icon'] ?></a>
								<?php endforeach; ?>
							</div>
							<div class="separator"></div>

							<?php if ($hotline) : ?>
								<a
									<?php if ($hotline['href']) : ?>
									href="<?= $hotline['href'] ?>"
									<?php endif; ?>
									class="hotline flex items-center gap-2 highlight-bottom-wrapper contact-link"><span
										class="social-icon highlight-bottom"><i class="fa-regular fa-phone"></i></span><span
										class="xl:text-primary-1 transition-all"> <strong><?= $hotline['content'] ?></strong></span></a>
								<div class="separator"></div>
							<?php endif; ?>
							<?php if ($email) : ?>
								<a
									<?php if ($email['href']) : ?>
									href="<?= $email['href'] ?>"
									<?php endif; ?>
									class="contact-mail flex items-center gap-2 highlight-bottom-wrapper contact-link"><span
										class="social-icon highlight-bottom"><i
											class="fa-regular fa-envelope"></i></span><span
										class="xl:text-primary-1 transition-all"><?= $email['content'] ?></span></a>
								<div class="separator"></div>
							<?php endif; ?>
							<div class="language highlight-bottom">
								<div class="active-language">
									<?php do_action('wpml_add_language_selector'); ?>
								</div>
								<div class="hover-language">
									<?php do_action('wpml_add_language_selector'); ?>
								</div>
							</div>
							<div class="separator"></div>
							<div class="auth-button flex xl:clamp:space-x-[1px] xl:clamp:mr-[1px] <?php if ($user_info) : ?>
								auth-logged
							<?php endif; ?>">
								<?php if ($user_info) :
									$page_account = get_field('page_account', 'option');
								?>
									<a href="<?= get_permalink($page_account) ?>"
										class="btn-auth account highlight-bottom cursor-pointer">
										<i class="fa-regular fa-user"></i>
										<?php _e('Tài khoản', 'canhcamtheme'); ?>
									</a>
									<div class="cart [&amp;_a]:h-full clamp:mb-[-1px]"><a href="<?= get_permalink($page_cart) ?>"
											class="btn-auth highlight-bottom cursor-pointer">
											<i class="fa-regular fa-cart-shopping"></i>
											<?php _e('Giỏ hàng', 'canhcamtheme'); ?>
										</a>
									</div>
								<?php endif; ?>
								<?php if (!$user_info) : ?>
									<?php if ($login_page) : ?>
										<a
											href="<?= get_permalink($login_page) ?>"
											class="btn-auth login highlight-bottom">
											<i class="fa-regular fa-right-to-bracket"></i>
											<?php _e('Đăng nhập', 'canhcamtheme'); ?>
										</a>
									<?php endif; ?>
									<?php if ($register_page) : ?>
										<a
											href="<?= get_permalink($register_page) ?>"
											class="btn-auth register highlight-bottom">
											<i class="fa-regular fa-user-plus"></i><?php _e('Đăng ký', 'canhcamtheme'); ?>
										</a>
									<?php endif; ?>
								<?php endif; ?>
							</div>
							<?php if ($internal_links) : ?>
								<div class="page-link-wrapper flex xl:clamp:space-x-[1px]">
									<?php foreach ($internal_links as $value): ?>
										<?php
										$select_page = $value['select_page'];
										$icon = $value['icon'];
										?>
										<a
											<?php if ($select_page) : ?>
											href="<?= get_permalink($select_page) ?>"
											<?php endif; ?>
											class="page-link highlight-bottom <?php if (get_the_ID() === $select_page->ID) : ?> current-menu-item <?php endif; ?>"><?= $icon ?>
											<?php if ($select_page) : ?>
												<?= $select_page->post_title ?>
											<?php endif; ?>
										</a>
									<?php endforeach; ?>
								</div>
							<?php endif; ?>
						</div>
					</div>
					<div class="wrap-bottom">
						<div class="xl:overflow-visible overflow-auto" id="autoClone-MainMenu">
							<?= custom_mega_menu(); ?>

						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="nav-mobile fixed xl:hidden top-header right-0 w-full bg-white shadow-md z-[100] flex flex-col">
			<div class="close-nav">
				<i class="fa-solid fa-xmark"></i>
				<?php _e('Close Menu', 'canhcamtheme'); ?>
			</div>
			<div class="nav-wrapper h-full flex-1 overflow-hidden relative flex flex-col pb-[24px]">
				<div class="flex-1 h-full clearfix" id="autoCloneHere-MainMenu"></div>
				<div id="autoCloneHere-WrapTop"></div>
			</div>
		</div>
		<div
			class="backdrop-overlay nav-mobile-overlay fixed inset-0 z-[90] pointer-events-none transition-all opacity-0">
		</div>
	</header>
	<?php if ($user_info) : ?>
		<div class="mini-cart-wrapper fixed top-0 right-0 max-w-clamp-600px w-full bg-neutral-50 h-full z-[160] px-4 py-6 flex flex-col transition-all duration-300 translate-x-full shadow-light" id="mini-cart-wrapper">
			<div class="top relative">
				<div class="close-mini-cart size-10 flex items-center justify-center absolute left-0 top-1/2 -translate-y-1/2 group bg-neutral-100 hover:bg-primary-2 rounded-l-6"><i class="fa-regular fa-arrow-left subheader-20 font-bold group-hover:text-white"></i></div>
				<div class="title header-28 uppercase font-bold text-center font-heading">Giỏ hàng</div>
			</div>
			<div class="middle border-t border-t-neutral-200 mt-3 py-3 flex-1 h-full flex flex-col overflow-hidden">
				<div class="list overflow-auto space-y-6 pt-2">
					<div class="bn-small zoom-img relative grid grid-cols-[calc(240/640*100%)_1fr] gap-4 xl:gap-8 items-center">
						<div class="img"><a class="ratio-[160/240] rounded-2"><img class="lozad" data-src="https://picsum.photos/1600/900" /></a>
						</div>
						<div class="content space-y-2 w-full py-3">
							<time class="body-14 text-neutral-400">24.01.2025</time>
							<div class="title body-18 font-bold"><a class="line-clamp-2">Saigon Office tạo dấu ấn trong phân khúc văn phòng cho thuê</a></div>
							<div class="ctn body-14 text-neutral-800 line-clamp-3">Việt Nam đã trải qua một tốc độ phát triển kinh tế ấn tượng trong vài thập kỷ qua. TP.Hồ Chí Minh nằm ở vị trí chiến lược...</div>
						</div>
					</div>
				</div>
			</div>
			<div class="bottom pt-3 border-t border-t-neutral-200 space-y-4">
				<div class="cart-quantity flex items-center justify-between gap-2">
					<div class="title subheader-20 font-bold">Số lượng</div>
					<div class="ctn body-16 font-medium">3 sản phẩm</div>
				</div>
				<div class="total flex items-center justify-between gap-2">
					<div class="title subheader-20 font-bold">Tổng thanh toán</div>
					<div class="ctn body-16 font-medium">250.000 đ</div>
				</div>
				<div class="checkout border-t border-t-neutral-200 pt-3"><a class="btn ml-auto btn-primary ">Xem giỏ hàng<i class="fa-solid fa-right-long"></i></a>
				</div>
			</div>
		</div>
		<div class="backdrop-overlay mini-cart-overlay fixed inset-0 z-[158]"></div>
	<?php endif; ?>

	<main>
		<?php
		if (is_page_template('templates/About.php') || is_tax('recruit-tax')) {
			get_template_part('./modules/banner/page-banner');
		}

		?>
		<?php canhcam_breadcrumbs(); ?>