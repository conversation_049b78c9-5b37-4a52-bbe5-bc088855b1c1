<?php

/**
 * Template Name: Page Product
 */

get_header();

global $post, $wpdb;

$page_id = $post->ID;
$current_page_title = get_the_title($page_id);
$chu_de = get_field('chu_de', $post->ID);
$page_title = get_field('page_title', $post->ID);
$page_description = get_field('page_content_office', $post->ID);
$page_title_rank = get_field('page_title_rank', $post->ID);
$page_description_rank = get_field('page_content_rank', $post->ID);
$page_title_faq = get_field('page_title_faq', $post->ID);
$page_description_faq = get_field('page_content_faq', $post->ID);
$list_faq = get_field('list_faq', $post->ID);
$page_title_map = get_field('page_title_map', $post->ID);
$page_map = get_field('page_map', $post->ID);

// Get all child pages
$child_args = array(
    'post_parent' => $page_id, // The parent id.
    'post_type'   => 'page',
    'post_status' => 'publish',
);
$children = get_children($child_args);

// Get child pages to show in parent
$child_lists_args = array(
    'post_parent' => $page_id, // The parent id.
    'post_type'   => 'page',
    'post_status' => 'publish',
    // 'meta_key'    => 'show_page_parent',
    // 'meta_value'  => '1'
);
$childrens = get_children($child_lists_args);

// Get districts and building classes
$list_districts = get_posts(array(
    'post_type' => 'district',
    'numberposts' => -1,
    'post_status' => 'publish',
    'suppress_filters' => false,
));

$list_building_classes = get_posts(array(
    'post_type' => 'building-class',
    'numberposts' => -1,
    'post_status' => 'publish',
    'suppress_filters' => false,
));

// OPTIMIZATION: Get all district IDs from child pages in one go
$district_ids = array();
$district_to_child = array(); // Map district IDs to child page IDs
if (!empty($children)) {
    foreach ($children as $child) {
        $district_id = get_field('district_id', $child->ID);
        if ($district_id) {
            $district_ids[] = $district_id;
            $district_to_child[$district_id] = $child->ID;
        }
    }
}

// OPTIMIZATION: Get all office counts by district with a single query
$district_counts = array();
if (!empty($district_ids)) {
    $placeholders = implode(',', array_fill(0, count($district_ids), '%d'));

    $counts_query = $wpdb->prepare(
        "SELECT pm.meta_value AS district_id, COUNT(DISTINCT p.ID) AS count
         FROM {$wpdb->posts} p
         INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
         WHERE p.post_type = %s
         AND p.post_status = 'publish'
         AND pm.meta_key = '_office_district'
         AND pm.meta_value IN ({$placeholders})
         GROUP BY pm.meta_value",
        array_merge(array($chu_de), $district_ids)
    );

    $counts = $wpdb->get_results($counts_query, OBJECT_K);

    // Convert to a simple array
    foreach ($counts as $district_id => $result) {
        $district_counts[$district_id] = intval($result->count);
    }
}

// OPTIMIZATION: Get all offices by district for the slideshow in one query
$district_to_offices = array();
if (!empty($childrens)) {
    $child_district_ids = array();
    foreach ($childrens as $child) {
        $district_id = get_field('district_id', $child->ID);
        if ($district_id) {
            $child_district_ids[] = $district_id;
            // Initialize with empty array
            $district_to_offices[$district_id] = array();
        }
    }

    // Only run the query if we have districts to search for
    if (!empty($child_district_ids)) {
        // Get all offices for all districts
        $offices_query = new WP_Query(array(
            'post_type' => $chu_de,
            'posts_per_page' => -1,
            'post_status' => 'publish',
            'meta_key' => '_office_district',
            'meta_value' => $child_district_ids,
            'meta_compare' => 'IN',
        ));

        // Organize by district
        if ($offices_query->have_posts()) {
            while ($offices_query->have_posts()) {
                $offices_query->the_post();
                $office_id = get_the_ID();
                $office_district = get_post_meta($office_id, '_office_district', true);

                if (isset($district_to_offices[$office_district])) {
                    $district_to_offices[$office_district][] = $office_id;
                }
            }
            wp_reset_postdata();
        }
    }
}
?>

<?= get_template_part('components/nav/stickyNav') ?>

<div class="scroll-section filter-nav bg-primary-1/5 py-10" data-title="Tìm kiếm">
    <div class="container">
        <?php get_template_part('components/filter/filter-archive'); ?>
    </div>
</div>

<?php echo display_rating_html(get_the_ID(), true); ?>


<section class="scroll-section cities-1 section pt-10 xl:pb-20" data-title="<?php echo $page_title; ?>">
    <div class="container">
        <h2 class="block-title"><?php echo $page_title; ?></h2>
        <div class="description mt-5 font-medium space-y-4">
            <?php echo $page_description; ?>
        </div>
        <?php
        if ($children) {
        ?>
            <div class="wrapper" has-expand-btn item-count="12" item-expand="12">
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6 mt-10">

                    <?php
                    foreach ($children as $child) {
                        $district_id = get_field('district_id', $child->ID);

                        // OPTIMIZED: Use the pre-fetched counts instead of running a new query
                        $building_count = isset($district_counts[$district_id]) ? $district_counts[$district_id] : 0;

                        if ($building_count > 0) {
                    ?>
                            <div class="expand-item hidden">
                                <div class="product-cat-1 zoom-img relative text-white rounded-4 overflow-hidden item">
                                    <div class="img">
                                        <a href="<?php echo get_the_permalink($child->ID); ?>" class="ratio-[1/1]">
                                            <img class="lozad" data-src="<?php echo (has_post_thumbnail($child->ID)) ? get_the_post_thumbnail_url($child->ID, 'full') : 'https://picsum.photos/1600/900' ?>" />
                                        </a>
                                    </div>
                                    <div class="content absolute bottom-0 left-0 w-full flex items-end z-1 pointer-events-none">
                                        <div class="wrapper w-full p-4">
                                            <div class="title sm:body-18 lg:subheader-20 font-bold"><span class="line-clamp-2"><?php echo get_the_title($district_id); ?></span></div>
                                            <div class="ctn line-clamp-1 mt-2 sm:body-16 body-14"><?php echo $building_count; ?>+ <?php echo __('tòa nhà', 'canhcamtheme'); ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                    <?php
                        }
                    }
                    ?>
                </div>
                <a class="btn mx-auto mt-10 btn-primary expand-btn"><?php echo __('Xem thêm', 'canhcamtheme'); ?><i class="fa-solid fa-plus"></i></a>
            </div>
        <?php
        }
        ?>

    </div>
</section>

<section class="cites-2">
    <div class="cities-by-categories">
        <?php if ($childrens) {
            foreach ($childrens as $child) {
                $district_id = get_field('district_id', $child->ID);

                // OPTIMIZED: Use the pre-fetched offices instead of running a new query
                $office_ids = isset($district_to_offices[$district_id]) ? $district_to_offices[$district_id] : array();

                if (!empty($office_ids)) {
                    // Create a lightweight query object that behaves like WP_Query for the template
                    $officebyDistrict = new stdClass();
                    $officebyDistrict->found_posts = count($office_ids);
                    $officebyDistrict->have_posts = function () use ($office_ids) {
                        return !empty($office_ids);
                    };

                    // Current post index
                    $current_index = 0;

                    // Mimic the_post() function
                    $officebyDistrict->the_post = function () use ($office_ids, &$current_index) {
                        global $post;
                        if (isset($office_ids[$current_index])) {
                            $post = get_post($office_ids[$current_index]);
                            setup_postdata($post);
                            $current_index++;
                            return true;
                        }
                        return false;
                    };
        ?>
                    <div class="scroll-section swiper-column-auto category-item odd:bg-primary-1/5 section xl:py-20 allow-touchMove auto-4-column" data-title="<?php echo $current_page_title . ' ' . get_the_title($district_id); ?>">
                        <div class="container">
                            <div class="wrap-top-nav">
                                <h2 class="block-title"><?php echo $current_page_title . ' ' . get_the_title($district_id); ?></h2>
                                <div class="arrow-button close-arrow">
                                    <div class="button-prev"></div>
                                    <div class="button-next"></div>
                                </div>
                            </div>
                            <div class="swiper mt-10">
                                <div class="swiper-wrapper">
                                    <?php
                                    // Reset the index before starting the loop
                                    $current_index = 0;
                                    foreach ($office_ids as $post_id) {
                                        echo '<div class="swiper-slide">';
                                        if ($chu_de == 'office-rent-full') {
                                            get_template_part('components/boxProduct/box-rent-1', '', array('post_id' => $post_id));
                                        } else {
                                            get_template_part('components/boxProduct/box-rent-1', '', array('post_id' => $post_id));
                                        }
                                        echo '</div>';
                                    }
                                    ?>
                                </div>
                            </div>
                            <a class="btn mx-auto mt-10 btn-primary " href="<?php echo get_the_permalink($child->ID); ?>"><?php echo __('Xem thêm', 'canhcamtheme'); ?><i class="fa-solid fa-right-long"></i></a>
                        </div>
                    </div>
        <?php
                }
            }
        }
        ?>
    </div>
</section>

<div class="container">
    <div class="border-t border-neutral-200"></div>
</div>

<?php
if ($page_map) {
?>
    <section class="scroll-section cities-3 section xl:pt-20 pb-0" data-title="Bản đồ vị trí">
        <div class="container">
            <div class="block-title"><?php echo $page_title_map; ?></div>
            <div class="ratio-[642/1400] mt-10">
                <div class="ratio-frame">
                    <?php echo $page_map; ?>
                </div>
            </div>
        </div>
    </section>
<?php
}
?>

<?php
if ($page_title_rank) {
?>
    <section class="scroll-section cities-4 section xl:pt-20 pb-0 overflow-hidden" data-title="<?php echo $page_title_rank; ?>">
        <div class="container">
            <h2 class="block-title"><?php echo $page_title_rank; ?></h2>
            <div class="description space-y-4 mt-5">
                <?php echo $page_description_rank; ?>
            </div>

            <!-- Show ở trang Văn Phòng Cho Thuê-->
            <?php
            if ($chu_de == 'office-rent') {
            ?>
                <div class="office-gallery">
                    <div class="swiper-column-auto relative auto-4-column mt-10 visible-slide allow-mouseWheel  allow-touchMove">
                        <div class="swiper">
                            <div class="swiper-wrapper">
                                <?php
                                foreach ($list_building_classes as $building_class) {
                                    $building_class_title = get_the_title($building_class->ID);
                                    $building_class_image = get_the_post_thumbnail_url($building_class->ID, 'full');
                                    $building_class_excerpt = get_the_excerpt($building_class->ID);
                                ?>
                                    <div class="swiper-slide">
                                        <div class="item relative text-white rounded-4 overflow-hidden group">
                                            <a href="<?php echo get_the_permalink($building_class->ID); ?>" class="absolute inset-0 z-3"></a>
                                            <div class="img"><a href="<?php echo get_the_permalink($building_class->ID); ?>" class="ratio-[372/320]"><img class="lozad" data-src="<?php echo $building_class_image; ?>" /></a>
                                            </div>
                                            <div class="content absolute bottom-0 left-0 w-full p-5 z-1">
                                                <div class="title subheader-20 font-bold"><?php echo $building_class_title; ?></div>
                                            </div>
                                            <div class="hover-content absolute transition-all duration-500 bg-primary-1/80 rem:backdrop-blur-[10px] group-hover:top-0 w-full h-full top-full left-0 z-1 flex flex-col before:static before:flex-1 after:static after:flex-1 p-5">
                                                <div class="title subheader-20 font-bold"><a href="<?php echo get_the_permalink($building_class->ID); ?>"><?php echo $building_class_title; ?></a></div>
                                                <div class="ctn font-medium overflow-auto rem:mt-2.5">
                                                    <?php echo $building_class_excerpt; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php
                                }
                                ?>
                            </div>
                        </div>
                        <div class="swiper-scrollbar"></div>
                    </div>
                </div>
            <?php
            }
            ?>

            <!-- Show ở trang Văn Phòng Trọn Gói-->
            <?php
            if ($chu_de == 'office-rent-full') {
                $page_logo_rank = get_field('page_logo_rank', $post->ID);
            ?>
                <div class="list-logos grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6 mt-10">
                    <?php
                    foreach ($page_logo_rank as $key => $value) {
                    ?>
                        <div class="img zoom-img rounded-2 border border-enutral-100 overflow-hidden transition-all duration-300">
                            <a class="ratio-[112/200] ratio-contain" href="<?php echo !empty($value['url']) ? $value['url'] : '#'; ?>" target="_blank">
                                <?php if (!empty($value['logo'])) { ?>
                                    <img class="lozad" data-src="<?php echo $value['logo']['url']; ?>" />
                                <?php } ?>
                            </a>
                        </div>
                    <?php
                    }
                    ?>
                </div>

                <a class="btn mx-auto mt-10 btn-primary "><?php echo __('Xem thêm', 'canhcamtheme'); ?><i class="fa-solid fa-plus"></i></a>
            <?php
            }
            ?>
        </div>
    </section>
<?php
}
?>

<?php
if (get_the_content($post->ID)) {
?>
    <section class="cites-5 section pb-0">
        <div class="container">
            <div class="section bg-primary-1/5 rounded-4 px-6 md:rem:px-[60px]">
                <div class="wrap-show-content">
                    <div class="show-content expand-content rem:max-h-[240px] overflow-hidden">
                        <article class="prose font-medium js-toc-content">
                            <?php
                            $content = get_the_content($post->ID);
                            echo replace_toc_tag($content);
                            ?>
                        </article>
                    </div><a class="btn btn-expand mx-auto mt-4 btn-primary "><span><?php echo __('Xem thêm', 'canhcamtheme'); ?></span><span><?php echo __('Thu gọn', 'canhcamtheme'); ?></span><i class="fa-light fa-plus"></i></a>
                </div>
            </div>
        </div>
    </section>
<?php
}
?>

<?php
if ($list_faq) {
?>
    <section class="scroll-section cities-6 section xl:pt-20 pb-0" data-title="Câu hỏi thường gặp">
        <div class="container">
            <div class="grid grid-cols-12 base-gap">
                <div class="col-left col-span-full lg:col-span-4">
                    <div class="wrapper xl:rem:max-w-[360px]">
                        <h2 class="block-title"><?php echo $page_title_faq; ?></h2>
                        <div class="description font-medium mt-5"><?php echo $page_description_faq; ?></div>
                    </div>
                </div>
                <div class="col-right col-span-full lg:col-span-8">
                    <div class="module-toggle list space-y-5" data-open-atleast-one="true" data-open-first-item="true">
                        <?php
                        foreach ($list_faq as $key => $faq) {
                            $faq_title = get_the_title($faq->ID);
                        ?>
                            <div class="item border border-transparent rounded-2 bg-neutral-50 transition-all duration-500">
                                <div class="top body-18 sm:subheader-20 font-bold flex items-center gap-5 p-5 transition-all duration-500">
                                    <div class="id clamp:min-w-5"><?php echo $key + 1; ?></div>
                                    <div class="title flex-1"><?php echo $faq_title; ?></div>
                                    <div class="icon relative"><i class="fa-solid fa-plus"></i>
                                        <i class="fa-solid fa-minus"></i>
                                    </div>
                                </div>
                                <div class="bottom pb-8 px-5 hidden">
                                    <div class="description simple-prose">
                                        <?php echo wpautop($faq->post_content); ?>
                                    </div>
                                </div>
                            </div>
                        <?php
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php
}
?>

<?php get_template_part('components/product/box-footer-product'); ?>
<?php
get_footer();
