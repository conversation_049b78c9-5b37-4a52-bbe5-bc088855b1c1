<?php
/*
Template name: Template - Consignment
*/
?>

<?php get_header(); ?>

<?php
$section_1 = get_field("section_1");
$section_2 = get_field("section_2");
$section_3 = get_field("section_3");
?>
<?php if ($section_1) : ?>
    <section class="consignment-1 overflow-hidden">
        <div class="container">
            <div class="grid lg:grid-cols-2 lg:base-gap xl:gap-0 items-center">
                <div class="col-left xl:pr-15 section -lg:order-2">
                    <h1 class="block-title"><?= $section_1["title"]; ?></h1>
                    <div class="description font-medium text-justify mt-10 space-y-5"><?= $section_1["description"]; ?></div>
                </div>
                <div class="col-right -lg:!mx-[-12px]" stick-to-edge="right" unstick-min="1024">
                    <div class="img"><span class="ratio-[660/960]"><?= custom_lozad_image($section_1["image"]); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php endif; ?>

<?php if ($section_2['form_shortcode']) : ?>
    <section class="consignment-2 overflow-hidden bg-primary-1/5">
        <div class="container">
            <div class="grid lg:grid-cols-2 base-gap xl:gap-0">
                <div class="col-left xl:pr-15 section xl:py-20">
                    <h2 class="block-title"><?= $section_2["title"]; ?></h2>
                    <!-- <div class="form-wrap form-wrap-layout mt-10">
                        <div class="split-form">
                            <div class="form-title col-span-full">Thông tin người ký gởi</div>
                            <div class="form-group">
                                <input type="text" placeholder="Họ và tên...">
                            </div>
                            <div class="form-group">
                                <input type="text" placeholder="Số điện thoại...">
                            </div>
                            <div class="form-group">
                                <input type="text" placeholder="Email...">
                            </div>
                            <div class="form-group">
                                <input type="text" placeholder="Địa chỉ...">
                            </div>
                        </div>
                        <div class="split-form">
                            <div class="form-title col-span-full">Thông tin sản phẩm ký gửi</div>
                            <div class="form-group">
                                <select>
                                    <option>Loại ký gửi</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <input type="text" placeholder="Giá BĐS...">
                            </div>
                            <div class="form-group">
                                <input type="text" placeholder="Địa chỉ...">
                            </div>
                            <div class="form-group file-upload">
                                <input type="text" placeholder="Hình ảnh">
                                <input type="file" placeholder="Hình ảnh">
                            </div>
                            <div class="form-note col-span-full">
                                <p> <strong class="text-primary-2">Hình ảnh tòa nhà < 5MB (nếu có)</strong>
                                </p>
                                <p>(Chọn cùng lúc nhiều hình ảnh để gửi, tối đa 12 hình định dạng: png, jpg, jpeg)</p>
                            </div>
                        </div>
                        <div class="form-submit col-span-full">
                            <button class="btn btn-primary">Gửi <i class="fa-solid fa-right-long"></i></button>
                        </div>
                    </div> -->
                    <?= do_shortcode($section_2["form_shortcode"]); ?>
                </div>
                <div class="col-right lg:block hidden self-end xl:pl-30" stick-to-edge="right" unstick-min="1024">
                    <div class="img"><span class="ratio-[604/820]"><?= custom_lozad_image($section_2["image"]); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php endif; ?>

<?php if ($section_3['repeater']) : ?>
    <section class="consignment-3 section xl:py-20">
        <div class="container">
            <h2 class="block-title"><?= $section_3["title"]; ?></h2>
            <div class="list mt-10 base-gap grid sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-5">
                <?php foreach ($section_3['repeater'] as $key => $value): ?>
                    <div class="item bg-primary-1/5 shadow-light rounded-4 px-6 py-10">
                        <div class="step header-48 font-heading uppercase text-primary-1"><?= $key < 10 ? '0' . $key + 1 : $key; ?></div>
                        <div class="description subheader-20 text-neutral-700 mt-5"><?= $value['description']; ?></div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
<?php endif; ?>

<?php get_footer(); ?>


<script>
    jQuery(document).ready(function($) {
        $(document).on('click', '.file-upload', function() {
            $(this).closest('.form-wrap').find('.file-upload-add-on').fadeIn();
            $(this).closest('.form-wrap').find('.eacf7-uploader-browse').trigger('click');
        });
    });
</script>