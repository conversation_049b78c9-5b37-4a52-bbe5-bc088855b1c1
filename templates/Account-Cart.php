<?php
/*
Template name: Template - Account - Page Cart
*/
?>


<?php
$content = '';

$cart_products = get_cart_products();


ob_start();
?>
<div class="block-title normal-case">
    <?php _e('Giỏ hàng', 'canhcamtheme'); ?>
</div>
<?php
if (!empty($cart_products)) {
    echo '<div class="cart-container grid sm:grid-cols-2 md:grid-cols-3 gap-4 mt-5">';
    foreach ($cart_products as $post) {
        setup_postdata($post);
?>
        <div class="product-item relative" data-id="<?php echo $post->ID; ?>" data-name="<?php echo $post->post_title; ?>">
            <?php get_template_part('components/boxProduct/box-rent-1', '', array('post_id' => $post->ID)); ?>
        </div>
    <?php
    }
    echo '</div>';

    ?>
    <a class="btn btn-light mt-3 btn-primary btn-contact-cart mt-5"
        data-fancybox=""
        data-src="#popup-contact"
        data-popup="popup-contact-template">
        <?php echo __('<PERSON>ên hệ đi xem', 'canhcamtheme') ?>
    </a>
<?php

    wp_reset_postdata();
} else {
    echo '<div class="empty-cart py-10">' . __('Giỏ hàng của bạn đang trống.', 'canhcamtheme') . '</div>';
}

?>
<?php
$content = ob_get_clean();
?>

<div class="hidden">
    <div id="popup-contact">
        <div class="content">
            <div class="block-title"><?php echo __('Liên hệ tư vấn', 'canhcamtheme'); ?></div>
            <div class="mt-3 ctn font-medium"><?php echo __('Vui lòng điền thông tin để nhận tư vấn nhanh hoặc đặt lịch xem thực tế', 'canhcamtheme'); ?></div>
        </div>
        <div class="mt-8"></div>
        <?php echo do_shortcode('[contact-form-7 id="bd20446" title="Form Tư Vấn"]'); ?>
    </div>
</div>

<?= get_template_part("templates/Account", '', [
    "content" => $content
]) ?>