<?php
/*
Template name: Template - <PERSON><PERSON><PERSON> k<PERSON>
*/
?>

<?= get_header(); ?>
<?php
$userInfo = getUserInfo();
$ancestor = get_ancestors(get_the_ID(), 'page');
$count_ancestor = count($ancestor);
$parent_id = $count_ancestor > 0 ? $ancestor[$count_ancestor - 1] : get_the_ID();
$login_page = get_field("login_page", 'options');
$child_page = get_children($parent_id);
?>

<?php
$default_content = '';
ob_start();
?>
<div class="block-title normal-case">
    <?php _e('Tài khoản của tôi', 'canhcamtheme'); ?>
</div>
<div class="description mt-4">
    <?php echo __('Chào mừng bạn đến với trang tài khoản', 'canhcamtheme'); ?>
</div>
<?php
$default_content = ob_get_clean();
$content = $default_content;
?>

<?php

if (isset($args['content'])) {
    $content = $args['content'];
}
?>

<section class="account-page section xl:py-20">
    <div class="container">
        <?php if (!$userInfo) : ?>
            <div class="block-title text-center">
                <?php _e('Vui lòng đăng nhập để xem thông tin tài khoản', 'canhcamtheme'); ?>
            </div>
            <?php if ($login_page) : ?>
                <a href="<?= get_permalink($login_page) ?>" class="btn btn-primary mt-8 mx-auto"><i class="fa-regular fa-right-to-bracket"></i> <?php _e('Đăng nhập', 'canhcamtheme'); ?></a>
            <?php endif; ?>
        <?php else : ?>
            <div class="block-title text-center">
                <?php _e('Thông tin tài khoản', 'canhcamtheme'); ?>
            </div>
            <div class="grid grid-cols-12 base-gap mt-10">
                <div class="col-left col-span-full xl:col-span-4">
                    <div class="account-menu">
                        <div class="title subheader-24 font-bold text-white bg-primary-1 px-5 py-6">
                            <?php _e('Tài khoản của tôi', 'canhcamtheme'); ?>
                        </div>
                        <ul>
                            <?php if ($child_page) : ?>
                                <?php foreach ($child_page as $value): ?>
                                    <li class="<?php if ($value->ID == get_the_ID()) : ?> active <?php endif; ?>">
                                        <a href="<?= get_permalink($value) ?>">
                                            <?= $value->post_title ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            <li class="signout"><a href="<?= wp_logout_url(home_url()) ?>"><i class="fa-solid fa-right-from-bracket"></i> <?php _e('Đăng xuất', 'canhcamtheme'); ?></a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-right col-span-full xl:col-span-8">
                    <?php if ($content) : ?>
                        <div class="wrapper bg-neutral-100 rounded-2 p-6 md:p-10">
                            <?= $content ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<?= get_footer(); ?>