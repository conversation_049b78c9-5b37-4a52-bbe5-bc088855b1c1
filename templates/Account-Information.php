<?php
/*
Template name: Template - Account - Thông tin tài khoản
*/
?>

<?php

$content = "";
ob_start();

$user_info = getUserInfo();
?>
<div class="block-title normal-case"><?php _e('Xin chào', 'canhcamtheme'); ?>, <?= $user_info['name'] ?></div>
<div class="form-wrap account-info-form">
    <form id="update-account-form">
        <?php wp_nonce_field('update_account_info', 'account_info_nonce'); ?>
        <h3 class="subheader-24 font-bold text-primary-1 mb-4"><?php _e('Tài khoản', 'canhcamtheme'); ?></h3>
        <div class="form-group mt-6">
            <label for="display_name"><?php _e('Tên hiển thị', 'canhcamtheme'); ?></label>
            <input type="text" name="display_name" id="display_name" class="mt-2" placeholder="<?php _e('Tên hiển thị', 'canhcamtheme'); ?>" value="<?= $user_info['name'] ?>">
        </div>
        <div class="form-group mt-6">
            <label for="email"><?php _e('Email', 'canhcamtheme'); ?></label>
            <input disabled type="email" placeholder="Email" class="mt-2" value="<?= $user_info['email'] ?>">
        </div>
        <!-- <div class="form-group">
            <label for="phone"><?php _e('Số điện thoại', 'canhcamtheme'); ?></label>
            <input disabled type="text" placeholder="<?php _e('Số điện thoại', 'canhcamtheme'); ?>" value="<?= $user_info['phone'] ?>">
        </div>
         -->
        <div class="password-change-section mt-10">
            <h3 class="subheader-24 font-bold text-primary-1 mb-4"><?php _e('Đổi mật khẩu', 'canhcamtheme'); ?></h3>
            <div class="form-group mt-6">
                <label for="current_password"><?php _e('Mật khẩu hiện tại', 'canhcamtheme'); ?></label>
                <input type="password" name="current_password" id="current_password" class="mt-2" placeholder="<?php _e('Mật khẩu hiện tại', 'canhcamtheme'); ?>">
            </div>
            <div class="form-group mt-6">
                <label for="new_password"><?php _e('Mật khẩu mới', 'canhcamtheme'); ?></label>
                <input type="password" name="new_password" id="new_password" class="mt-2" placeholder="<?php _e('Mật khẩu mới', 'canhcamtheme'); ?>">
            </div>
            <div class="form-group mt-6">
                <label for="confirm_password"><?php _e('Xác nhận mật khẩu mới', 'canhcamtheme'); ?></label>
                <input type="password" name="confirm_password" id="confirm_password" class="mt-2" placeholder="<?php _e('Xác nhận mật khẩu mới', 'canhcamtheme'); ?>">
            </div>
        </div>

        <div class="form-submit mt-6">
            <button type="submit" class="btn btn-primary"><?php _e('Cập nhật', 'canhcamtheme'); ?></button>
        </div>
        <div class="response-message mt-4"></div>
    </form>
</div>

<script>
    jQuery(document).ready(function($) {
        $('#update-account-form').on('submit', function(e) {
            e.preventDefault();

            var formData = $(this).serialize();
            var submitButton = $(this).find('button[type="submit"]');
            var responseMessage = $(this).find('.response-message');

            // Disable button and show loading state
            submitButton.prop('disabled', true).html('<i class="fa-solid fa-spinner fa-spin"></i> <?php _e('Đang xử lý...', 'canhcamtheme'); ?>');

            $.ajax({
                url: '<?= admin_url('admin-ajax.php') ?>',
                type: 'POST',
                data: {
                    action: 'update_account_info',
                    formData: formData
                },
                success: function(response) {
                    if (response.success) {
                        responseMessage.html('<div class="alert alert-success">' + response.data.message + '</div>');

                        // Update displayed name if changed
                        if (response.data.new_display_name) {
                            $('.block-title.normal-case').text('<?php _e('Xin chào', 'canhcamtheme'); ?>, ' + response.data.new_display_name);
                        }

                        // Clear password fields
                        $('#current_password, #new_password, #confirm_password').val('');
                    } else {
                        responseMessage.html('<div class="alert alert-danger">' + response.data.message + '</div>');
                    }

                    // Re-enable button
                    submitButton.prop('disabled', false).text('<?php _e('Cập nhật', 'canhcamtheme'); ?>');

                    setTimeout(function() {
                        window.location.reload();
                    }, 4000);
                },
                error: function() {
                    responseMessage.html('<div class="alert alert-danger"><?php _e('Đã xảy ra lỗi. Vui lòng thử lại sau.', 'canhcamtheme'); ?></div>');
                    submitButton.prop('disabled', false).text('<?php _e('Cập nhật', 'canhcamtheme'); ?>');
                }
            });
        });
    });
</script>
<?php
$content = ob_get_clean();
?>

<?= get_template_part("templates/Account", '', [
    "content" => $content
]) ?>