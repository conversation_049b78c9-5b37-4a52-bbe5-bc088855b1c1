<?php

/*
Template Name: Page Product Child
*/

global $post;

if ($post->post_parent != 0) {
    $post_parent_id = $post->post_parent;
} else {
    $post_parent_id = $post->ID;
}

$post_id = $post->ID;
$chu_de = get_field('chu_de', $post_parent_id);
$district_page_child_id = get_field('district_id', $post_id);
$thuong_hieu = get_field('thuong_hieu', $post_id);
$page_title = get_the_title($post_id);
$page_description = get_field('page_description', $post_id);

$page_title_faq = get_field('page_title_faq', $post_parent_id);
$page_description_faq = get_field('page_content_faq', $post_parent_id);
$list_faq = get_field('list_faq', $post_parent_id);


// Get all child pages
$child_args = array(
    'post_parent' => $post_parent_id, // The parent id.
    'post_type'   => 'page',
    'post_status' => 'publish',
);
$children = get_children($child_args);


// OPTIMIZATION: Get all district IDs from child pages in one go
$district_ids = array();
$district_to_child = array(); // Map district IDs to child page IDs
if (!empty($children)) {
    foreach ($children as $child) {
        $district_id = get_field('district_id', $child->ID);
        if ($district_id) {
            $district_ids[] = $district_id;
            $district_to_child[$district_id] = $child->ID;
        }
    }
}

// OPTIMIZATION: Get all office counts by district with a single query
$district_counts = array();
if (!empty($district_ids)) {
    $placeholders = implode(',', array_fill(0, count($district_ids), '%d'));

    $counts_query = $wpdb->prepare(
        "SELECT pm.meta_value AS district_id, COUNT(DISTINCT p.ID) AS count
         FROM {$wpdb->posts} p
         INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
         WHERE p.post_type = %s
         AND p.post_status = 'publish'
         AND pm.meta_key = '_office_district'
         AND pm.meta_value IN ({$placeholders})
         GROUP BY pm.meta_value",
        array_merge(array($chu_de), $district_ids)
    );

    $counts = $wpdb->get_results($counts_query, OBJECT_K);

    // Convert to a simple array
    foreach ($counts as $district_id => $result) {
        $district_counts[$district_id] = intval($result->count);
    }
}

get_header();

$paged = (get_query_var('paged')) ? get_query_var('paged') : 1;

// Get sort parameter from URL
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'default';

// Set up query arguments based on sort parameter
$meta_query = array();
$query_args = array(
    'post_type' => $chu_de,
    'posts_per_page' => 24,
    'paged' => $paged,
    'suppress_filters' => false,
);

if ($district_page_child_id) {
    $meta_query[] = array(
        'key' => '_office_district',
        'value' => $district_page_child_id
    );
}

if ($thuong_hieu) {
    $meta_query[] = array(
        'key' => '_office_brand',
        'value' => $thuong_hieu
    );
}


if (count($meta_query) > 0) {
    $query_args['meta_query'] = $meta_query;
}

// Apply sorting based on URL parameter
if ($sort == 'price-asc') {
    $query_args['meta_key'] = 'price_product';
    $query_args['orderby'] = 'meta_value_num';
    $query_args['order'] = 'ASC';
} elseif ($sort == 'price-desc') {
    $query_args['meta_key'] = 'price_product';
    $query_args['orderby'] = 'meta_value_num';
    $query_args['order'] = 'DESC';
} else {
    $query_args['orderby'] = 'title';
    $query_args['order'] = 'ASC';
}

$office_rent_query = new WP_Query($query_args);



// OPTIMIZED APPROACH: Use direct SQL queries to get count data efficiently
global $wpdb;

if (!empty($district_id)) {
    // Get wards by district_id
    $wards = get_posts(array(
        'post_type' => 'ward',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
        'suppress_filters' => false,
        'meta_query' => array(
            array(
                'key' => 'district_id',
                'value' => $district_id
            )
        )
    ));

    // Get all ward IDs
    $ward_ids = wp_list_pluck($wards, 'ID');

    // If no wards found, set empty array
    $wards_with_counts = array();

    if (!empty($ward_ids)) {
        // Get counts for all wards in a single query
        $ward_counts = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT pm.meta_value AS ward_id, COUNT(p.ID) AS count
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
                WHERE p.post_type = %s
                AND p.post_status = 'publish'
                AND pm.meta_key = '_office_ward'
                AND pm.meta_value IN (" . implode(',', array_fill(0, count($ward_ids), '%d')) . ")
                GROUP BY pm.meta_value",
                array_merge(array($chu_de), $ward_ids)
            ),
            OBJECT_K
        );

        // Create wards with counts array
        foreach ($wards as $ward) {
            $ward_id = $ward->ID;
            if (isset($ward_counts[$ward_id]) && $ward_counts[$ward_id]->count > 0) {
                $wards_with_counts[] = array(
                    'id' => $ward_id,
                    'title' => $ward->post_title,
                    'count' => $ward_counts[$ward_id]->count,
                    'permalink' => get_permalink($ward_id)
                );
            }
        }

        // Sort wards by count (highest first)
        usort($wards_with_counts, function ($a, $b) {
            return $b['count'] - $a['count'];
        });
    }

    // Get blocks with associated properties
    $blocks = get_posts(array(
        'post_type' => 'block',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
        'suppress_filters' => false
    ));

    // Get all block IDs
    $block_ids = wp_list_pluck($blocks, 'ID');

    // If no blocks found, set empty array
    $blocks_with_counts = array();

    if (!empty($block_ids)) {
        // Get counts for all blocks in a single query
        $block_counts = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT pm.meta_value AS block_id, COUNT(p.ID) AS count
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
                WHERE p.post_type = %s
                AND p.post_status = 'publish'
                AND pm.meta_key = '_office_block'
                AND pm.meta_value IN (" . implode(',', array_fill(0, count($block_ids), '%d')) . ")
                GROUP BY pm.meta_value",
                array_merge(array($chu_de), $block_ids)
            ),
            OBJECT_K
        );

        // Create blocks with counts array
        foreach ($blocks as $block) {
            $block_id = $block->ID;
            if (isset($block_counts[$block_id]) && $block_counts[$block_id]->count > 0) {
                $blocks_with_counts[] = array(
                    'id' => $block_id,
                    'title' => $block->post_title,
                    'count' => $block_counts[$block_id]->count,
                    'permalink' => get_permalink($block_id)
                );
            }
        }

        // Sort blocks by count (highest first)
        usort($blocks_with_counts, function ($a, $b) {
            return $b['count'] - $a['count'];
        });
    }

    // Get streets by district_id
    $streets = get_posts(array(
        'post_type' => 'street',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
        'suppress_filters' => false,
        'meta_query' => array(
            array(
                'key' => 'district_id',
                'value' => $district_id
            )
        )
    ));

    // Get all street IDs
    $street_ids = wp_list_pluck($streets, 'ID');

    // If no streets found, set empty array
    $streets_with_counts = array();

    if (!empty($street_ids)) {
        // Get counts for all streets in a single query
        $street_counts = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT pm.meta_value AS street_id, COUNT(p.ID) AS count
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
                WHERE p.post_type = %s
                AND p.post_status = 'publish'
                AND pm.meta_key = '_office_street'
                AND pm.meta_value IN (" . implode(',', array_fill(0, count($street_ids), '%d')) . ")
                GROUP BY pm.meta_value",
                array_merge(array($chu_de), $street_ids)
            ),
            OBJECT_K
        );

        // Create streets with counts array
        foreach ($streets as $street) {
            $street_id = $street->ID;
            if (isset($street_counts[$street_id]) && $street_counts[$street_id]->count > 0) {
                $streets_with_counts[] = array(
                    'id' => $street_id,
                    'title' => $street->post_title,
                    'count' => $street_counts[$street_id]->count,
                    'permalink' => get_permalink($street_id)
                );
            }
        }

        // Sort streets by count (highest first)
        usort($streets_with_counts, function ($a, $b) {
            return $b['count'] - $a['count'];
        });
    }
}

?>

<div class="scroll-section filter-nav bg-primary-1/5 py-10" data-title="Tìm kiếm">
    <div class="container">

        <?php get_template_part('components/filter/filter-archive'); ?>
    </div>
</div>


<?php echo display_rating_html(get_the_ID(), true); ?>


<section class="scroll-section district-1 section pt-10 xl:pb-20" data-title="Tổng quan">
    <div class="container">
        <h2 class="block-title"><?php echo $page_title; ?></h2>
        <div class="description mt-5 font-medium space-y-4">
            <?php echo $page_description; ?>
        </div>
        <div class="filter flex items-center gap-2 sm:gap-5 mt-10 flex-wrap">
            <div class="label font-bold text-black">Sắp xếp theo</div>
            <select name="sort" id="sort" onchange="window.location.href = '<?php echo esc_js(add_query_arg(array('paged' => false), get_permalink())); ?>?sort=' + this.value">
                <option value="default" <?php selected($sort, 'default'); ?>>Mặc định</option>
                <option value="price-asc" <?php selected($sort, 'price-asc'); ?>>Giá từ thấp đến cao</option>
                <option value="price-desc" <?php selected($sort, 'price-desc'); ?>>Giá từ cao đến thấp</option>
            </select>
        </div>
        <div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 base-gap mt-10">
            <?php
            if ($office_rent_query->have_posts()) :
                while ($office_rent_query->have_posts()) : $office_rent_query->the_post();
                    $post_office_id = get_the_ID();
                    get_template_part('components/boxProduct/box-rent-1', '', array('post_id' => $post_office_id));

                endwhile;
            else :
                echo '<div class="col-span-full text-center">' . __('Không tìm thấy kết quả nào', 'canhcamtheme') . '</div>';
            endif;
            ?>

        </div>
        <?php echo wp_bootstrap_pagination(array("custom_query" => $office_rent_query)); ?>
    </div>
</section>

<section class="distric-2 section">
    <?php if (!empty($wards_with_counts)) { ?>
        <div class="scroll-section distric-item odd:bg-primary-1/5 section xl:py-20" data-title="Tìm văn phòng theo phường liên quan">
            <div class="container">
                <h2 class="block-title"><?php echo __('Tìm văn phòng theo phường', 'canhcamtheme'); ?></h2>
                <div class="list keep-ul-disc">
                    <ul class="grid grid-cols-2 lg:grid-cols-4 base-gap gap-y-4 mt-10">
                        <?php foreach ($wards_with_counts as $ward) : ?>
                            <li>
                                <a href="<?php echo $ward['permalink']; ?>">
                                    <?php echo $ward['title']; ?> <span>(<?php echo $ward['count']; ?>)</span>
                                </a>
                            </li>
                        <?php endforeach; ?>

                        <?php if (empty($wards_with_counts)) : ?>
                            <li><?php _e('Không tìm thấy phường nào', 'canhcamtheme'); ?></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>

    <?php } ?>

    <?php if (!empty($blocks_with_counts)) { ?>
        <div class="scroll-section distric-item odd:bg-primary-1/5 section xl:py-20" data-title="Tìm văn phòng theo khu vực">
            <div class="container">
                <h2 class="block-title"><?php echo __('Tìm văn phòng theo khu vực', 'canhcamtheme'); ?></h2>
                <div class="list keep-ul-disc">
                    <ul class="grid grid-cols-2 lg:grid-cols-4 base-gap gap-y-4 mt-10">
                        <?php

                        foreach ($blocks_with_counts as $block) : ?>
                            <li>
                                <a href="<?php echo $block['permalink']; ?>">
                                    <?php echo $block['title']; ?> <span>(<?php echo $block['count']; ?>)</span>
                                </a>
                            </li>
                        <?php endforeach; ?>

                        <?php if (empty($blocks_with_counts)) : ?>
                            <li><?php _e('Không tìm thấy khu vực nào', 'canhcamtheme'); ?></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    <?php } ?>

    <?php if (!empty($streets_with_counts)) { ?>
        <div class="scroll-section distric-item odd:bg-primary-1/5 section xl:py-20" data-title="Tìm văn phòng theo đường liên quan">
            <div class="container">
                <h2 class="block-title"><?php echo __('Tìm văn phòng theo đường', 'canhcamtheme'); ?></h2>
                <div class="list keep-ul-disc">
                    <ul class="grid grid-cols-2 lg:grid-cols-4 base-gap gap-y-4 mt-10">
                        <?php foreach ($streets_with_counts as $street) : ?>
                            <li>
                                <a href="<?php echo $street['permalink']; ?>">
                                    <?php echo $street['title']; ?> <span>(<?php echo $street['count']; ?>)</span>
                                </a>
                            </li>
                        <?php endforeach; ?>

                        <?php if (empty($streets_with_counts)) : ?>
                            <li><?php _e('Không tìm thấy đường phố nào', 'canhcamtheme'); ?></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    <?php } ?>


</section>


<?php
/*     $page_title_rank = get_field('page_title_rank', $post_parent_id);
        $page_description_rank = get_field('page_content_rank', $post_parent_id);
        if ($page_title_rank) {
        ?>
    <section class="scroll-section cities-4 section xl:pt-20 pb-0 overflow-hidden" data-title="<?php echo $page_title_rank; ?>">
        <div class="container">
            <h2 class="block-title"><?php echo $page_title_rank; ?></h2>
            <div class="description space-y-4 mt-5">
                <?php echo $page_description_rank; ?>
            </div>

            <!-- Show ở trang Văn Phòng Cho Thuê-->
            <?php
            if ($chu_de == 'office-rent') {
            ?>
                <div class="office-gallery">
                    <div class="swiper-column-auto relative auto-4-column mt-10 visible-slide allow-mouseWheel  allow-touchMove">
                        <div class="swiper">
                            <div class="swiper-wrapper">
                                <?php
                                foreach ($list_building_classes as $building_class) {
                                    $building_class_title = get_the_title($building_class->ID);
                                    $building_class_image = get_the_post_thumbnail_url($building_class->ID, 'full');
                                    $building_class_excerpt = get_the_excerpt($building_class->ID);
                                ?>
                                    <div class="swiper-slide">
                                        <div class="item relative text-white rounded-4 overflow-hidden group">
                                            <div class="img"><a href="<?php echo get_the_permalink($building_class->ID); ?>" class="ratio-[372/320]"><img class="lozad" data-src="<?php echo $building_class_image; ?>" /></a>
                                            </div>
                                            <div class="content absolute bottom-0 left-0 w-full p-5 z-1">
                                                <div class="title subheader-20 font-bold"><?php echo $building_class_title; ?></div>
                                            </div>
                                            <div class="hover-content absolute transition-all duration-500 bg-primary-1/80 rem:backdrop-blur-[10px] group-hover:top-0 w-full h-full top-full left-0 z-1 flex flex-col before:static before:flex-1 after:static after:flex-1 p-5">
                                                <div class="title subheader-20 font-bold"><a href="<?php echo get_the_permalink($building_class->ID); ?>"><?php echo $building_class_title; ?></a></div>
                                                <div class="ctn font-medium overflow-auto rem:mt-2.5">
                                                    <?php echo $building_class_excerpt; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php
                                }
                                ?>
                            </div>
                        </div>
                        <div class="swiper-scrollbar"></div>
                    </div>
                </div>
            <?php
            }
            ?>

            <!-- Show ở trang Văn Phòng Trọn Gói-->
            <?php
            if ($chu_de == 'office-rent-full') {
                $page_logo_rank = get_field('page_logo_rank', $post_parent_id);
            ?>
                <div class="list-logos grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6 mt-10">
                    <?php
                    foreach ($page_logo_rank as $key => $value) {
                    ?>
                        <div class="img zoom-img rounded-2 border border-enutral-100 overflow-hidden transition-all duration-300">
                            <a class="ratio-[112/200] ratio-contain" href="<?php echo !empty($value['url']) ? $value['url'] : '#'; ?>" target="_blank">
                                <?php if (!empty($value['logo'])) { ?>
                                    <img class="lozad" data-src="<?php echo $value['logo']['url']; ?>" />
                                <?php } ?>
                            </a>
                        </div>
                    <?php
                    }
                    ?>
                </div>

                <a class="btn mx-auto mt-10 btn-primary "><?php echo __('Xem thêm', 'canhcamtheme'); ?><i class="fa-solid fa-plus"></i></a>
            <?php
            }
            ?>
        </div>
    </section>
<?php
        } */
?>


<?php

$content_post = get_post($post_parent_id);

if (!empty($content_post)) {
?>
    <?php
    $content = $content_post->post_content;

    ?>
    <section class="cites-5 section pb-0">
        <div class="container">
            <div class="section bg-primary-1/5 rounded-4 px-6 md:rem:px-[60px]">
                <div class="wrap-show-content">
                    <div class="show-content expand-content rem:max-h-[240px] overflow-hidden">
                        <article class="prose font-medium js-toc-content">
                            <?php
                            echo replace_toc_tag($content);
                            ?>
                        </article>
                    </div><a class="btn btn-expand mx-auto mt-4 btn-primary "><span><?php echo __('Xem thêm', 'canhcamtheme'); ?></span><span><?php echo __('Thu gọn', 'canhcamtheme'); ?></span><i class="fa-light fa-plus"></i></a>
                </div>
            </div>
        </div>
    </section>
<?php
}
?>



<?php
if ($list_faq) {
?>
    <section class="scroll-section cities-6 section xl:pt-20 pb-0" data-title="Câu hỏi thường gặp">
        <div class="container">

            <div class="grid grid-cols-12 base-gap">
                <div class="col-left col-span-full lg:col-span-4">
                    <div class="wrapper xl:rem:max-w-[360px]">
                        <h2 class="block-title"><?php echo $page_title_faq; ?></h2>
                        <div class="description font-medium mt-5"><?php echo $page_description_faq; ?></div>
                    </div>
                </div>
                <div class="col-right col-span-full lg:col-span-8">
                    <div class="module-toggle list space-y-5" data-open-atleast-one="true" data-open-first-item="true">
                        <?php
                        foreach ($list_faq as $key => $faq) {

                            $faq_title = get_the_title($faq->ID);

                        ?>
                            <div class="item border border-transparent rounded-2 bg-neutral-50 transition-all duration-500">
                                <div class="top body-18 sm:subheader-20 font-bold flex items-center gap-5 p-5 transition-all duration-500">
                                    <div class="id clamp:min-w-5"><?php echo $key + 1; ?></div>
                                    <div class="title flex-1"><?php echo $faq_title; ?></div>
                                    <div class="icon relative"><i class="fa-solid fa-plus"></i>
                                        <i class="fa-solid fa-minus"></i>
                                    </div>
                                </div>
                                <div class="bottom pb-8 px-5 hidden">
                                    <div class="description simple-prose">
                                        <?php echo wpautop($faq->post_content); ?>
                                    </div>
                                </div>
                            </div>
                        <?php
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php

}




$page_description = get_field('page_content_office', $post_parent_id);
?>

<section class="scroll-section cities-1 section pt-10 xl:pb-20" data-title="<?php echo __('Văn phòng cho thuê tại các quận khác', 'canhcamtheme') ?>">
    <div class="container">
        <h2 class="block-title"><?php echo __('Văn phòng cho thuê tại các quận khác', 'canhcamtheme') ?></h2>
        <div class="description mt-5 font-medium space-y-4">
            <?php echo $page_description; ?>
        </div>
        <?php
        if ($children) {
        ?>
            <div class="wrapper" has-expand-btn item-count="12" item-expand="12">
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6 mt-10">

                    <?php
                    foreach ($children as $child) {
                        $district_id = get_field('district_id', $child->ID);
                        // OPTIMIZED: Use the pre-fetched counts instead of running a new query
                        $building_count = isset($district_counts[$district_id]) ? $district_counts[$district_id] : 0;

                        if ($child->ID != $post_id) {
                            if ($building_count > 0) {
                    ?>
                                <div class="expand-item hidden">
                                    <div class="product-cat-1 zoom-img relative text-white rounded-4 overflow-hidden item">
                                        <div class="img">
                                            <a href="<?php echo get_the_permalink($child->ID); ?>" class="ratio-[1/1]">
                                                <img class="lozad" data-src="<?php echo (has_post_thumbnail($child->ID)) ? get_the_post_thumbnail_url($child->ID, 'full') : 'https://picsum.photos/1600/900' ?>" />
                                            </a>
                                        </div>
                                        <div class="content absolute bottom-0 left-0 w-full flex items-end z-1 pointer-events-none">
                                            <div class="wrapper w-full p-4">
                                                <div class="title sm:body-18 lg:subheader-20 font-bold"><span class="line-clamp-2"><?php echo get_the_title($district_id); ?></span></div>
                                                <div class="ctn line-clamp-1 mt-2 sm:body-16 body-14"><?php echo $building_count; ?>+ <?php echo __('tòa nhà', 'canhcamtheme'); ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                    <?php
                            }
                        }
                    }
                    ?>
                </div>
                <a class="btn mx-auto mt-10 btn-primary expand-btn"><?php echo __('Xem thêm', 'canhcamtheme'); ?><i class="fa-solid fa-plus"></i></a>
            </div>
        <?php
        }
        ?>

    </div>
</section>

<?php get_template_part('components/product/box-footer-product'); ?>



<?php
get_footer();
