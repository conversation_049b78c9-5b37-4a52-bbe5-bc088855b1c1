<?php

/**
 * Template Name: Page - Calculator
 */

global $post;

get_header();

?>

<section class="calculator section xl:pb-20 pt-10">
    <div class="container">
        <div class="grid lg:grid-cols-2 base-gap">
            <div class="col-left xl:-mr-10">
                <div class="img"><a class="ratio-[570/720] rounded-4"><img class="lozad" src="<?php echo get_the_post_thumbnail_url($post, 'full') ?>" data-loaded="true" alt="<?php echo get_the_title($post) ?>"></a>
                </div>
            </div>
            <div class="col-right xl:pl-20">
                <div class="block-title">tính toán diện tích</div>
                <form class="form-tinh-dien-tich" id="tinhDienTichForm" action="#" method="post">
                    <div class="form-wrap form-wrap-layout mt-8 space-y-4 md:space-y-8">
                        <div class="form-group form-required">
                            <label><?php _e('Số lượng nhân viên', 'canhcam_theme'); ?>
                                <input type="number" name="so-luong" id="so-luong" class="so-luong" min="0" max="1000" value="10" placeholder="<?php _e('Số lượng nhân viên', 'canhcam_theme'); ?>">
                            </label>
                            <!-- <span class="error-message"><?php _e('Vui lòng điền số lượng nhân viên', 'canhcam_theme'); ?></span> -->
                        </div>
                        <div class="form-group form-required">
                            <label><?php _e('Mức diện tích', 'canhcam_theme'); ?>
                                <select name="muc-dien-tich" class="muc-dien-tich">
                                    <option value="9"><?php _e('Văn phòng hạng A: 9m2/người', 'canhcam_theme'); ?></option>
                                    <option value="7"><?php _e('Văn phòng hạng B: 7m2/người', 'canhcam_theme'); ?></option>
                                    <option value="4"><?php _e('Văn phòng hạng C: 4m2/người', 'canhcam_theme'); ?></option>
                                </select>
                            </label>
                        </div>
                        <div class="form-group form-required">
                            <label><?php _e('Phòng họp', 'canhcam_theme'); ?>
                                <select name="phong-hop" class="phong-hop">
                                    <option value="0"><?php _e('Không có phòng họp: 0', 'canhcam_theme'); ?></option>
                                    <option value="8"><?php _e('Phòng họp nhỏ (4-6 người): 8m2', 'canhcam_theme'); ?></option>
                                    <option value="15"><?php _e('Phòng họp trung (8-10 người): 15m2', 'canhcam_theme'); ?></option>
                                    <option value="30"><?php _e('Phòng họp lớn (12 - 15 người): 30m2', 'canhcam_theme'); ?></option>
                                </select>
                            </label>
                        </div>
                        <div class="form-group form-required">
                            <label><?php _e('Pantry', 'canhcam_theme'); ?>
                                <select name="pantry" class="pantry">
                                    <option value="0"><?php _e('Không có: 0m2', 'canhcam_theme'); ?></option>
                                    <option value="5"><?php _e('Cơ bản: 5m2', 'canhcam_theme'); ?></option>
                                    <option value="10"><?php _e('Tiêu chuẩn: 10m2', 'canhcam_theme'); ?></option>
                                </select>
                            </label>
                        </div>
                        <div class="form-submit">
                            <button type="button" onclick="tinhDienTichThue()" class="btn btn-primary js-tdt-submit"><?php _e('Gửi', 'canhcam_theme'); ?> <i class="fa-solid fa-right-long"></i></button>
                        </div>
                </form>
            </div>
        </div>
    </div>
    <div class="results section pb-0 xl:pt-20">
        <h2 class="block-title">Đề xuất diện tích thuê văn phòng là&nbsp;<span class="js-result">~0m2</span></h2>
        <div class="mt-5 description font-medium text-justify">* Bảng tính toán và gợi ý văn phòng mang tính chất tương đối. Xin mời Quý công ty tham khảo trước và Saigon Office sẽ tư vấn chính xác theo từng trường hợp cụ thể qua <br>Hotline 0908 600 359.</div>
        <div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 base-gap mt-10 office-listings">
            <!-- Office listings will be loaded here via Ajax -->
        </div>

        <!-- <a class="btn mx-auto mt-10 btn-primary "><?php _e('Xem thêm', 'canhcam_theme'); ?><i class="fa-solid fa-right-long"></i></a> -->
    </div>
    </div>
</section>

<?php get_footer(); ?>

<script>
    function tinhDienTichThue() {
        const soLuong = parseFloat(document.querySelector('.so-luong').value) || 0;
        const mucDienTich = parseFloat(document.querySelector('.muc-dien-tich').value) || 0;
        const phongHop = parseFloat(document.querySelector('.phong-hop').value) || 0;
        const pantry = parseFloat(document.querySelector('.pantry').value) || 0;

        // Tính tổng diện tích
        const tongDienTich = (soLuong * mucDienTich) + phongHop + pantry;

        // Hiển thị kết quả
        document.querySelector('.js-result').innerHTML = `${tongDienTich}m<sup>2</sup>`;

        // Gọi Ajax để lấy danh sách văn phòng phù hợp
        fetchOfficeListings(tongDienTich, soLuong, mucDienTich, phongHop, pantry);
    }

    function fetchOfficeListings(totalArea, soLuong, mucDienTich, phongHop, pantry) {
        // Hiển thị trạng thái đang tải
        const officeListingsContainer = document.querySelector('.office-listings');
        officeListingsContainer.innerHTML = '<div class="col-span-full text-center">Đang tìm kiếm văn phòng phù hợp...</div>';

        // Lấy các tham số để truyền vào AJAX request
        const data = {
            action: 'get_office_listings_by_area',
            dien_tich: totalArea,
            so_luong: soLuong,
            muc_dien_tich: mucDienTich,
            phong_hop: phongHop,
            pantry: pantry
        };

        // Thực hiện AJAX request
        fetch(compareData.ajax_url, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Cập nhật container với danh sách văn phòng
                    officeListingsContainer.innerHTML = data.data;
                    FE.lozadInit();
                } else {
                    officeListingsContainer.innerHTML = '<div class="col-span-full text-center">Không tìm thấy văn phòng phù hợp.</div>';
                }
            })
            .catch(error => {
                console.error('Error fetching office listings:', error);
                officeListingsContainer.innerHTML = '<div class="col-span-full text-center">Đã xảy ra lỗi khi tìm kiếm văn phòng.</div>';
            });
    }
</script>