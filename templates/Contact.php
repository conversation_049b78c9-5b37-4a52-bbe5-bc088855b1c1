<?php
/*
Template name: Template - <PERSON><PERSON><PERSON> hệ
*/
?>
<?php //$GLOBALS['CANHCAM']['bg_body'] = 'bg-primary-50'; 
?>
<?php get_header(); ?>





<?php global $post;
$title = get_field('title', $post);
$form_description = get_field('form_description', $post);
$form_shortcode = get_field('form_shortcode', $post);
$repeater = get_field('repeater', $post);
?>


<section class="contact section xl:py-20" tab-wrapper="parent">
    <div class="container">
        <div class="grid grid-cols-12 base-gap">
            <div class="col-left col-span-full lg:col-span-5">
                <h1 class="block-title"><?= $title ?></h1>
                <?php if ($repeater) : ?>
                    <div class="hidden">
                        <nav class="primary-nav justify-start mt-10 pb-6 border-b border-neutral-100">
                            <ul>
                                <?php foreach ($repeater as $key => $value): ?>
                                    <li tab-item="parent" tab-item-value="<?= $key ?>"><a><?= $value['title'] ?></a></li>
                                <?php endforeach; ?>
                            </ul>
                        </nav>
                    </div>
                    <div class="tab-content pt-5">

                        <?php foreach ($repeater as $key => $value): ?>
                            <?php
                            $infos = $value['infos'];
                            $iframe = $value['iframe'];
                            ?>
                            <div class="tab-content-item group" tab-content="parent" tab-content-value="<?= $key ?>">
                                <div class="title subheader-20 font-bold"><?= $value['sub_title'] ?></div>
                                <div class="infos mt-4 space-y-2">
                                    <?php foreach ($infos as $info): ?>
                                        <div class="info [&amp;_i]:text-primary-2 flex items-baseline gap-3 hover-a-tag"><?= $info['icon'] ?> <?= $info['content'] ?></div>
                                    <?php endforeach; ?>
                                </div>
                                <?php if ($iframe) : ?>
                                    <div class="ratio-[330/560] mt-5">
                                        <div class="ratio-frame rounded-3"><?= $iframe ?></div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
            <div class="col-right col-span-full lg:col-span-7">
                <div class="box bg-neutral-50 p-6 lg:p-12 h-full">
                    <div class="description text-center"><?= $form_description ?></div>
                    <!-- <div class="form-wrap form-wrap-layout">
                        <div class="form-group">
                            <label>Họ và tên/ Đơn vị
                                <input type="text" placeholder="Nhập họ và tên/ Đơn vị">
                            </label>
                        </div>
                        <div class="form-group">
                            <label>Email
                                <input type="text" placeholder="Nhập email">
                            </label>
                        </div>
                        <div class="form-group">
                            <label>Số điện thoại
                                <input type="text" placeholder="Nhập số điện thoại">
                            </label>
                        </div>
                        <div class="form-group col-span-full">
                            <label>Ghi chú thêm (Không bắt buộc)
                                <textarea placeholder="Bạn có yêu cầu đặc biệt nào không? (Diện tích, giá, tiện ích…)"></textarea>
                            </label>
                        </div>
                        <div class="form-submit col-span-full">
                            <button class="btn btn-primary mx-auto">Gửi yêu cầu <i class="fa-solid fa-right-long"></i></button>
                        </div>
                    </div> -->
                    <?= do_shortcode($form_shortcode) ?>
                </div>
            </div>
        </div>
    </div>
</section>


<?php get_footer(); ?>

<script>
    jQuery(function($) {
        // On load
        function setFirstLocation() {
            let title = '';
            if ($('.primary-nav li.active').length > 0) {
                title = $('.primary-nav li.active a').text();
            } else {
                title = $('.primary-nav li a').first().text();
            }
            $('input[name="location"]').val(title);
        }
        setFirstLocation()
        $(document).on('click', '.primary-nav li a', function() {
            var $this = $(this);
            const title = $this.text();
            $('input[name="location"]').val(title);
        });


        var wpcf7Elm = document.querySelectorAll('.wpcf7');
        wpcf7Elm.forEach(function() {
            this.addEventListener('wpcf7mailsent', function(event) {
                setTimeout(() => {
                    setFirstLocation()
                }, 100);
            }, false);
        })
    });
</script>