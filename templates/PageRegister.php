<?php
/*
Template Name: Register Page
*/
get_header();
$login_page = get_field('login_page', 'option');
$register_page = get_field('register_page', 'option');
$lost_password_page = get_field('lost_password_page', 'option');
$page_account = get_field('page_account', 'option');
?>

<section class="section xl:py-20">
    <div class="container">
        <div class="wrapper clamp:max-w-[1004px] w-full mx-auto rounded-4 bg-neutral-50 p-10">
            <h2 class="block-title text-center"><?php _e('Đăng ký', 'canhcamtheme'); ?></h2>
            <?php if (is_user_logged_in()) : ?>
                <p class="text-center mt-3"><?php _e('Bạn đã đăng nhập. <a href="' . wp_logout_url(home_url()) . '">Đ<PERSON>ng xuất</a>', 'canhcamtheme'); ?></p>
            <?php else : ?>
                <div id="register-message"></div>
                <form id="register-form" method="post">
                    <div class="form-wrap space-y-8">
                        <div class="form-group">
                            <label for="username"><?php _e('Tên đăng nhập', 'canhcamtheme'); ?></label>
                            <input type="text" name="username" id="username" placeholder="<?php echo __('Tên đăng nhập', 'canhcamtheme'); ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="email"><?php _e('Email', 'canhcamtheme'); ?></label>
                            <input type="email" name="email" id="email" placeholder="<?php echo __('Email', 'canhcamtheme'); ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="password"><?php _e('Mật khẩu', 'canhcamtheme'); ?></label>
                            <input type="password" name="password" id="password" placeholder="<?php echo __('Mật khẩu', 'canhcamtheme'); ?>" required>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary mx-auto"><?php _e('Đăng ký', 'canhcamtheme'); ?></button>
                        </div>
                        <div class="form-group text-center">
                            <?php _e('Đã có tài khoản? <a href="' . esc_url(get_permalink($login_page)) . '" style="color: #0e6b38;">Đăng nhập</a>', 'canhcamtheme'); ?>
                        </div>
                        <?php wp_nonce_field('register_action', 'register_nonce'); ?>
                    </div>
                </form>
            <?php endif; ?>
        </div>
    </div>
</section>

<style>
    #register-message {
        margin-bottom: 15px;
    }

    .error {
        color: red;
    }

    .success {
        color: green;
    }

    form p {
        margin-bottom: 15px;
    }

    label {
        display: block;
        margin-bottom: 5px;
    }

    input[type="text"],
    input[type="email"],
    input[type="password"] {
        width: 100%;
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }

    input[type="submit"] {
        background: #0073aa;
        color: #fff;
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    input[type="submit"]:hover {
        background: #005177;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const registerForm = document.getElementById('register-form');
        if (registerForm) {
            registerForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const messageDiv = document.getElementById('register-message');
                messageDiv.innerHTML = '<p>Đang xử lý...</p>';

                const formData = new FormData(registerForm);
                formData.append('action', 'custom_register');

                fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            messageDiv.innerHTML = '<p class="success">' + data.message + '</p>';
                            setTimeout(() => {
                                window.location.href = '<?php echo get_permalink($page_account); ?>';
                            }, 1000);
                        } else {
                            messageDiv.innerHTML = '<p class="error">' + data.message + '</p>';
                        }
                    })
                    .catch(error => {
                        messageDiv.innerHTML = '<p class="error">Đã có lỗi xảy ra. Vui lòng thử lại.</p>';
                    });
            });
        }
    });
</script>

<?php
get_footer();
?>