<?php


/**
 * Template Name: Page - Search
 */

$keyword = isset($_GET['search']) ? $_GET['search'] : '';
$paged = isset($_GET['paged']) ? $_GET['paged'] : 1;
// $postTypes = ['office-rent', 'office-rent-full', 'business-space', 'real-estate'];
$postTypes = ['office-rent', 'office-rent-full'];

$arg_district = array(
    'post_type' => 'district',
    'posts_per_page' => -1,
    'suppress_filters' => false,
    // 'meta_query' => array(
    //     array(
    //         'key' => 'check_search',
    //         'value' => '1',
    //         'compare' => '=',
    //     ),
    // ),
);
$districts = new WP_Query($arg_district);

// Function to create property query
function create_property_query($keyword, $paged, $postTypes, $district_id = null)
{
    $meta_query = array();

    $args = array(
        'post_type' => $postTypes,
        'posts_per_page' => 12,
        'paged' => $paged,
        'suppress_filters' => false,
        'post_title_like' => $keyword,
    );

    if (isset($_GET['city']) && !empty($_GET['city'])) {
        $meta_query[] = array(
            'key' => '_office_city',
            'value' => $_GET['city'],
            'compare' => '=',
        );
    }

    if ($district_id) {
        $meta_query[] = array(
            'key' => '_office_district',
            'value' => $district_id,
            'compare' => '=',
        );
    } else if (isset($_GET['district']) && !empty($_GET['district'])) {
        $meta_query[] = array(
            'key' => '_office_district',
            'value' => $_GET['district'],
            'compare' => '=',
        );
    }

    if (isset($_GET['ward']) && !empty($_GET['ward'])) {
        $meta_query[] = array(
            'key' => '_office_ward',
            'value' => $_GET['ward'],
            'compare' => '=',
        );
    }

    if (isset($_GET['street']) && !empty($_GET['street'])) {
        $meta_query[] = array(
            'key' => '_office_street',
            'value' => $_GET['street'],
            'compare' => '=',
        );
    }

    if (isset($_GET['block']) && !empty($_GET['block'])) {
        $meta_query[] = array(
            'key' => '_office_block',
            'value' => $_GET['block'],
            'compare' => '=',
        );
    }

// Area filter using direct area_from and area_to parameters or select-area
if (isset($_GET['area_from']) || isset($_GET['area_to']) || isset($_GET['select-area'])) {
    $min_area = isset($_GET['area_from']) && is_numeric($_GET['area_from']) ? floatval($_GET['area_from']) : 0;
    $max_area = isset($_GET['area_to']) && is_numeric($_GET['area_to']) ? floatval($_GET['area_to']) : 2000;

    // Handle select-area parameter (format: "0,100")
    if (isset($_GET['select-area']) && !empty($_GET['select-area'])) {
        $area_parts = explode(',', $_GET['select-area']);
        if (count($area_parts) == 2) {
            $min_area = is_numeric($area_parts[0]) ? floatval($area_parts[0]) : $min_area;
            $max_area = is_numeric($area_parts[1]) ? floatval($area_parts[1]) : $max_area;
        }

        // Sử dụng relation OR để kết hợp các điều kiện
        $area_seats_query = array(
            'relation' => 'OR',
            // Điều kiện cho diện tích (area) - dùng cho office-rent
            array(
                'key' => 'numeric_area',
                'value' => array($min_area, $max_area),
                'compare' => 'BETWEEN',
                'type' => 'NUMERIC'
            ),
            // Điều kiện cho số chỗ ngồi (seats) - dùng cho office-rent-full
            array(
                'key' => 'numeric_seats',
                'value' => array($min_area, $max_area),
                'compare' => 'BETWEEN',
                'type' => 'NUMERIC'
            )
        );

        // Thêm vào meta_query chính
        $meta_query[] = $area_seats_query;
    }
}

    // Price filter using price_from, price_to or select-price parameters
    if (isset($_GET['price_from']) || isset($_GET['price_to']) || isset($_GET['select-price'])) {
        $min_price = isset($_GET['price_from']) && is_numeric($_GET['price_from']) ? floatval($_GET['price_from']) : 0;
        $max_price = isset($_GET['price_to']) && is_numeric($_GET['price_to']) ? floatval($_GET['price_to']) : 45;

        // Handle select-price parameter (format: "15,20")
        if (isset($_GET['select-price']) && !empty($_GET['select-price'])) {
            $price_parts = explode(',', $_GET['select-price']);
            if (count($price_parts) == 2) {
                $min_price = is_numeric($price_parts[0]) ? floatval($price_parts[0]) : $min_price;
                $max_price = is_numeric($price_parts[1]) ? floatval($price_parts[1]) : $max_price;
            }
        }

        $price_meta_query = array('relation' => 'OR');

        // Check both possible price fields
        $price_meta_query[] = array(
            'key' => 'price_product',
            'value' => array($min_price, $max_price),
            'type' => 'NUMERIC',
            'compare' => 'BETWEEN'
        );

        $price_meta_query[] = array(
            'key' => 'dynamic_field_gia_van_phong_gia-van-phong',
            'value' => array($min_price, $max_price),
            'type' => 'NUMERIC',
            'compare' => 'BETWEEN'
        );

        $meta_query[] = $price_meta_query;
    }

    if (isset($_GET['select-direction']) && !empty($_GET['select-direction'])) {
        $orientation_meta_query = array('relation' => 'OR');

        $orientation_meta_query[] = array(
            'key' => 'dynamic_field_ts_toa_nha_tron_goi_huong-toa-nha',
            'value' => $_GET['select-direction'],
            'compare' => 'LIKE',
        );

        $orientation_meta_query[] = array(
            'key' => 'dynamic_field_thong_so_toa_nha_huong-toa-nha',
            'value' => $_GET['select-direction'],
            'compare' => 'LIKE',
        );

        $meta_query[] = $orientation_meta_query;
    }

    if (isset($_GET['select-type']) && !empty($_GET['select-type']) && $_GET['select-type'] !== 'all') {
        $class_meta_query = array('relation' => 'OR');

        $class_meta_query[] = array(
            'key' => 'dynamic_field_ts_toa_nha_tron_goi_hang-toa-nha',
            'value' => $_GET['select-type'],
            'compare' => 'LIKE',
        );

        $class_meta_query[] = array(
            'key' => 'dynamic_field_thong_so_toa_nha_hang-toa-nha',
            'value' => $_GET['select-type'],
            'compare' => 'LIKE',
        );

        $meta_query[] = $class_meta_query;
    }

    if (count($meta_query) > 0) {
        $args['meta_query'] = array(
            'relation' => 'AND',
            $meta_query
        );
    }



    return new WP_Query($args);
}

// Create main query based on parameters
$has_district_param = isset($_GET['district']) && !empty($_GET['district']);

// If district parameter is provided, create a single query for that district
if ($has_district_param) {
    $offices = create_property_query($keyword, $paged, $postTypes);
} else {
    // If no district parameter but has city, we'll query per district later
    $offices = isset($_GET['city']) && !empty($_GET['city']) ? null : create_property_query($keyword, $paged, $postTypes);
}

get_header();

if (isset($_GET['city']) && !empty($_GET['city'])) {
    $city = get_post($_GET['city']);
    $city_name = $city->post_title;
}

$total_displayed = 0;
if ($has_district_param || !isset($_GET['city']) || empty($_GET['city'])) {
    // Nếu chọn quận cụ thể hoặc không chọn city, hiển thị số lượng kết quả thực tế
    $total_displayed = $offices ? $offices->found_posts : 0;
} else {
    // Nếu tìm theo city, tính tổng số kết quả từ mỗi quận (tối thiểu 12 bài mỗi quận)
    if ($districts->have_posts()) {
        while ($districts->have_posts()) {
            $districts->the_post();
            $district_id = get_the_ID();
            
            // Tạo query cho mỗi quận để đếm số lượng bài viết
            $district_properties = create_property_query($keyword, 1, $postTypes, $district_id);
            
            // Chỉ tính các quận có bài viết
            if ($district_properties->have_posts()) {
                // Mỗi quận hiển thị tối thiểu 12 bài, hoặc số lượng thực tế nếu ít hơn
                $total_displayed += min(12, $district_properties->found_posts);
            }
        }
        // Reset post data sau khi loop
        wp_reset_postdata();
    }
}
?>


<section class="search-results">
    <div class="container">
        <div class="search-header py-10 subheader-20 font-bold [&_span]:text-primary-2">
            <?php echo __('Hiển thị', 'canhcamtheme'); ?>
            <span><?php echo $total_displayed; ?> </span>
            <?php echo __('trong số', 'canhcamtheme'); ?>
            <span><?php echo wp_count_posts('office-rent')->publish + wp_count_posts('office-rent-full')->publish + wp_count_posts('business-space')->publish + wp_count_posts('real-estate')->publish; ?></span>
            <?php echo __('nhà đất xác thực', 'canhcamtheme'); ?>
        </div>
    </div>
    <div class="search-results-list">
        <?php if ($has_district_param || !isset($_GET['city']) || empty($_GET['city'])) { ?>
            <!-- Display results without district grouping -->
            <div class="search-results-category even:bg-primary-1/5 section xl:py-20 first:pt-0">
                <div class="container">
                    <?php if ($city_name) { ?>
                        <div class="block-title"><?php echo $city_name; ?></div>
                    <?php } else {
                        echo '<div class="block-title">' . __('Kết quả tìm kiếm', 'canhcamtheme') . '</div>';
                    } ?>

                    <div class="results-list mt-5">
                        <?php
                        // Display non-grouped property results
                        if ($offices && $offices->have_posts()) {
                            echo '<div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 base-gap mt-10" bis_skin_checked="1">';

                            while ($offices->have_posts()) {
                                $offices->the_post();
                                $post_id = get_the_ID();
                                // Property display code here
                                get_template_part('components/boxProduct/box-rent-1', '', array('post_id' => $post_id));

                            }
                            echo '</div>';
                            wp_reset_postdata();
                        } else {
                            echo __('<p>No properties found.</p>', 'canhcamtheme');
                        }
                        ?>
                    </div>
                </div>
            </div>
        <?php } else { ?>
            <!-- Display results grouped by district -->
            <div class="search-results-category even:bg-primary-1/5 section xl:py-20 first:pt-0">
                <div class="container">
                    <?php if ($city_name) { ?>
                        <div class="block-title"><?php echo $city_name; ?></div>
                    <?php } ?>

                    <div class="results-list mt-5">
                        <?php
                        if ($districts->have_posts()) {
                            while ($districts->have_posts()) {
                                $districts->the_post();
                                $district_id = get_the_ID();

                                // Create a query for each district
                                $district_properties = create_property_query($keyword, $paged, $postTypes, $district_id);

                                // Only show districts that have properties
                                if ($district_properties->have_posts()) {
                        ?>
                                    <div class="results-item py-5 border-b border-primary-1 first:border-t last:border-b-0 space-y-5">
                                        <div class="title header-24 uppercase text-black font-heading">Văn phòng cho thuê <?php echo get_the_title(); ?></div>
                                        <div class="swiper-column-auto relative auto-4-column allow-touchMove">
                                            <div class="swiper">
                                                <div class="swiper-wrapper">
                                                    <?php
                                                    while ($district_properties->have_posts()) {
                                                        $district_properties->the_post();
                                                        $post_id = get_the_ID();
                                                        echo '<div class="swiper-slide">';
                                                        if (get_post_type($post_id) == 'office-rent-full') {
                                                            get_template_part('components/boxProduct/box-rent-2', '', array('post_id' => $post_id));
                                                        } else {
                                                            get_template_part('components/boxProduct/box-rent-1', '', array('post_id' => $post_id));
                                                        }
                                                        echo '</div>';
                                                    }
                                                    wp_reset_postdata();
                                                    ?>
                                                </div>
                                            </div>
                                            <div class="arrow-button">
                                                <div class="button-prev"></div>
                                                <div class="button-next"></div>
                                            </div>
                                        </div>
                                        <a href="<?php echo add_query_arg('district', $district_id, $_SERVER['REQUEST_URI']); ?>" class="btn mx-auto btn-primary"><?php echo __('Xem thêm', 'canhcamtheme'); ?><i class="fa-solid fa-right-long"></i></a>
                                    </div>
                        <?php
                                }
                            }
                            wp_reset_postdata();
                        }
                        ?>
                    </div>
                </div>
            </div>
        <?php } ?>
    </div>
</section>
<?php

get_footer();
