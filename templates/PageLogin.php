<?php
/*
Template Name: Login Page
*/
get_header();
$login_page = get_field('login_page', 'option');
$register_page = get_field('register_page', 'option');
$lost_password_page = get_field('lost_password_page', 'option');
?>

<section class="section xl:py-20">
    <div class="container">
        <div class="wrapper clamp:max-w-[1004px] w-full mx-auto rounded-4 bg-neutral-50 p-10">
            <h2 class="block-title text-center">Đăng nhập</h2>
            <?php if (is_user_logged_in()) : ?>
                <p class="text-center mt-3"><?php _e('Bạn đã đăng nhập. <a href="' . wp_logout_url(home_url()) . '">Đăng xuất</a>', 'canhcamtheme'); ?></p>
            <?php else : ?>
                <div id="login-message"></div>
                <form id="login-form" method="post">
                    <div class="form-wrap space-y-8">
                        <div class="form-group">
                            <label for="username"><?php _e('Tên đăng nhập hoặc Email', 'canhcamtheme'); ?></label>
                            <input type="text" name="username" id="username" placeholder="<?php echo __('Tên đăng nhập hoặc Email', 'canhcamtheme'); ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="password"><?php _e('Mật khẩu', 'canhcamtheme'); ?></label>
                            <input type="password" name="password" id="password" placeholder="<?php echo __('Mật khẩu', 'canhcamtheme'); ?>" required>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary mx-auto"><?php _e('Đăng nhập', 'canhcamtheme'); ?></button>
                        </div>
                        <div class="form-group text-center">
                            <a href="<?php echo esc_url(get_permalink($lost_password_page)); ?>"><?php _e('Quên mật khẩu?', 'canhcamtheme'); ?></a> |
                            <a href="<?php echo esc_url(get_permalink($register_page)); ?>"><?php _e('Đăng ký', 'canhcamtheme'); ?></a>
                        </div>
                        <?php wp_nonce_field('login_action', 'login_nonce'); ?>
                    </div>
                </form>
            <?php endif; ?>
        </div>
    </div>
</section>

<style>
    #login-message {
        margin-bottom: 15px;
    }

    .error {
        color: red;
    }

    .success {
        color: green;
    }

    form p {
        margin-bottom: 15px;
    }

    label {
        display: block;
        margin-bottom: 5px;
    }

    input[type="text"],
    input[type="password"] {
        width: 100%;
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }

    input[type="submit"] {
        background: #0073aa;
        color: #fff;
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    input[type="submit"]:hover {
        background: #005177;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const messageDiv = document.getElementById('login-message');
                messageDiv.innerHTML = '<p>Đang xử lý...</p>';

                const formData = new FormData(loginForm);
                formData.append('action', 'custom_login');

                fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            messageDiv.innerHTML = '<p class="success">' + data.message + '</p>';
                            setTimeout(() => {
                                window.location.href = '<?php echo home_url(); ?>';
                            }, 1000);
                        } else {
                            messageDiv.innerHTML = '<p class="error">' + data.message + '</p>';
                        }
                    })
                    .catch(error => {
                        messageDiv.innerHTML = '<p class="error">Đã có lỗi xảy ra. Vui lòng thử lại.</p>';
                    });
            });
        }
    });
</script>

<?php
get_footer();
?>