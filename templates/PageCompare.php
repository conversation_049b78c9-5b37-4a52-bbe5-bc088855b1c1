<?php

/*
Template Name: Page - Compare
*/

get_header();

$compare_list = get_compare_list();
// $test = find_or_create_location_term('Ho Chi Minh City', 'city', 'en');
// var_dump($test);

// Initialize variables
$thong_so_toa_nha = null;
$thong_tin_dien_tich_va_gia_thue = null;
$tien_ich_toa_nha = null;
$ts_toa_nha_tron_goi = null;
$dich_vu_tron_goi = null;
$tien_ich_tron_goi = null;

// Check post type in compare list
$is_office_rent_full = false;
if (!empty($compare_list)) {
    foreach ($compare_list as $post_id) {
        $post_type = get_post_type($post_id);
        if ($post_type === 'office-rent-full') {
            $is_office_rent_full = true;
            break;
        }
    }
}



// Get appropriate options based on post type
if ($is_office_rent_full) {
    // For office-rent-full, use tron_goi options
    $ts_toa_nha_tron_goi = get_field('ts_toa_nha_tron_goi', 'option');
    $dich_vu_tron_goi = get_field('dich_vu_tron_goi', 'option');
    $tien_ich_tron_goi = get_field('tien_ich_tron_goi', 'option');
} else {
    // For other post types, use regular options
    $thong_so_toa_nha = get_field('thong_so_toa_nha', 'option');
    $thong_tin_dien_tich_va_gia_thue = get_field('thong_tin_dien_tich_va_gia_thue', 'option');
    $tien_ich_toa_nha = get_field('tien_ich_toa_nha', 'option');
}

// foreach ($ts_toa_nha_tron_goi as $field) {
//     echo 'dynamic_field_ts_toa_nha_tron_goi_' . $field['item_key'] . '<br>';
// }
// foreach ($dich_vu_tron_goi as $field) {
//     echo 'dynamic_field_dich_vu_tron_goi_' . $field['item_key'] . '<br>';
// }
// foreach ($tien_ich_tron_goi as $field) {
//     echo 'dynamic_field_tien_ich_tron_goi_' . $field['item_key'] . '<br>';
// }

?>

<section class="compare section">
    <div class="container">
        <div class="table-wrapper">
            <?php
            if (count($compare_list) > 0) :

            ?>
                <table>
                    <thead>
                        <tr class="align-top">
                            <th class="header-40 font-bold uppercase text-primary-1 text-left font-heading bg-white big-title"><?php echo __('So sánh', 'canhcamtheme') ?></th>
                            <?php
                            if (!empty($compare_list)) :
                                foreach ($compare_list as $post_id) :
                                    $post = get_post($post_id);
                                    if ($post) :
                                        $title = $post->post_title;
                                        $featured_image = get_the_post_thumbnail_url($post_id, 'large') ?: 'https://picsum.photos/1600/900';
                            ?>
                                        <th class="text-left">
                                            <div class="product-title hidden"><?php echo $title ?></div>
                                            <div class="bpd-compare zoom-img border border-neutral-100 h-full flex flex-col">
                                                <div class="office-compare remove-btn" data-id="<?php echo $post_id; ?>"><i class="fa-regular fa-xmark"></i></div>
                                                <div class="img">
                                                    <a class="ratio-[234/360] bg-neutral-100">
                                                        <img class="lozad" data-src="<?php echo $featured_image; ?>" />
                                                    </a>
                                                </div>
                                                <div class="content pt-4 px-4 pb-6 flex-1 flex flex-col">
                                                    <div class="flex items-center justify-between gap-3 py-1">
                                                        <time class="body-14 text-primary-1"><?php echo get_the_date('d.m.Y', $post_id) ?></time>
                                                        <?php
                                                        if (is_post_favorite($post_id)) {
                                                            $class = 'is-favorite';
                                                        } else {
                                                            $class = '';
                                                        }
                                                        ?>
                                                        <div class="favorite <?php echo $class; ?>" data-id="<?php echo $post_id; ?>"><i class="fa-light fa-heart"></i></div>
                                                    </div>
                                                    <div class="title rem:mt-[5px] font-bold text-black flex-1"><a href="<?php echo get_the_permalink($post_id) ?>" class="line-clamp-2"><?php echo $title ?></a>
                                                    </div>
                                                    <a class="btn btn-light mt-3 btn-primary btn-contact-compare"
                                                        data-fancybox
                                                        data-src="#popup-contact"
                                                        data-popup="popup-contact-template"
                                                        data-post-id="<?php echo $post_id; ?>"
                                                        data-img="<?php echo $featured_image; ?>"
                                                        data-title="<?php echo $title ?>">
                                                        <?php echo __('Liên hệ đi xem', 'canhcamtheme') ?>
                                                    </a>
                                                </div>
                                            </div>
                                        </th>
                            <?php
                                    endif;
                                endforeach;
                            endif;
                            ?>
                        </tr>
                    </thead>
                    <?php if ($is_office_rent_full && $ts_toa_nha_tron_goi) : ?>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="title header-24 uppercase font-heading text-neutral-700 flex items-center gap-5 select-none">
                                        <i class="fa-regular fa-circle-chevron-down"></i> <?php echo __('THÔNG SỐ TÒA NHÀ', 'canhcamtheme') ?>
                                    </div>
                                </td>
                            </tr>
                            <?php foreach ($ts_toa_nha_tron_goi as $field) : ?>
                                <tr>
                                    <td><?php echo $field['ten_thong_so']; ?></td>
                                    <?php foreach ($compare_list as $post_id) :
                                        $value = get_field('dynamic_field_ts_toa_nha_tron_goi_' . $field['item_key'], $post_id);

                                        // Xử lý hiển thị cho trường hạng tòa nhà
                                        if ($field['item_key'] == 'hang-toa-nha' && !empty($value) && is_numeric($value)) {
                                            $post = get_post($value);
                                            if ($post) {
                                                $value = $post->post_title;
                                            }
                                        }

                                        // Xử lý hiển thị cho trường hướng tòa nhà
                                        if ($field['item_key'] == 'huong-toa-nha' && !empty($value)) {
                                            if (is_array($value)) {
                                                $titles = array();
                                                foreach ($value as $post_id_value) {
                                                    if (is_numeric($post_id_value)) {
                                                        $post = get_post($post_id_value);
                                                        if ($post) {
                                                            $titles[] = $post->post_title;
                                                        }
                                                    }
                                                }
                                                $value = !empty($titles) ? implode(', ', $titles) : $value;
                                            } elseif (is_numeric($value)) {
                                                $post = get_post($value);
                                                if ($post) {
                                                    $value = $post->post_title;
                                                }
                                            }
                                        }
                                    ?>
                                        <td><?php echo $value ? $value : '-'; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    <?php elseif (!$is_office_rent_full && $thong_so_toa_nha) : ?>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="title header-24 uppercase font-heading text-neutral-700 flex items-center gap-5 select-none">
                                        <i class="fa-regular fa-circle-chevron-down"></i> <?php echo __('THÔNG SỐ TÒA NHÀ', 'canhcamtheme') ?>
                                    </div>
                                </td>
                            </tr>
                            <?php foreach ($thong_so_toa_nha as $field) : ?>
                                <tr>
                                    <td><?php echo $field['ten_thong_so']; ?></td>
                                    <?php foreach ($compare_list as $post_id) :
                                        $value = get_field('dynamic_field_thong_so_toa_nha_' . $field['item_key'], $post_id);

                                        // Xử lý hiển thị cho trường hạng tòa nhà
                                        if ($field['item_key'] == 'hang-toa-nha' && !empty($value) && is_numeric($value)) {
                                            $post = get_post($value);
                                            if ($post) {
                                                $value = $post->post_title;
                                            }
                                        }

                                        // Xử lý hiển thị cho trường hướng tòa nhà
                                        if ($field['item_key'] == 'huong-toa-nha' && !empty($value)) {
                                            if (is_array($value)) {
                                                $titles = array();
                                                foreach ($value as $post_id_value) {
                                                    if (is_numeric($post_id_value)) {
                                                        $post = get_post($post_id_value);
                                                        if ($post) {
                                                            $titles[] = $post->post_title;
                                                        }
                                                    }
                                                }
                                                $value = !empty($titles) ? implode(', ', $titles) : $value;
                                            } elseif (is_numeric($value)) {
                                                $post = get_post($value);
                                                if ($post) {
                                                    $value = $post->post_title;
                                                }
                                            }
                                        }
                                    ?>
                                        <td><?php echo $value ? $value : '-'; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    <?php endif; ?>

                    <?php if ($is_office_rent_full && $dich_vu_tron_goi) : ?>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="title header-24 uppercase font-heading text-neutral-700 flex items-center gap-5 select-none">
                                        <i class="fa-regular fa-circle-chevron-down"></i> <?php echo __('DỊCH VỤ CHO THUÊ', 'canhcamtheme') ?>
                                    </div>
                                </td>
                            </tr>
                            <?php foreach ($dich_vu_tron_goi as $field) : ?>
                                <tr>
                                    <td><?php echo $field['ten_thong_so']; ?></td>
                                    <?php foreach ($compare_list as $post_id) :
                                        $value = get_field('dynamic_field_dich_vu_tron_goi_' . $field['item_key'], $post_id);
                                    ?>
                                        <td><?php echo $value ? $value : '-'; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    <?php elseif (!$is_office_rent_full && $thong_tin_dien_tich_va_gia_thue) : ?>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="title header-24 uppercase font-heading text-neutral-700 flex items-center gap-5 select-none">
                                        <i class="fa-regular fa-circle-chevron-down"></i> <?php echo __('DỊCH VỤ CHO THUÊ', 'canhcamtheme') ?>
                                    </div>
                                </td>
                            </tr>
                            <?php foreach ($thong_tin_dien_tich_va_gia_thue as $field) : ?>
                                <tr>
                                    <td><?php echo $field['ten_thong_so']; ?></td>
                                    <?php foreach ($compare_list as $post_id) :
                                        $value = get_field('dynamic_field_thong_tin_dien_tich_va_gia_thue_' . $field['item_key'], $post_id);
                                    ?>
                                        <td><?php echo $value ? $value : '-'; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    <?php endif; ?>

                    <?php if ($is_office_rent_full && $tien_ich_tron_goi) : ?>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="title header-24 uppercase font-heading text-neutral-700 flex items-center gap-5 select-none">
                                        <i class="fa-regular fa-circle-chevron-down"></i> <?php echo __('TIỆN ÍCH XUNG QUANH', 'canhcamtheme') ?>
                                    </div>
                                </td>
                            </tr>
                            <?php foreach ($tien_ich_tron_goi as $field) : ?>
                                <tr>
                                    <td><?php echo $field['ten_thong_so']; ?></td>
                                    <?php foreach ($compare_list as $post_id) :
                                        $value = get_field('dynamic_field_tien_ich_tron_goi_' . $field['item_key'], $post_id);
                                    ?>
                                        <td><?php echo $value ? $value : '-'; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    <?php elseif (!$is_office_rent_full && $tien_ich_toa_nha) : ?>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="title header-24 uppercase font-heading text-neutral-700 flex items-center gap-5 select-none">
                                        <i class="fa-regular fa-circle-chevron-down"></i> <?php echo __('TIỆN ÍCH XUNG QUANH', 'canhcamtheme') ?>
                                    </div>
                                </td>
                            </tr>
                            <?php foreach ($tien_ich_toa_nha as $field) : ?>
                                <tr>
                                    <td><?php echo $field['ten_thong_so']; ?></td>
                                    <?php foreach ($compare_list as $post_id) :
                                        $value = get_field('dynamic_field_tien_ich_toa_nha_' . $field['item_key'], $post_id);
                                    ?>
                                        <td><?php echo $value ? $value : '-'; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    <?php endif; ?>
                </table>
            <?php else : ?>
                <div class="text-center">
                    <p><?php echo __('Vui lòng chọn ít nhất 2 văn phòng để so sánh', 'canhcamtheme'); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>


<div class="hidden">
    <div id="popup-contact">
        <div class="grid md:grid-cols-2 items-center base-gap">
            <div class="img"><a class="ratio-[282/424] rounded-4"><img class="lozad" data-src="<?php echo get_the_post_thumbnail_url($post_id, 'full'); ?>" alt="<?php echo get_the_title($post_id); ?>" /></a>
            </div>
            <div class="content">
                <div class="block-title"><?php echo __('Bạn quan tâm đến', 'canhcamtheme'); ?></div>
                <div class="sub-title subheader-20 font-bold text-primary-2 mt-5"><?php echo get_the_title($post_id); ?></div>
                <div class="mt-3 ctn font-medium"><?php echo __('Vui lòng điền thông tin để nhận tư vấn nhanh hoặc đặt lịch xem thực tế', 'canhcamtheme'); ?></div>
            </div>
        </div>
        <div class="mt-8"></div>
        <?php echo do_shortcode('[contact-form-7 id="bd20446" title="Form Tư Vấn"]'); ?>
    </div>
</div>

<?php

get_footer();
