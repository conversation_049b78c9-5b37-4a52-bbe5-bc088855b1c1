<?php
function add_field_select_banner()
{
    acf_add_local_field_group(array(
        'key' => 'select_banner',
        'title' => 'Banner: Select Page',
        'fields' => array(),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'page',
                ),
            ),
            // Thêm taxonomy ở dưới
            array(
                array(
                    'param' => 'taxonomy',
                    'operator' => '==',
                    'value' => 'category'
                )
            ),
            array(
                array(
                    'param' => 'taxonomy',
                    'operator' => '==',
                    'value' => 'product-tax'
                )
            ),
            array(
                array(
                    'param' => 'taxonomy',
                    'operator' => '==',
                    'value' => 'recruit-tax'
                )
            ),
        ),
    ));
    acf_add_local_field(array(
        'key' => 'banner_select_page',
        'label' => 'Chọn banner hiển thị',
        'name' => 'Chọn banner hiển thị',
        'type' => 'post_object',
        'post_type' => 'banner',
        'multiple' => 1,
        'parent' => 'select_banner',
    ));
}
add_action('acf/init', 'add_field_select_banner');

function pp_create_post_type($args)
{

    if (!is_array($args) || !$args['post_type'] || !$args['name'] || !$args['single'] || !$args['slug']) return;

    $post_type = $args['post_type'];

    $name = $args['name'];

    $single = $args['single'];

    $icon = $args['icon'] ? $args['icon'] : "dashicons-star-filled";

    $archive = isset($args['archive']) ? $args['archive'] : true;

    $slug = $args['slug'];

    $rewrite = (isset($args['rewrite'])) ? $args['rewrite'] : $args['slug'];

    $supports = isset($args['supports']) ? $args['supports'] : array('title', 'editor', 'revisions', 'thumbnail', 'author', 'excerpt', 'comments');

    $public = isset($args['public']) ? $args['public'] : true;

    $capabilities = isset($args['capabilities']) ? $args['capabilities'] : array();

    $exclude_from_search = isset($args['exclude_from_search']) ? $args['exclude_from_search'] : true;

    $menu_position = isset($args['menu_position']) ? $args['menu_position'] : 6;

    register_post_type($post_type, array(
        'labels' => array(

            'name' => __($name, 'pp'),

            'singular_name' => __($single, 'pp'),

            'add_new' => __('Add New ' . $single, 'pp'),

            'add_new_item' => __('Add New ' . $single, 'pp'),


            'edit_item' => __('Edit ' . $single, 'pp'),


            'new_item' => __('New' . $single, 'pp'),


            'all_items' => __('All ' . $name, 'pp'),


            'view_item' => __('View ' . $single, 'pp'),


            'search_items' => __('Filter By ' . $name, 'pp'),


            'not_found' => __('Not Found ' . $single, 'pp'),


            'not_found_in_trash' => __('Not Found ' . $single . ' In Trash', 'pp'),


            'parent_item_colon' => '',


            'menu_name' => __($name, 'pp')


        ),


        'public' => $public,


        'exclude_from_search' => $exclude_from_search,


        'menu_position' => $menu_position,


        'menu_icon' => $icon,


        'has_archive' => $archive,


        'taxonomies' => array($post_type),


        'rewrite' => array('slug' => $rewrite),


        'publicly_queryable' => $public,


        'supports' => $supports,


        'capabilities' => $capabilities,


    ));
}
add_action('init', 'create_new_custom_post_type');


function create_new_custom_post_type()
{
    $args = array(
        // Nhóm bất động sản (menu_position: 10)
        array(
            "post_type" => 'office-rent',
            "name" => __('Văn phòng cho thuê', 'canhcamtheme'),
            "single" => __('Văn phòng cho thuê', 'canhcamtheme'),
            "slug" => "office-rent",
            "rewrite" => "office-rent",
            "supports" => array('title', 'thumbnail', 'excerpt'),
            "icon" => 'dashicons-admin-multisite',
            "archive" => false,
            "exclude_from_search" => false,
            "menu_position" => 10,
        ),
        array(
            "post_type" => 'office-rent-full',
            "name" => __('Văn phòng trọn gói', 'canhcamtheme'),
            "single" => __('Văn phòng trọn gói', 'canhcamtheme'),
            "slug" => "office-rent-full",
            "rewrite" => "office-rent-full",
            "supports" => array('editor', 'title', 'thumbnail', 'excerpt'),
            "icon" => 'dashicons-admin-multisite',
            "archive" => false,
            "exclude_from_search" => false,
            "menu_position" => 10,
        ),
        array(
            "post_type" => 'business-space',
            "name" => __('Mặt bằng kinh doanh', 'canhcamtheme'),
            "single" => __('Mặt bằng kinh doanh', 'canhcamtheme'),
            "slug" => "business-space",
            "rewrite" => "business-space",
            "supports" => array('editor', 'title', 'thumbnail', 'excerpt'),
            "icon" => 'dashicons-admin-multisite',
            "archive" => false,
            "exclude_from_search" => false,
            "menu_position" => 10,
        ),
        array(
            "post_type" => 'real-estate',
            "name" => __('Bất động sản', 'canhcamtheme'),
            "single" => __('Bất động sản', 'canhcamtheme'),
            "slug" => "real-estate",
            "rewrite" => "real-estate",
            "supports" => array('editor', 'title', 'thumbnail', 'excerpt'),
            "icon" => 'dashicons-admin-multisite',
            "archive" => false,
            "exclude_from_search" => false,
            "menu_position" => 10,
        ),

        // Nhóm thông tin tòa nhà (menu_position: 11)
        array(
            "post_type" => 'building-class',
            "name" => __('Hạng tòa nhà', 'canhcamtheme'),
            "single" => __('Hạng tòa nhà', 'canhcamtheme'),
            "slug" => "building-class",
            "rewrite" => "building-class",
            "supports" => array('title', 'thumbnail', 'excerpt'),
            "icon" => 'dashicons-building',
            "archive" => false,
            "exclude_from_search" => false,
            "menu_position" => 11,
        ),
        array(
            "post_type" => 'building-orientation',
            "name" => __('Hướng tòa nhà', 'canhcamtheme'),
            "single" => __('Hướng tòa nhà', 'canhcamtheme'),
            "slug" => "building-orientation",
            "rewrite" => "building-orientation",
            "supports" => array('title', 'thumbnail', 'excerpt'),
            "icon" => 'dashicons-location-alt',
            "archive" => false,
            "exclude_from_search" => false,
            "menu_position" => 11,
        ),
        array(
            "post_type" => 'area',
            "name" => __('Diện tích', 'canhcamtheme'),
            "single" => __('Diện tích', 'canhcamtheme'),
            "slug" => "area",
            "rewrite" => "area",
            "supports" => array('title', 'thumbnail', 'excerpt'),
            "icon" => 'dashicons-editor-contract',
            "archive" => false,
            "exclude_from_search" => false,
            "menu_position" => 11,
        ),
        array(
            "post_type" => 'certificate',
            "name" => __('Chứng chỉ', 'canhcamtheme'),
            "single" => __('Chứng chỉ', 'canhcamtheme'),
            "slug" => "certificate",
            "rewrite" => "certificate",
            "supports" => array('title', 'thumbnail', 'excerpt'),
            "icon" => 'dashicons-editor-contract',
            "archive" => false,
            "exclude_from_search" => false,
            "menu_position" => 11,
        ),

        // Nhóm thông tin khác (menu_position: 12)
        // array(
        // 	"post_type" => 'product',
        // 	"name" => __('Sản phẩm', 'canhcamtheme'),
        // 	"single" => __('Sản phẩm', 'canhcamtheme'),
        // 	"slug" => "product",
        // 	"rewrite" => "product",
        // 	"supports" => array('title', 'thumbnail', 'excerpt'),
        // 	"icon" => 'dashicons-admin-multisite',
        // 	"archive" => false,
        // 	"exclude_from_search" => false,
        // 	"menu_position" => 12,
        // ),
        array(
            "post_type" => 'recruit',
            "name" => __('Tuyển dụng', 'canhcamtheme'),
            "single" => __('Tuyển dụng', 'canhcamtheme'),
            "slug" => "recruit",
            "rewrite" => "recruit",
            "supports" => array('title', 'thumbnail', 'excerpt'),
            "icon" => 'dashicons-groups',
            "archive" => false,
            "exclude_from_search" => false,
            "menu_position" => 12,
        ),
        array(
            "post_type" => 'faq',
            "name" => __('Câu hỏi thường gặp', 'canhcamtheme'),
            "single" => __('Câu hỏi thường gặp', 'canhcamtheme'),
            "slug" => "faq",
            "rewrite" => "faq",
            "supports" => array('editor', 'title', 'thumbnail', 'excerpt'),
            "icon" => 'dashicons-format-chat',
            "archive" => false,
            "exclude_from_search" => false,
            "menu_position" => 12,
        ),
        array(
            "post_type" => 'thuong-hieu',
            "name" => __('Thương hiệu', 'canhcamtheme'),
            "single" => __('Thương hiệu', 'canhcamtheme'),
            "slug" => "thuong-hieu",
            "rewrite" => "thuong-hieu",
            "supports" => array('editor', 'title', 'thumbnail', 'excerpt'),
            "icon" => 'dashicons-flag',
            "archive" => false,
            "exclude_from_search" => false,
            "menu_position" => 12,
        ),

        // Nhóm địa điểm (menu_position: 14 - already managed by register_location_menu function)
        array(
            "post_type" => 'city',
            "name" => __('Thành phố', 'canhcamtheme'),
            "single" => __('Thành phố', 'canhcamtheme'),
            "slug" => "city",
            "rewrite" => "city",
            "supports" => array('title', 'thumbnail', 'excerpt'),
            "icon" => 'dashicons-location',
            "archive" => false,
            "exclude_from_search" => false,
            "menu_position" => 14,
        ),
        array(
            "post_type" => 'district',
            "name" => __('Quận huyện', 'canhcamtheme'),
            "single" => __('Quận huyện', 'canhcamtheme'),
            "slug" => "district",
            "rewrite" => "district",
            "supports" => array('title', 'thumbnail', 'excerpt'),
            "icon" => 'dashicons-location',
            "archive" => false,
            "exclude_from_search" => false,
            "menu_position" => 14,
        ),
        array(
            "post_type" => 'ward',
            "name" => __('Phường xã', 'canhcamtheme'),
            "single" => __('Phường xã', 'canhcamtheme'),
            "slug" => "ward",
            "rewrite" => "ward",
            "supports" => array('title', 'thumbnail', 'excerpt'),
            "icon" => 'dashicons-location',
            "archive" => false,
            "exclude_from_search" => false,
            "menu_position" => 14,
        ),
        array(
            "post_type" => 'street',
            "name" => __('Đường phố', 'canhcamtheme'),
            "single" => __('Đường phố', 'canhcamtheme'),
            "slug" => "street",
            "rewrite" => "street",
            "supports" => array('title', 'thumbnail', 'excerpt'),
            "icon" => 'dashicons-location',
            "archive" => false,
            "exclude_from_search" => false,
            "menu_position" => 14,
        ),
        array(
            "post_type" => 'block',
            "name" => __('Khu vực', 'canhcamtheme'),
            "single" => __('Khu vực', 'canhcamtheme'),
            "slug" => "block",
            "rewrite" => "block",
            "supports" => array('title', 'thumbnail', 'excerpt'),
            "icon" => 'dashicons-location',
            "archive" => false,
            "exclude_from_search" => false,
            "menu_position" => 14,
        ),
    );
    foreach ($args as $arg) {
        if ($arg['post_type']) {
            pp_create_post_type($arg);
        }
    }
}

function create_custom_taxonomies()
{
    $args = array(

        array(
            "post_type" => array('product'),
            "name" => __('Danh mục SP', 'canhcamtheme'),
            "single" => __('Danh mục SP', 'canhcamtheme'),
            "slug" => "product-tax",
            "rewrite" => "product-tax",
            "taxonomy" => "product-tax",
        ),
        array(
            "post_type" => array('recruit'),
            "name" => __('Danh mục tuyển dụng', 'canhcamtheme'),
            "single" => __('Danh mục tuyển dụng', 'canhcamtheme'),
            "slug" => "recruit-tax",
            "rewrite" => "recruit-tax",
            "taxonomy" => "recruit-tax",
        ), // Văn phòng cho thê
        // array(
        // 	"post_type" => array('office-rent'),
        // 	"name" => __('Danh mục văn phòng cho thuê', 'canhcamtheme'),
        // 	"single" => __('Danh mục văn phòng cho thuê', 'canhcamtheme'),
        // 	"slug" => "office-rent-tax",
        // 	"rewrite" => "office-rent-tax",
        // 	"taxonomy" => "office-rent-tax",
        // ),
        // array(
        // 	"post_type" => array('office-rent'),
        // 	"name" => __('Thông số toà nhà', 'canhcamtheme'),
        // 	"single" => __('Thông số toà nhà', 'canhcamtheme'),
        // 	"slug" => "office-rent-info",
        // 	"rewrite" => "office-rent-info",
        // 	"taxonomy" => "office-rent-info",
        // ),
        // array(
        // 	"post_type" => array('office-rent'),
        // 	"name" => __('Thông tin diện tích và giá', 'canhcamtheme'),
        // 	"single" => __('Thông tin diện tích và giá', 'canhcamtheme'),
        // 	"slug" => "office-rent-area",
        // 	"rewrite" => "office-rent-area",
        // 	"taxonomy" => "office-rent-area",
        // ),
        // array(
        // 	"post_type" => array('office-rent'),
        // 	"name" => __('Tiện ích toà nhà', 'canhcamtheme'),
        // 	"single" => __('Tiện ích toà nhà', 'canhcamtheme'),
        // 	"slug" => "office-rent-facility",
        // 	"rewrite" => "office-rent-facility",
        // 	"taxonomy" => "office-rent-facility",
        // ), 

        //
        // array(
        // 	"post_type" => array('office-rent-full'),
        // 	"name" => __('Danh mục văn phòng trọn gói', 'canhcamtheme'),
        // 	"single" => __('Danh mục văn phòng trọn gói', 'canhcamtheme'),
        // 	"slug" => "office-rent-full-tax",
        // 	"rewrite" => "office-rent-full-tax",
        // 	"taxonomy" => "office-rent-full-tax",
        // ),
        // array(
        // 	"post_type" => array('business-space'),
        // 	"name" => __('Danh mục mặt bằng kinh doanh', 'canhcamtheme'),
        // 	"single" => __('Danh mục mặt bằng kinh doanh', 'canhcamtheme'),
        // 	"slug" => "business-space-tax",
        // 	"rewrite" => "business-space-tax",
        // 	"taxonomy" => "business-space-tax",
        // ),
        // array(
        // 	"post_type" => array('real-estate'),
        // 	"name" => __('Danh mục bất động sản', 'canhcamtheme'),
        // 	"single" => __('Danh mục bất động sản', 'canhcamtheme'),
        // 	"slug" => "real-estate-tax",
        // 	"rewrite" => "real-estate-tax",
        // 	"taxonomy" => "real-estate-tax",
        // ),
    );
    foreach ($args as $arg) {
        if (!empty($arg['post_type'])) {
            pp_create_taxonomy($arg);
        }
    }
}

function pp_create_taxonomy($args)
{
    if (!is_array($args) || !$args['post_type'] || !$args['name'] || !$args['single'] || !$args['taxonomy'] || !$args['slug']) return;

    $post_type = $args['post_type'];

    $name = $args['name'];

    $single = $args['single'];

    $slug = $args['slug'];

    $rewrite = (isset($args['rewrite'])) ? $args['rewrite'] : $slug;

    $taxonomy = $args['taxonomy'];

    $hierarchical = isset($args['hierarchical']) ? $args['hierarchical'] : true;

    $labels = array(

        'name' => __($name, 'pp'),

        'singular_name' => __($single, 'pp'),

        'search_items' => __('Filter By ' . $name, 'pp'),

        'popular_items' => __('Popular ' . $name, 'pp'),

        'all_items' => __('All ' . $name, 'pp'),

        'parent_item' => null,

        'parent_item_colon' => null,

        'edit_item' => __('Edit ' . $single, 'pp'),

        'update_item' => __('Update ' . $single, 'pp'),

        'add_new_item' => __('Add New ' . $single, 'pp'),

        'new_item_name' => __('Add New ' . $single, 'pp'),

        'menu_name' => __($name, 'pp'),

    );


    $args = array(

        'hierarchical' => $hierarchical,

        'labels' => $labels,

        'show_ui' => true,

        'show_admin_column' => true,

        'query_var' => true,

        'rewrite' => array('slug' => $rewrite),

    );

    register_taxonomy($taxonomy, $post_type, $args);
}



add_action('init', 'create_custom_taxonomies');



// Create custom Location menu and submenus
function register_location_menu()
{
    global $menu;

    // Xóa menu liên kết trực tiếp đến city trước
    foreach ($menu as $key => $value) {
        if (isset($value[2]) && $value[2] === 'edit.php?post_type=city') {
            unset($menu[$key]);
        }
    }

    // Create parent menu 
    add_menu_page(
        'Quản lý địa điểm', // Page title
        'Địa điểm',         // Menu title
        'manage_options',   // Capability
        'edit.php?post_type=city', // Liên kết đến Thành phố
        '',                 // Function callback (empty as we'll use submenus)
        'dashicons-location-alt', // Icon
        24                  // Position
    );


    // Register the existing post types as submenu items
    add_submenu_page(
        'edit.php?post_type=city',
        'Thành phố',
        'Thành phố',
        'manage_options',
        'edit.php?post_type=city',
        null
    );

    add_submenu_page(
        'edit.php?post_type=city',
        'Quận huyện',
        'Quận huyện',
        'manage_options',
        'edit.php?post_type=district',
        null
    );

    add_submenu_page(
        'edit.php?post_type=city',
        'Phường xã',
        'Phường xã',
        'manage_options',
        'edit.php?post_type=ward',
        null
    );

    add_submenu_page(
        'edit.php?post_type=city',
        'Đường phố',
        'Đường phố',
        'manage_options',
        'edit.php?post_type=street',
        null
    );

    add_submenu_page(
        'edit.php?post_type=city',
        'Khu vực',
        'Khu vực',
        'manage_options',
        'edit.php?post_type=block',
        null
    );
}
// Thêm hook này với priority cao để đảm bảo chạy sau khi toàn bộ menu đã được tạo
add_action('admin_menu', 'register_location_menu', 100);

// Hide the original menu items 
function hide_original_post_type_menus()
{
    remove_menu_page('edit.php?post_type=district');
    remove_menu_page('edit.php?post_type=ward');
    remove_menu_page('edit.php?post_type=street');
    remove_menu_page('edit.php?post_type=block');
}
add_action('admin_menu', 'hide_original_post_type_menus', 999);


function group_real_estate_menu()
{
    // Tạo menu chính
    add_menu_page(
        'Quản lý Office',              // Page title
        'Quản lý Office',              // Menu title
        'manage_options',                // Capability (có thể thay bằng 'manage_options' nếu chỉ admin thấy)
        'edit.php?post_type=office-rent', // Liên kết đến Thành phố
        '',                          // Callback function (trống vì không cần trang chính)
        'dashicons-building',        // Icon
        22                           // Position (đồng bộ với menu_position của nhóm)
    );

    add_menu_page(
        'Quản lý thuộc tính',              // Page title
        'Quản lý thuộc tính',              // Menu title
        'manage_options',                // Capability (có thể thay bằng 'manage_options' nếu chỉ admin thấy)
        'edit.php?post_type=building-class', // Liên kết đến Thành phố
        '',                          // Callback function (trống vì không cần trang chính)
        'dashicons-admin-tools',        // Icon
        23                           // Position (đồng bộ với menu_position của nhóm)
    );

    // Danh sách post types cần gom
    $post_types = [
        'office-rent' => __('Văn phòng cho thuê', 'canhcamtheme'),
        'office-rent-full' => __('Văn phòng trọn gói', 'canhcamtheme'),
        'business-space' => __('Mặt bằng kinh doanh', 'canhcamtheme'),
        'real-estate' => __('Bất động sản', 'canhcamtheme'),
    ];


    // Danh sách post types cần gom
    $post_types_attr = [
        'building-class' => __('Hạng tòa nhà', 'canhcamtheme'),
        'area' => __('Diện tích', 'canhcamtheme'),
        'building-orientation' => __('Hướng tòa nhà', 'canhcamtheme'),
        'thuong-hieu' => __('Thương hiệu', 'canhcamtheme'),
        'certificate' => __('Chứng chỉ', 'canhcamtheme'),
    ];

    // Xóa menu mặc định và thêm submenu
    foreach ($post_types as $post_type => $label) {
        // Xóa menu mặc định của post type
        remove_menu_page('edit.php?post_type=' . $post_type);

        // Thêm submenu dưới menu chính
        add_submenu_page(
            'edit.php?post_type=office-rent',           // Parent slug
            $label,                          // Page title
            $label,                          // Menu title
            'edit_posts',                    // Capability
            'edit.php?post_type=' . $post_type // Menu slug (trỏ đến trang quản lý post type)
        );
    }

    foreach ($post_types_attr as $post_type => $label) {
        // Xóa menu mặc định của post type
        remove_menu_page('edit.php?post_type=' . $post_type);

        // Thêm submenu dưới menu chính
        add_submenu_page(
            'edit.php?post_type=building-class',           // Parent slug
            $label,                          // Page title
            $label,                          // Menu title
            'edit_posts',                    // Capability
            'edit.php?post_type=' . $post_type // Menu slug (trỏ đến trang quản lý post type)
        );
    }
}
add_action('admin_menu', 'group_real_estate_menu', 999);


/**
 * Set initial values for the "banner-field" taxonomy.
 */
function set_banner_field_initial_values()
{
    // Check if the taxonomy already exists
    if (taxonomy_exists('banner-field')) {
        // Array of initial values with their corresponding names
        $initial_values = array(
            'main-banner' => 'Main Banner',
            'page-banner' => 'Page Banner'
        );

        // Loop through the initial values and check if they exist, if not, insert them
        foreach ($initial_values as $value => $name) {
            if (!term_exists($value, 'banner-field')) {
                wp_insert_term($name, 'banner-field', array('slug' => $value));
            }
        }
    }
}
add_action('init', 'set_banner_field_initial_values');





/**
 * Đăng ký ACF field type mới
 */
function register_acf_dynamic_fields()
{
    // Đường dẫn đến thư mục field type
    $path = get_template_directory() . '/inc/acf/acf-dynamic-fields/';

    // Kiểm tra xem thư mục có tồn tại không
    if (file_exists($path . 'init.php')) {
        include_once($path . 'init.php');
    }
}
add_action('acf/include_field_types', 'register_acf_dynamic_fields');


/**
 * Add location metabox for office rental posts
 */
function add_office_location_metabox()
{
    add_meta_box(
        'office_location_metabox',
        'Thông tin địa điểm',
        'render_office_location_metabox',
        ['office-rent', 'office-rent-full', 'business-space', 'real-estate'], // Assuming 'office-rental' is the post type for office rentals
        'side', // Changed from 'normal' to 'side' to display on the right
        'high' // High priority to display at the top
    );
}
add_action('add_meta_boxes', 'add_office_location_metabox');

/**
 * Render the location metabox content
 */
function render_office_location_metabox($post)
{
    // Add nonce for security
    wp_nonce_field('office_location_metabox_nonce', 'office_location_nonce');

    // Get saved values
    $city_id = get_post_meta($post->ID, '_office_city', true);
    $district_id = get_post_meta($post->ID, '_office_district', true);
    $ward_id = get_post_meta($post->ID, '_office_ward', true);
    $street_id = get_post_meta($post->ID, '_office_street', true);
    $house_number = get_post_meta($post->ID, '_office_house_number', true);
    $brand_id = get_post_meta($post->ID, '_office_brand', true);
    $block_id = get_post_meta($post->ID, '_office_block', true);

    // Get cities
    $cities = get_posts(array(
        'post_type' => 'city',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
        'suppress_filters' => false // Allow WPML to filter by current language
    ));

    // Get districts
    $districts = array();
    if (!empty($city_id)) {
        $districts = get_posts(array(
            'post_type' => 'district',
            'posts_per_page' => -1,
            'orderby' => 'title',
            'order' => 'ASC',
            'suppress_filters' => false, // Allow WPML to filter by current language
            'meta_query' => array(
                array(
                    'key' => 'city_id',
                    'value' => $city_id
                )
            )
        ));
    }

    // Get wards
    $wards = array();
    if (!empty($district_id)) {
        $wards = get_posts(array(
            'post_type' => 'ward',
            'posts_per_page' => -1,
            'orderby' => 'title',
            'order' => 'ASC',
            'suppress_filters' => false, // Allow WPML to filter by current language
            'meta_query' => array(
                array(
                    'key' => 'district_id',
                    'value' => $district_id
                )
            )
        ));
    }

    // Get streets
    $streets = array();
    if (!empty($district_id)) {
        $streets = get_posts(array(
            'post_type' => 'street',
            'posts_per_page' => -1,
            'orderby' => 'title',
            'order' => 'ASC',
            'suppress_filters' => false, // Allow WPML to filter by current language
            'meta_query' => array(
                array(
                    'key' => 'district_id',
                    'value' => $district_id
                )
            )
        ));
    }

    // Get brands
    $brands = get_posts(array(
        'post_type' => 'thuong-hieu',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
        'suppress_filters' => false // Allow WPML to filter by current language
    ));

    // Get blocks
    $blocks = get_posts(array(
        'post_type' => 'block',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
        'suppress_filters' => false // Allow WPML to filter by current language
    ));

?>
    <div class="office-location-fields">
        <style>
            .office-location-fields .location-field {
                margin-bottom: 15px;
            }

            .office-location-fields label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
            }

            .office-location-fields select,
            .office-location-fields input[type="text"] {
                width: 100%;
            }
        </style>

        <div class="location-field">
            <label for="office_city">Tỉnh/Thành phố:</label>
            <select name="office_city" id="office_city" class="widefat">
                <option value="">-- Chọn Tỉnh/Thành phố --</option>
                <?php foreach ($cities as $city) : ?>
                    <option value="<?php echo esc_attr($city->ID); ?>" <?php selected($city_id, $city->ID); ?>>
                        <?php echo esc_html($city->post_title); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="location-field">
            <label for="office_district">Quận/Huyện:</label>
            <select name="office_district" id="office_district" class="widefat" <?php echo empty($city_id) ? 'disabled' : ''; ?>>
                <option value="">-- Chọn Quận/Huyện --</option>
                <?php foreach ($districts as $district) : ?>
                    <option value="<?php echo esc_attr($district->ID); ?>" <?php selected($district_id, $district->ID); ?>>
                        <?php echo esc_html($district->post_title); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="location-field">
            <label for="office_ward">Phường/Xã:</label>
            <select name="office_ward" id="office_ward" class="widefat" <?php echo empty($district_id) ? 'disabled' : ''; ?>>
                <option value="">-- Chọn Phường/Xã --</option>
                <?php foreach ($wards as $ward) : ?>
                    <option value="<?php echo esc_attr($ward->ID); ?>" <?php selected($ward_id, $ward->ID); ?>>
                        <?php echo esc_html($ward->post_title); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="location-field">
            <label for="office_street">Đường:</label>
            <select name="office_street" id="office_street" class="widefat" <?php echo empty($district_id) ? 'disabled' : ''; ?>>
                <option value="">-- Chọn Đường --</option>
                <?php foreach ($streets as $street) : ?>
                    <option value="<?php echo esc_attr($street->ID); ?>" <?php selected($street_id, $street->ID); ?>>
                        <?php echo esc_html($street->post_title); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="location-field">
            <label for="office_block">Khu vực:</label>
            <select name="office_block" id="office_block" class="widefat">
                <option value="">-- Chọn Khu vực --</option>
                <?php foreach ($blocks as $block) : ?>
                    <option value="<?php echo esc_attr($block->ID); ?>" <?php selected($block_id, $block->ID); ?>>
                        <?php echo esc_html($block->post_title); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="location-field">
            <label for="office_house_number">Số nhà:</label>
            <input type="text" name="office_house_number" id="office_house_number" class="widefat" value="<?php echo esc_attr($house_number); ?>" placeholder="Nhập số nhà">
        </div>

        <div class="location-field">
            <label for="office_brand">Thương hiệu:</label>
            <select name="office_brand" id="office_brand" class="widefat">
                <option value="">-- Chọn Thương hiệu --</option>
                <?php foreach ($brands as $brand) : ?>
                    <option value="<?php echo esc_attr($brand->ID); ?>" <?php selected($brand_id, $brand->ID); ?>>
                        <?php echo esc_html($brand->post_title); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
    </div>

    <script>
        jQuery(document).ready(function($) {
            // City change event
            $('#office_city').on('change', function() {
                var cityId = $(this).val();
                var districtSelect = $('#office_district');
                var wardSelect = $('#office_ward');
                var streetSelect = $('#office_street');

                // Reset dependent dropdowns
                districtSelect.html('<option value="">-- Chọn Quận/Huyện --</option>').prop('disabled', !cityId);
                wardSelect.html('<option value="">-- Chọn Phường/Xã --</option>').prop('disabled', true);
                streetSelect.html('<option value="">-- Chọn Đường --</option>').prop('disabled', true);

                if (cityId) {
                    // AJAX to get districts
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'get_districts_by_city',
                            city_id: cityId,
                            nonce: '<?php echo wp_create_nonce('get_location_data_nonce'); ?>'
                        },
                        success: function(response) {
                            if (response.success && response.data) {
                                $.each(response.data, function(id, name) {
                                    districtSelect.append($('<option></option>').attr('value', id).text(name));
                                });
                            }
                        }
                    });
                }
            });

            // District change event
            $('#office_district').on('change', function() {
                var districtId = $(this).val();
                var wardSelect = $('#office_ward');
                var streetSelect = $('#office_street');

                // Reset dependent dropdowns
                wardSelect.html('<option value="">-- Chọn Phường/Xã --</option>').prop('disabled', !districtId);
                streetSelect.html('<option value="">-- Chọn Đường --</option>').prop('disabled', !districtId);

                if (districtId) {
                    // AJAX to get wards
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'get_wards_by_district',
                            district_id: districtId,
                            nonce: '<?php echo wp_create_nonce('get_location_data_nonce'); ?>'
                        },
                        success: function(response) {
                            if (response.success && response.data) {
                                $.each(response.data, function(id, name) {
                                    wardSelect.append($('<option></option>').attr('value', id).text(name));
                                });
                            }
                        }
                    });

                    // AJAX to get streets
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'get_streets_by_district',
                            district_id: districtId,
                            nonce: '<?php echo wp_create_nonce('get_location_data_nonce'); ?>'
                        },
                        success: function(response) {
                            if (response.success && response.data) {
                                $.each(response.data, function(id, name) {
                                    streetSelect.append($('<option></option>').attr('value', id).text(name));
                                });
                            }
                        }
                    });
                }
            });
        });
    </script>
<?php
}

/**
 * Save the metabox data
 */
function save_office_location_metabox($post_id)
{
    // Check if nonce is set
    if (!isset($_POST['office_location_nonce'])) {
        return;
    }

    // Verify nonce
    if (!wp_verify_nonce($_POST['office_location_nonce'], 'office_location_metabox_nonce')) {
        return;
    }

    // Check autosave
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // Check permissions
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Save city
    if (isset($_POST['office_city'])) {
        update_post_meta($post_id, '_office_city', sanitize_text_field($_POST['office_city']));
    }

    // Save district
    if (isset($_POST['office_district'])) {
        update_post_meta($post_id, '_office_district', sanitize_text_field($_POST['office_district']));
    }

    // Save ward
    if (isset($_POST['office_ward'])) {
        update_post_meta($post_id, '_office_ward', sanitize_text_field($_POST['office_ward']));
    }

    // Save street
    if (isset($_POST['office_street'])) {
        update_post_meta($post_id, '_office_street', sanitize_text_field($_POST['office_street']));
    }

    // Save block
    if (isset($_POST['office_block'])) {
        update_post_meta($post_id, '_office_block', sanitize_text_field($_POST['office_block']));
    }

    // Save house number
    if (isset($_POST['office_house_number'])) {
        update_post_meta($post_id, '_office_house_number', sanitize_text_field($_POST['office_house_number']));
    }

    // Save brand
    if (isset($_POST['office_brand'])) {
        update_post_meta($post_id, '_office_brand', sanitize_text_field($_POST['office_brand']));
    }
}
add_action('save_post_office-rent', 'save_office_location_metabox');
add_action('save_post_office-rent-full', 'save_office_location_metabox');
add_action('save_post_business-space', 'save_office_location_metabox');
add_action('save_post_real-estate', 'save_office_location_metabox');
/**
 * AJAX handler for getting districts by city
 */
function get_districts_by_city_callback()
{
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'get_location_data_nonce')) {
        wp_send_json_error('Invalid nonce');
    }

    $city_id = isset($_POST['city_id']) ? intval($_POST['city_id']) : 0;

    if (!$city_id) {
        wp_send_json_error('Invalid city ID');
    }

    $districts = get_posts(array(
        'post_type' => 'district',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
        'suppress_filters' => false, // Allow WPML to filter by current language
        'meta_query' => array(
            array(
                'key' => 'city_id',
                'value' => $city_id
            )
        )
    ));

    $result = array();
    foreach ($districts as $district) {
        $result[$district->ID] = $district->post_title;
    }

    wp_send_json_success($result);
}
add_action('wp_ajax_get_districts_by_city', 'get_districts_by_city_callback');

/**
 * AJAX handler for getting wards by district
 */
function get_wards_by_district_callback()
{
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'get_location_data_nonce')) {
        wp_send_json_error('Invalid nonce');
    }

    $district_id = isset($_POST['district_id']) ? intval($_POST['district_id']) : 0;

    if (!$district_id) {
        wp_send_json_error('Invalid district ID');
    }

    $wards = get_posts(array(
        'post_type' => 'ward',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
        'suppress_filters' => false, // Allow WPML to filter by current language
        'meta_query' => array(
            array(
                'key' => 'district_id',
                'value' => $district_id
            )
        )
    ));

    $result = array();
    foreach ($wards as $ward) {
        $result[$ward->ID] = $ward->post_title;
    }

    wp_send_json_success($result);
}
add_action('wp_ajax_get_wards_by_district', 'get_wards_by_district_callback');

/**
 * AJAX handler for getting streets by district
 */
function get_streets_by_district_callback()
{
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'get_location_data_nonce')) {
        wp_send_json_error('Invalid nonce');
    }

    $district_id = isset($_POST['district_id']) ? intval($_POST['district_id']) : 0;

    if (!$district_id) {
        wp_send_json_error('Invalid district ID');
    }

    $streets = get_posts(array(
        'post_type' => 'street',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
        'suppress_filters' => false, // Allow WPML to filter by current language
        'meta_query' => array(
            array(
                'key' => 'district_id',
                'value' => $district_id
            )
        )
    ));

    $result = array();
    foreach ($streets as $street) {
        $result[$street->ID] = $street->post_title;
    }

    wp_send_json_success($result);
}
add_action('wp_ajax_get_streets_by_district', 'get_streets_by_district_callback');
