<?php
/**
 * Custom Breadcrumbs Function for WordPress
 * 
 * This function generates and displays a breadcrumb navigation trail for WordPress sites.
 * It matches the HTML structure from the provided example.
 */

function canhcam_breadcrumbs() {
    // Settings
    $separator          = '<span class="separator"></span>';
    $breadcrumbs_class  = 'rank-math-breadcrumb';
    $home_title         = __('Trang chủ', 'canhcamtheme');
    
    // Get the query & post information
    global $post, $wp_query;
    
    // Do not display on the homepage
    if (is_front_page()) {
        return;
    }
    
    // Build the breadcrumbs
    echo '<div class="global-breadcrumb">';
    echo '<div class="container">';
    echo '<nav class="' . $breadcrumbs_class . '">';
    echo '<p>';
    
    // Home page
    echo '<a href="' . get_home_url() . '">' . $home_title . '</a>';
    
    if (is_archive() && !is_tax() && !is_category() && !is_tag()) {
        // Archive page
        echo $separator;
        echo '<span class="last">' . post_type_archive_title('', false) . '</span>';
    } else if (is_archive() && is_tax() && !is_category() && !is_tag()) {
        // Taxonomy archive
        $current_term = get_term_by('slug', get_query_var('term'), get_query_var('taxonomy'));
        $taxonomy = get_taxonomy(get_query_var('taxonomy'));
        
        if ($taxonomy->hierarchical) {
            // Display parent terms
            $ancestors = get_ancestors($current_term->term_id, get_query_var('taxonomy'));
            $ancestors = array_reverse($ancestors);
            
            foreach ($ancestors as $ancestor) {
                $ancestor = get_term($ancestor, get_query_var('taxonomy'));
                echo $separator;
                echo '<a href="' . get_term_link($ancestor) . '">' . $ancestor->name . '</a>';
            }
        }
        
        echo $separator;
        echo '<span class="last">' . $current_term->name . '</span>';
    } else if (is_single()) {
        // Single post
        $post_type = get_post_type();
        
        if ($post_type == 'office-rent' || $post_type == 'office-rent-full' || $post_type == 'business-space' || $post_type == 'real-estate' || $post_type == 'real-estate') {
            // Special handling for office-rent post type
            // echo $separator;
            // echo '<a href="' . get_post_type_archive_link($post_type) . '">' . __('Văn phòng cho thuê', 'canhcamtheme') . '</a>';
            
            // Get chu_de_1 value using ACF
            $chu_de_1 = get_field('chu_de_1', $post->ID);
            if (!empty($chu_de_1)) {
                echo $separator;
                echo '<a href="' . get_permalink($chu_de_1) . '">' . get_the_title($chu_de_1) . '</a>';
            }
            
            // Get chu_de_2 value using ACF
            $chu_de_2 = get_field('chu_de_2', $post->ID);
            if (!empty($chu_de_2)) {
                echo $separator;
                echo '<a href="' . get_permalink($chu_de_2) . '">' . get_the_title($chu_de_2) . '</a>';
            }
            
            // Get district information
            // $district_id = get_post_meta($post->ID, '_office_district', true);
            // if (!empty($district_id)) {
            //     echo $separator;
            //     echo '<a href="' . home_url('/van-phong-cho-thue-quan-1/') . '">' . get_the_title($district_id) . '</a>';
            // }
            
            // echo $separator;
            // echo '<span class="last">' . get_the_title() . '</span>';
        } else if ($post_type != 'post') {
            // Custom post type
            $post_type_object = get_post_type_object($post_type);
            $post_type_archive = get_post_type_archive_link($post_type);
            
            echo $separator;
            echo '<a href="' . $post_type_archive . '">' . $post_type_object->labels->name . '</a>';
            
            echo $separator;
            echo '<span class="last">' . get_the_title() . '</span>';
        } else {
            // Standard post
            $category = get_the_category();
            if ($category) {
                $category_values = array_values($category);
                $last_category = end($category_values);
                
                // Get category hierarchy
                $cat_parents = array();
                $parent_id = $last_category->parent;
                
                while ($parent_id) {
                    $parent = get_category($parent_id);
                    $cat_parents[] = array($parent->name, get_category_link($parent->term_id));
                    $parent_id = $parent->parent;
                }
                $cat_parents = array_reverse($cat_parents);
                
                // Output category parents
                foreach ($cat_parents as $parent) {
                    echo $separator;
                    echo '<a href="' . $parent[1] . '">' . $parent[0] . '</a>';
                }
                
                echo $separator;
                echo '<a href="' . get_category_link($last_category->term_id) . '">' . $last_category->name . '</a>';
            }
            
            echo $separator;
            echo '<span class="last">' . get_the_title() . '</span>';
        }
    } else if (is_category()) {
        // Category page
        $category = get_category(get_query_var('cat'));
        
        if ($category->parent != 0) {
            // Get category hierarchy
            $cat_parents = array();
            $parent_id = $category->parent;
            
            while ($parent_id) {
                $parent = get_category($parent_id);
                $cat_parents[] = array($parent->name, get_category_link($parent->term_id));
                $parent_id = $parent->parent;
            }
            $cat_parents = array_reverse($cat_parents);
            
            // Output category parents
            foreach ($cat_parents as $parent) {
                echo $separator;
                echo '<a href="' . $parent[1] . '">' . $parent[0] . '</a>';
            }
        }
        
        echo $separator;
        echo '<span class="last">' . single_cat_title('', false) . '</span>';
    } else if (is_page()) {
        // Standard page
        if ($post->post_parent) {
            // If child page, get parents
            $ancestors = get_post_ancestors($post->ID);
            $ancestors = array_reverse($ancestors);
            
            foreach ($ancestors as $ancestor) {
                echo $separator;
                echo '<a href="' . get_permalink($ancestor) . '">' . get_the_title($ancestor) . '</a>';
            }
        }
        
        echo $separator;
        echo '<span class="last">' . get_the_title() . '</span>';
    } else if (is_tag()) {
        // Tag page
        echo $separator;
        echo '<span class="last">' . single_tag_title('', false) . '</span>';
    } else if (is_day()) {
        // Day archive
        echo $separator;
        echo '<a href="' . get_year_link(get_the_time('Y')) . '">' . get_the_time('Y') . '</a>';
        echo $separator;
        echo '<a href="' . get_month_link(get_the_time('Y'), get_the_time('m')) . '">' . get_the_time('F') . '</a>';
        echo $separator;
        echo '<span class="last">' . get_the_time('d') . '</span>';
    } else if (is_month()) {
        // Month archive
        echo $separator;
        echo '<a href="' . get_year_link(get_the_time('Y')) . '">' . get_the_time('Y') . '</a>';
        echo $separator;
        echo '<span class="last">' . get_the_time('F') . '</span>';
    } else if (is_year()) {
        // Year archive
        echo $separator;
        echo '<span class="last">' . get_the_time('Y') . '</span>';
    } else if (is_author()) {
        // Author archive
        global $author;
        $userdata = get_userdata($author);
        echo $separator;
        echo '<span class="last">' . $userdata->display_name . '</span>';
    } else if (is_search()) {
        // Search results page
        echo $separator;
        echo '<span class="last">' . __('Kết quả tìm kiếm cho', 'canhcamtheme') . ' "' . get_search_query() . '"</span>';
    } else if (is_404()) {
        // 404 page
        echo $separator;
        echo '<span class="last">' . __('Không tìm thấy trang', 'canhcamtheme') . '</span>';
    }
    
    echo '</p>';
    echo '</nav>';
    echo '</div>';
    echo '</div>';
}
