<?php
function remove_editor()
{
	remove_post_type_support('post', 'editor');
}
// add_action('init', 'remove_editor');

function formatNumber($number)
{
	if (is_string($number)) {
		$number = stringToNumber($number);
	}
	if (!is_numeric($number)) {
		return "Invalid input";
	}
	if ($number > 1000) {
		return $number / 1000 . "N";
	} else {
		return $number;
	}
}

function stringToNumber($string)
{
	$number = 0;
	if (is_numeric($string)) {
		$number = (float) $string;
	}
	return $number;
};


function footer_logo_customizer_setting($wp_customize)
{
	// add a setting 
	$wp_customize->add_setting('footer_logo');
	// Add a control to upload the hover logo
	$wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'footer_logo', array(
		'label' => 'Footer logo',
		'section' => 'title_tagline', //this is the section where the custom-logo from WordPress is
		'settings' => 'footer_logo',
		'priority' => 8 // show it just below the custom-logo
	)));
}

add_action('customize_register', 'footer_logo_customizer_setting');



// Wordpress + custom lozad

function custom_get_post_thumbnail($post_id, $size = 'full', $attr = '')
{
	if (is_array($post_id))
		$post_id = $post_id["ID"];
	$post_thumbnail_id = get_post_thumbnail_id($post_id);
	$image_attributes = wp_get_attachment_image_src($post_thumbnail_id, $size);
	$alt_text = get_post_meta($post_thumbnail_id, '_wp_attachment_image_alt', true);
	if ($image_attributes) {
		$html = "<img width='" . $image_attributes[1] . "' height='" . $image_attributes[2] . "' data-src='" . esc_url($image_attributes[0]) . "' class='lozad'";
		if (empty($alt_text)) {
			$html .= 'alt="' . get_bloginfo('name') . '"';
		} else {
			$html .= ' alt="' . esc_attr($alt_text) . '"';
		}
		$html .= ' />';

		return $html;
	} else {
		$html = "<img data-src='" . get_bloginfo("template_directory") . "/img/no-image.jpg' class='lozad' width='100px' height='100px' alt='" . get_bloginfo('name') . " '/>";
		return $html;
	}
}

function custom_lozad_image($attachment_id, $lozad = true, $showEmptyImage = false)
{
	if (is_array($attachment_id))
		$attachment_id = $attachment_id["ID"];
	$image_attributes = wp_get_attachment_image_src($attachment_id, 'full');
	$alt_text = get_post_meta($attachment_id, '_wp_attachment_image_alt', true);
	if ($image_attributes) {
		if (!$lozad) {
			$html = "<img width='" . $image_attributes[1] . "' height='" . $image_attributes[2] . "' src='" . esc_url($image_attributes[0]) . "'";
		} else {
			$html = "<img width='" . $image_attributes[1] . "' height='" . $image_attributes[2] . "' data-src='" . esc_url($image_attributes[0]) . "' class='lozad'";
		}
		if (empty($alt_text)) {
			$html .= 'alt="' . get_bloginfo('name') . '"';
		} else {
			$html .= ' alt="' . esc_attr($alt_text) . '"';
		}
		$html .= ' />';
	} else if (
		!$image_attributes && $showEmptyImage
	) {
		$html = "<img width='100' height='100' alt='" . esc_attr(get_bloginfo('name')) . "' data-src='" . get_bloginfo("template_directory") . "/img/no-image.jpg' class='lozad'";
		$html .= ' />';
	}
	return $html;
}

// Remove p tag in contact form 
add_filter('wpcf7_autop_or_not', '__return_false');

add_filter('rank_math/frontend/breadcrumb/items', function ($crumbs, $class) {
	$language_active = do_shortcode('[language]');
	$homepage_url = get_home_url();
	if ($language_active == 'en') {
		$crumbs[0][0] = 'Home';
		$crumbs[0][1] = $homepage_url;
	} else {
		$crumbs[0][0] = 'Trang chủ';
		$crumbs[0][1] = $homepage_url;
	}
	return $crumbs;
}, 10, 2);


// Lấy ID các trang sử dụng giao diện
function get_page_by_template($name_template)
{
	$pages = get_posts(array(
		'post_type' => 'page',
		'meta_key' => '_wp_page_template',
		'meta_value' => $name_template
	));
	$id = $pages[0]->ID;
	return $id;
}



function dateFormatOnLayout($id_or_date)
{
	if (is_numeric($id_or_date)) {
		$id = (int)$id_or_date; // Ensure it's an integer (post ID)
		$day = get_the_date("d", $id);
		$month = get_the_date("m", $id);
		$year = get_the_date("Y", $id);
	} else {
		$date_time = false;

		// Array of possible date formats to check
		$date_formats = array("d/m/Y", "Y/d/m");
		// Try each date format until one succeeds
		foreach ($date_formats as $format) {
			$date_time = DateTime::createFromFormat($format, $id_or_date);
			if ($date_time !== false) {
				break; // Break out of the loop if a valid date is found
			}
		}
		if ($date_time) {
			$day = $date_time->format('d');
			$month = $date_time->format('m');
			$year = $date_time->format('Y');
		} else {
			// Handle invalid date string
			return __("Đang cập nhật...", 'canhcamtheme');
		}
	}
	$language_active = do_shortcode('[language]');
	// $dateFormat = get_the_time('H:i', $id) . ' - ' . $day . '/' . $month . '/' . $year;
	if ($language_active == 'vi') {
		$dateFormat = $day . '.' . $month . '.' . $year;
	} else {
		$dateFormat = $month . '.' . $day . '.' . $year;
	}
	return $dateFormat;
}

function getYear($time)
{
	// 
	if (!date_create($time))
		return __('Vui lòng nhập đúng định dạng ngày', 'canhcamtheme');
	$year = date_format(date_create($time), 'Y');
	return $year;
}

function fullDateLayout($date_string)
{
	$date_time = DateTime::createFromFormat("Y-m-d\TH:i:sT", $date_string);

	if (!$date_time) {
		return ''; // Return empty string if the date format is invalid
	}

	$language_active = do_shortcode('[language]');

	if ($language_active == 'vi') {
		$day_names = array(
			'Sunday' => 'Chủ nhật',
			'Monday' => 'Thứ hai',
			'Tuesday' => 'Thứ ba',
			'Wednesday' => 'Thứ tư',
			'Thursday' => 'Thứ năm',
			'Friday' => 'Thứ sáu',
			'Saturday' => 'Thứ bảy',
		);

		$dateFormat = $date_time->format("l, d/m/Y - G:i");
		$dateFormat = strtr($dateFormat, $day_names);
	} else {
		$dateFormat = $date_time->format("l, F/d/Y - G:i");
	}

	return $dateFormat;
}

function get_other_post_type($post, $catID, $post_per_page = 8)
{
	$args = array(
		'posts_per_page' => $post_per_page,
		'order_by' => "date",
		'post_type' => $post->post_type,
		'post__not_in' => array($post->ID),

	);
	if ($post->post_type != 'post') {
		// Get taxonomy by post id
		$taxonomy = get_post_taxonomies($post)[0];
		$term = wp_get_post_terms($post->ID, $taxonomy);
		$taxonomyID = $term[0]->term_taxonomy_id;
		$args['tax_query'] = array(
			'relation' => 'AND',
			array(
				'taxonomy' => $taxonomy,
				'field' => 'term_id',
				'terms' => array($taxonomyID),
			),
		);
	} else {
		$args['category__in'] = $catID;
	}
	return $args;
}


// * get related post without tax query taxonomy 
function get_dynamic_related_posts($post, $posts_per_page = 8)
{
	$taxonomy = get_post_taxonomies($post)[0];
	$term = wp_get_post_terms($post->ID, $taxonomy)[0];
	$taxonomyID = $term->term_taxonomy_id;
	$args = array(
		'posts_per_page' => $posts_per_page,
		'order_by' => "date",
		'post_type' => $post->post_type,
		'post__not_in' => array($post->ID),
	);
	if ($post->post_type != 'post') {
		$args['tax_query'] = array(
			'relation' => 'AND',
			array(
				'taxonomy' => $taxonomy,
				'field' => 'term_id',
				'terms' => array($taxonomyID),
			),
		);
	} else {
		$args['cat'] = $taxonomyID;
	}
	return $args;
}

function get_query_posts($term, $posts_per_page = 10)
{
	$taxonomy = $term->taxonomy;
	$taxonomyID = $term->term_taxonomy_id;
	$post_type = get_taxonomy($term->taxonomy)->object_type[0];
	$args = array(
		'post_type' => $post_type,
		'posts_per_page' => $posts_per_page,
	);
	if ($taxonomy === 'category') {
		$args['cat'] = $taxonomyID;
	} else {
		$args['tax_query'] = array(
			'relation' => 'AND', // You can change 'AND' to 'OR' if needed
			array(
				'taxonomy' => $taxonomy,
				'field'    => 'term_id',
				'terms'    => $taxonomyID,
			),
		);
	}
	return new WP_Query($args); // Return the WP_Query object directly
}



class Custom_Walker_Nav_Menu extends Walker_Nav_Menu
{
	protected $current_parent_title = '';
	protected $current_parent_description = '';

	public function start_el(&$output, $item, $depth = 0, $args = array(), $id = 0)
	{
		// Debugging: Output the contents of $custom_post_type_parents
		// error_log('Custom Post Type Parents Object: ' . print_r($custom_post_type_parents, true));

		$isValidCustomUrl = $item->custom_data && $item->custom_data['url'] && $item->custom_data['isModify'];
		if ($isValidCustomUrl) {
			$item->url = $item->custom_data['url'];
		}
		$classes = empty($item->classes) ? array() : (array) $item->classes;

		if ($depth === 0) {
			$this->current_parent_title = $item->title;
			$this->current_parent_description = $item->description;
			$classes[] = 'menu-item-has-children'; // Add current class for depth 0
		}

		if ($depth === 0) {
			$classes[] = 'nav-link';
		} else {
			$classes[] = 'nav-link-sub';
		}
		if ($depth === 1) {
			$image = get_field('image', $item);
			$item->current_image_url = $image ? getImageUrl($image) : null;
		}

		// Check if the current item is a parent of the current post or taxonomy
		if (is_singular() && in_array($item->object_id, get_post_ancestors(get_the_ID()))) {
			$classes[] = 'active';
		} elseif (is_tax() && $item->object_id == get_queried_object()->parent) {
			$classes[] = 'active';
		} else {
			$classes[] = ($item->current || $item->current_item_ancestor) ? 'active' : '';
		}

		if (in_array('menu-item-has-children', $item->classes)) {
			$classes[] = 'drop-down';
		}

		$class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args, $depth));
		$class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';
		$id = apply_filters('nav_menu_item_id', 'menu-item-' . $item->ID, $item, $args, $depth);
		$id = $id ? ' id="' . esc_attr($id) . '"' : '';

		if ($depth === 1) {
			// Wrap depth 1 items in swiper-slide structure
			$output .= '<div class="swiper-slide ' . implode(' ', $classes) . '"><div class="item zoom-img">';
		}

		$output .= $depth === 1 ? '' : '<li' . $class_names . ($item->current_image_url ? ' data-image="' . esc_attr($item->current_image_url) . '"' : '') . '>';

		$atts = array();
		$atts['title']  = !empty($item->attr_title) ? $item->attr_title : '';
		$atts['target'] = !empty($item->target)     ? $item->target     : '';
		$atts['rel']    = !empty($item->xfn)        ? $item->xfn        : '';
		$atts['href']   = !empty($item->url)        ? $item->url        : '';

		$atts = apply_filters('nav_menu_link_attributes', $atts, $item, $args, $depth);

		$attributes = '';
		foreach ($atts as $attr => $value) {
			if (!empty($value)) {
				$value = ('href' === $attr) ? esc_url($value) : esc_attr($value);
				$attributes .= ' ' . $attr . '="' . $value . '"';
			}
		}

		$title = apply_filters('the_title', $item->title, $item->ID);
		$title = apply_filters('nav_menu_item_title', $title, $item, $args, $depth);

		$item_output = $args->before;
		if ($depth === 0 && in_array('menu-item-has-children', $item->classes)) {
			$item_output .= '<div class="title"><a' . $attributes . '>' . $title . '</a><i class="fa-regular fa-angle-down"></i></div>';
		} else if ($depth === 1) {

			$item_output .= '<div class="img"><div class="ratio-[1/1]">' . ($item->current_image_url ? '<img class="lozad" data-src="' . esc_url($item->current_image_url) . '" />' : '') . '</div></div>';
			$item_output .= '<div class="sub-link">' . esc_html($title) . '</div>';
			$item_output .= '<a class="absolute inset-0 z-2" href="' . esc_url($item->url) . '" alt="' . esc_attr($title) . '"></a>';
		} else {
			$item_output .= '<a' . $attributes . '>' . $title . '</a>';
		}
		$item_output .= $args->after;

		$output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
	}

	function start_lvl(&$output, $depth = 0, $args = array())
	{
		if ($depth === 0) {
			$output .= "<div class='dropdown'><div class='dropdown-container'><div class='dropdown-wrapper'><div class='col-left'>";
			$output .= "<div class='sub-title'>" . esc_html($this->current_parent_title) . "</div>";
			$output .= "<div class='description'>" . esc_html($this->current_parent_description) . "</div>";
			$output .= "</div><div class='col-right'>";
			$output .= "<div class='swiper-column-auto relative auto-3-column allow-touchMove allow-mouseWheel'>";
			$output .= "<div class='swiper'><div class='swiper-wrapper'>";
		}
	}

	function end_lvl(&$output, $depth = 0, $args = array())
	{
		if ($depth === 0) {
			$output .= "</div></div><div class='swiper-scrollbar'></div></div></div></div></div></div>";
		}
	}

	function end_el(&$output, $item, $depth = 0, $args = array())
	{
		if ($depth === 1) {
			$output .= "</div></div>"; // Close item zoom-img and swiper-slide
		} else {
			$output .= "</li>\n";
		}
	}
}


function wp_custom_link($items)
{
	$children = [];

	foreach ($items as $item) {
		if ($item->menu_item_parent && $item->menu_item_parent > 0 && $item->type_label === 'Custom Link') {
			array_push($children, [
				'name' => $item->post_title,
				'ID' => $item->ID,
				'parent_ID' => $item->menu_item_parent,
				'url' => '',
				'oldUrl' => $item->url,
				'isModify' => false,
			]);
		}
	}
	$newChildren = [];
	foreach ($children as $child) {
		foreach ($items as $item) {
			if ($item->ID == $child['parent_ID']) {
				$str_length = strlen($child['oldUrl']);
				$is_start_with_hashtag = substr($child['oldUrl'], 0, 1) === '#';
				if ($is_start_with_hashtag && $str_length > 1) {
					$child['url'] = $item->url . '#' . sanitize_title($child['oldUrl']);
				} else {
					$child['url'] = $item->url . '#' . sanitize_title($child['name']);
				}
				$child['isModify'] = true;
				$newChildren[] = $child;
			}
		}
	}
	foreach ($newChildren as $newChild) {
		foreach ($items as $item) {
			if ($item->ID == $newChild['ID']) {
				// log_dump($item->post_name);
				$item->custom_data = $newChild;
				$item->classes[] = 'dynamic-custom-link';
			}
		}
	}
	return $items;
}
add_filter('wp_nav_menu_objects', 'wp_custom_link');




function get_id_translate($templatePath, $postType = 'page')
{
	// templates/Ranking.php
	$pageTemplateID = get_page_by_template($templatePath);
	$pageTranslateID = get_id_language($pageTemplateID, $postType);
	return $pageTranslateID;
}


function compareArrays($array1, $array2)
{
	if (count($array1) !== count($array2)) {
		return false;
	}
	// Sort the arrays by their unique hashes
	usort($array1, function ($a, $b) {
		return strcmp(sha1(json_encode($a)), sha1(json_encode($b)));
	});

	usort($array2, function ($a, $b) {
		return strcmp(sha1(json_encode($a)), sha1(json_encode($b)));
	});

	// Iterate through the arrays and compare the hashes
	for ($i = 0; $i < count($array1); $i++) {
		if (sha1(json_encode($array1[$i])) !== sha1(json_encode($array2[$i]))) {
			return false; // Objects do not match
		}
	}

	return true; // Arrays are the same
}


function get_random_posts($categoryID, $length = 5)
{
	// Define your custom query to get random posts
	$args = array(
		'post_type' => 'post', // Change 'post' to your desired post type
		'posts_per_page' => $length,
		'post_status' => array(
			'publish',
		),
		'cat' => $categoryID,
	);

	$random_query = new WP_Query($args);

	// Check if there are posts
	if ($random_query->have_posts()) {
		$random_posts = $random_query->posts;
	} else {
		$random_posts = array(); // No posts found
	}

	// Restore the original post data
	wp_reset_postdata();

	return $random_posts;
}


function autoRenderHref($value)
{
	if (is_object($value)) {
		if ($value->post_type) {
			return get_permalink($value->ID);
		}
		if ($value->taxonomy) {
			return get_term_link($value->term_id);
		}
	}
	if (is_string($value)) {
		return $value;
	}
	if (is_numeric($value)) {
		$isTaxLink = get_term_link($value);
		if (!is_wp_error($isTaxLink))
			return $isTaxLink;
		else {
			$isPostLink = get_permalink($value);
			if ($isPostLink) return $isPostLink;
		}
	} else {
		return;
	}
}


function getImageUrl($imageID)
{
	if (is_array($imageID))
		$imageID = $imageID["ID"];
	if (is_object($imageID))
		$imageID = $imageID->ID;
	$image_url = wp_get_attachment_image_url($imageID, 'full');
	if (!$image_url) {
		$image_url = get_the_post_thumbnail_url($image_url, 'full');
	}
	return $image_url;
}


function custom_excerpt_more($more)
{
	return ''; // Remove the ellipsis
	// Try other string to see the different
}
add_filter('excerpt_more', 'custom_excerpt_more');

function custom_excerpt_length($length)
{
	return 300; // Set your desired excerpt length here
}
add_filter('excerpt_length', 'custom_excerpt_length');

function get_url_from_acf_link($link)
{
	if (!is_array($link)) {
		return ['attributes' => 'title="' . get_bloginfo('name') . '" rel="nofollow" href="javascript:;"', 'title' => get_bloginfo('name')];
	}
	$attributes = '';
	if ($link['url']) {
		$attributes .= ' href="' . $link['url'] . '"';
	}
	if ($link['target']) {
		$attributes .= ' target="' . $link['target'] . '"';
	}
	if ($link['title']) {
		$attributes .= ' title="' . $link['title'] . '"';
	}
	$title = $link['title'] ?? null;
	return ['attributes' => $attributes, 'title' => $title];
}


function get_attributes_from_term($term)
{
	$term_id = is_object($term) && isset($term->term_id) ? $term->term_id : $term;
	$href = null;
	if (function_exists('get_term_link') && function_exists('is_wp_error')) {
		$term_link = get_term_link($term_id);
		if (!is_wp_error($term_link)) {
			$href = $term_link;
		}
	}
	if (!$href) return 'title="' . get_bloginfo('name') . '" rel="nofollow" href="javascript:;"';
	$term_name = is_object($term) && isset($term->name) ? $term->name : '';
	$attributes = 'href="' . esc_url($href) . '" title="' . esc_attr($term_name) . '"';
	return $attributes;
}


function get_current_file_path()
{
	if (current_user_can('administrator')) {
		// Get the path of the current file being executed
		$current_file = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1)[0]['file'];
		return 'data-path="' . $current_file . '"';
	}
	return '';
}

function my_wpcf7_form_elements($html)
{
	$text = 'Select Option';
	$html = str_replace('asdsadasdsa',  $text, $html);
	return $html;
}
add_filter('wpcf7_form_elements', 'my_wpcf7_form_elements');



function get_single_needed_field($posts_per_page = 10)
{
	global $post;
	$taxonomy = get_post_taxonomies($post);
	$term = wp_get_post_terms($post->ID, $taxonomy);
	$cats = array_map(function ($o) {
		return $o->term_id;
	}, $term);
	$the_query = new WP_query(get_other_post_type($post, $cats, $posts_per_page));
	$post_categories = get_post_primary_category($post->ID, $taxonomy[0]);
	$primary_category = $post_categories['primary_category'];
	return array(
		'other_post_query' => $the_query,
		'primary_category' => $primary_category
	);
}



function custom_mega_menu()
{
	ob_start();
	get_template_part("/components/megaMenu/megaMenu");
	$mega_menu = ob_get_clean();
	return $mega_menu;
}

// Optional: Add custom HTML after the form details
add_action('cfdb7_after_formdetails', 'add_custom_form_details_footer', 10, 1);
function add_custom_form_details_footer($form_post_id)
{
	// Add any additional HTML or functionality here
	// 
?>
	<script>
		// Add any custom JavaScript if needed
		jQuery(document).ready(function($) {
			const imageUpload = $('.welcome-panel-column-container p').filter(function() {
				return $(this).find('b').text() === 'ImagesUpload';
			});
			if (imagep.length) {
				// Get the content and split by <br> tags
				const content = imagep.html();
				const urls = content
					.replace(/<b>ImagesUpload<\/b>:\s*/, '') // Remove the label
					.split('<br>')
					.map(url => url.trim())
					.filter(url => url && !url.includes('</p>') && !url.includes('<p>')); // Filter out empty strings and p tags

				// Replace the content with clickable links
				const links = urls.map(url =>
					`<a href="${url}" target="_blank" class="cfdb7-image-link" style="display: block; color: #0073aa; text-decoration: underline; margin: 5px 0;">
                        ${url.split('/').pop()}
                    </a>`
				).join('');

				imagep.html(`<b>ImagesUpload</b>: <br>${links}`);
			}
		});
	</script>
<?php
}


function get_location_posts($location, $chu_de)
{
	$args = array(
		'post_type' => 'page',
		'posts_per_page' => 8,
		'post_status' => 'publish',
		'meta_query' => array(
			array(
				'key' => 'chu_de',
				'value' => $chu_de,
				'compare' => '=',
			),
			array(
				'key' => 'district_id',
				'value' => $location,
				'compare' => '=',
			),
		),
	);
	return new WP_Query($args);
}


add_filter('acf/settings/remove_wp_meta_box', '__return_false');

// Function to replace [toc] with HTML
function replace_toc_tag($content)
{
	$toc_html = '<div class="table-of-content not-prose">';
	$toc_html .= '<div class="title">' . __('Xem nhanh bài viết', 'canhcamtheme') . '</div>';
	$toc_html .= '<div class="js-toc"></div>';
	$toc_html .= '</div>';
	return str_replace('[toc]', $toc_html, $content);
}
