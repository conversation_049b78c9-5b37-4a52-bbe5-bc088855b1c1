<?php

/**
 * <PERSON><PERSON><PERSON> năng yêu thích cho các thuộc tính văn phòng
 */

// Hành động Ajax để thêm/xóa yêu thích
add_action('wp_ajax_toggle_favorite', 'toggle_favorite_callback');
add_action('wp_ajax_nopriv_toggle_favorite', 'toggle_favorite_callback');

/**
 * Chuyển đổi trạng thái yêu thích cho một bài viết
 */
function toggle_favorite_callback()
{
    // Kiểm tra ID bài viết
    if (!isset($_POST['post_id'])) {
        wp_send_json_error('Thiếu ID bài viết');
    }

    $post_id = intval($_POST['post_id']);

    // Xác minh bài viết tồn tại và đã được xuất bản
    if (!get_post_status($post_id) === 'publish') {
        wp_send_json_error('<PERSON><PERSON><PERSON> viết không hợp lệ');
    }

    $favorites = array();
    $is_in_favorites = false;

    // Kiểm tra xem người dùng đã đăng nhập chưa
    if (is_user_logged_in()) {
        // Lấy danh sách yêu thích của người dùng từ user meta
        $user_id = get_current_user_id();
        $user_favorites = get_user_meta($user_id, 'user_favorites', true);

        if (!empty($user_favorites)) {
            $favorites = is_array($user_favorites) ? $user_favorites : array();
        }

        // Chuyển đổi trạng thái yêu thích
        if (in_array($post_id, $favorites)) {
            $favorites = array_diff($favorites, array($post_id));
            $is_in_favorites = false;
        } else {
            $favorites[] = $post_id;
            $is_in_favorites = true;
        }

        // Cập nhật user meta
        update_user_meta($user_id, 'user_favorites', $favorites);
    }

    // Luôn xử lý yêu thích trong cookie (kể cả khi đã đăng nhập)
    $cookie_favorites = array();

    if (isset($_COOKIE['office_favorites'])) {
        $cookie_data = sanitize_text_field($_COOKIE['office_favorites']);
        $cookie_favorites = explode(',', $cookie_data);
        $cookie_favorites = array_map('intval', $cookie_favorites);
    }

    // Chuyển đổi trạng thái yêu thích trong cookie
    if (in_array($post_id, $cookie_favorites)) {
        $cookie_favorites = array_diff($cookie_favorites, array($post_id));
        $is_in_favorites = false;
    } else {
        $cookie_favorites[] = $post_id;
        $is_in_favorites = true;
    }

    // Lưu vào cookie (hết hạn sau 30 ngày)
    setcookie(
        'office_favorites',
        implode(',', $cookie_favorites),
        time() + (30 * DAY_IN_SECONDS),
        COOKIEPATH,
        COOKIE_DOMAIN,
        is_ssl(),
        true
    );

    wp_send_json_success(array(
        'is_favorite' => $is_in_favorites,
        'favorites' => is_user_logged_in() ? $favorites : $cookie_favorites
    ));
}

/**
 * Kiểm tra xem bài viết có trong danh sách yêu thích của người dùng không
 * 
 * @param int $post_id
 * @return boolean
 */
function is_post_favorite($post_id)
{
    $post_id = intval($post_id);
    $favorites = get_user_favorites();

    return in_array($post_id, $favorites);
}

/**
 * Lấy tất cả danh sách yêu thích của người dùng (từ user meta nếu đã đăng nhập, nếu không thì từ cookie)
 * Khi người dùng đăng nhập, gộp danh sách yêu thích từ cookie với danh sách yêu thích của người dùng
 * 
 * @return array
 */
function get_user_favorites()
{
    $favorites = array();

    // Kiểm tra cookie trước (cho cả người dùng đã đăng nhập và chưa đăng nhập)
    if (isset($_COOKIE['office_favorites'])) {
        $cookie_data = sanitize_text_field($_COOKIE['office_favorites']);
        $cookie_favorites = explode(',', $cookie_data);
        $favorites = array_map('intval', $cookie_favorites);
    }

    // Nếu người dùng đã đăng nhập, lấy danh sách yêu thích từ user meta và gộp với cookie
    if (is_user_logged_in()) {
        $user_id = get_current_user_id();
        $user_favorites = get_user_meta($user_id, 'user_favorites', true);

        if (!empty($user_favorites) && is_array($user_favorites)) {
            // Gộp danh sách yêu thích từ cookie với danh sách yêu thích của người dùng (tránh trùng lặp)
            $favorites = array_unique(array_merge($favorites, $user_favorites));

            // Cập nhật user meta với danh sách yêu thích đã gộp
            update_user_meta($user_id, 'user_favorites', $favorites);

            // Tùy chọn: xóa cookie sau khi gộp (bỏ comment nếu muốn xóa)
            // setcookie('office_favorites', '', time() - 3600, COOKIEPATH, COOKIE_DOMAIN, is_ssl(), true);
        } else if (!empty($favorites)) {
            // Nếu người dùng không có danh sách yêu thích trong meta nhưng có trong cookie, lưu danh sách từ cookie vào user meta
            update_user_meta($user_id, 'user_favorites', $favorites);
        }
    }

    return $favorites;
}

/**
 * Thêm JavaScript cho chức năng yêu thích
 */
function add_favorite_scripts()
{
    // Nạp các script
    wp_enqueue_script('main-office-script', get_template_directory_uri() . '/scripts/main-office.js', array('jquery'), '1.0', true);

    // Truyền URL AJAX và dữ liệu yêu thích vào script
    wp_localize_script('main-office-script', 'favoriteData', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'favorites' => get_user_favorites(),
        'is_logged_in' => is_user_logged_in(),
        'cart_items' => get_user_cart(),
        'cart_count' => count(get_user_cart()),
        'cart_empty' => __('Giỏ hàng của bạn đang trống.', 'canhcamtheme')
    ));
}
add_action('wp_enqueue_scripts', 'add_favorite_scripts');

/**
 * Xử lý đăng nhập người dùng để gộp danh sách yêu thích từ cookie với danh sách yêu thích của người dùng
 */
function merge_favorites_on_login($user_login, $user)
{
    if (isset($_COOKIE['office_favorites'])) {
        $cookie_data = sanitize_text_field($_COOKIE['office_favorites']);
        $cookie_favorites = explode(',', $cookie_data);
        $cookie_favorites = array_map('intval', $cookie_favorites);

        // Lấy danh sách yêu thích hiện tại của người dùng
        $user_favorites = get_user_meta($user->ID, 'user_favorites', true);
        $user_favorites = !empty($user_favorites) && is_array($user_favorites) ? $user_favorites : array();

        // Gộp danh sách yêu thích
        $merged_favorites = array_unique(array_merge($user_favorites, $cookie_favorites));

        // Cập nhật user meta
        update_user_meta($user->ID, 'user_favorites', $merged_favorites);

        // Tùy chọn: xóa cookie sau khi gộp
        // setcookie('office_favorites', '', time() - 3600, COOKIEPATH, COOKIE_DOMAIN, is_ssl(), true);
    }
}
add_action('wp_login', 'merge_favorites_on_login', 10, 2);

/**
 * Chức năng so sánh (compare) cho các thuộc tính văn phòng
 */

// Hành động Ajax để thêm/xóa danh sách so sánh
add_action('wp_ajax_toggle_compare', 'toggle_compare_callback');
add_action('wp_ajax_nopriv_toggle_compare', 'toggle_compare_callback');

/**
 * Chuyển đổi trạng thái so sánh cho một bài viết
 */
function toggle_compare_callback()
{
    // Kiểm tra ID bài viết
    if (!isset($_POST['post_id'])) {
        wp_send_json_error('Thiếu ID bài viết');
    }

    $post_id = intval($_POST['post_id']);

    // Xác minh bài viết tồn tại và đã được xuất bản
    if (!get_post_status($post_id) === 'publish') {
        wp_send_json_error('Bài viết không hợp lệ');
    }

    // Lấy post type của bài viết hiện tại
    $current_post_type = get_post_type($post_id);

    // Danh sách các post type hợp lệ để so sánh
    $valid_post_types = array('office-rent', 'office-rent-full', 'business-space', 'real-estate');

    // Kiểm tra xem post type hiện tại có hợp lệ không
    if (!in_array($current_post_type, $valid_post_types)) {
        wp_send_json_error('Loại bài viết không hỗ trợ so sánh');
    }

    $compares = array();
    $is_in_compares = false;

    // Xử lý so sánh
    $cookie_compares = array();

    if (isset($_COOKIE['office_compares'])) {
        $cookie_data = sanitize_text_field($_COOKIE['office_compares']);
        $cookie_compares = explode(',', $cookie_data);
        $cookie_compares = array_filter(array_map('intval', $cookie_compares));
    }

    // Kiểm tra xem có bài viết nào trong danh sách so sánh không
    if (!empty($cookie_compares)) {
        // Lấy post type của bài viết đầu tiên trong danh sách so sánh
        $first_compare_post_type = get_post_type($cookie_compares[0]);

        // Nếu post type khác nhau, trả về lỗi
        if ($first_compare_post_type !== $current_post_type) {
            wp_send_json_error(array(
                'message' => __('Vui lòng chọn sản phẩm cùng chuyển mục', 'canhcamtheme'),
                'post_type' => $current_post_type,
                'first_compare_post_type' => $first_compare_post_type
            ));
        }
    }

    // Chuyển đổi trạng thái so sánh
    if (in_array($post_id, $cookie_compares)) {
        $cookie_compares = array_diff($cookie_compares, array($post_id));
        $is_in_compares = false;
    } else {
        // Kiểm tra giới hạn số lượng bài viết có thể so sánh (tối đa 4)
        // if (count($cookie_compares) >= 4) {
        //     wp_send_json_error(array(
        //         'message' => 'Chỉ được phép so sánh tối đa 4 bài viết',
        //         'compares' => $cookie_compares
        //     ));
        // }

        $cookie_compares[] = $post_id;
        $is_in_compares = true;
    }

    // Lưu vào cookie (hết hạn sau 7 ngày)
    setcookie(
        'office_compares',
        implode(',', $cookie_compares),
        time() + (7 * DAY_IN_SECONDS),
        COOKIEPATH,
        COOKIE_DOMAIN,
        is_ssl(),
        true
    );

    wp_send_json_success(array(
        'is_compare' => $is_in_compares,
        'compares' => $cookie_compares,
        'post_type' => $current_post_type
    ));
}

/**
 * Kiểm tra xem bài viết có trong danh sách so sánh không
 * 
 * @param int $post_id
 * @return boolean
 */
function is_post_in_compare($post_id)
{
    $post_id = intval($post_id);
    $compares = get_compare_list();

    return in_array($post_id, $compares);
}

/**
 * Lấy danh sách các bài viết trong chức năng so sánh
 * 
 * @return array
 */
function get_compare_list()
{
    $compares = array();

    if (isset($_COOKIE['office_compares'])) {
        $cookie_data = sanitize_text_field($_COOKIE['office_compares']);
        $compares = explode(',', $cookie_data);
        $compares = array_filter(array_map('intval', $compares));
    }

    return $compares;
}

/**
 * Lấy post type từ danh sách so sánh (trả về false nếu không có bài viết nào hoặc có nhiều post type)
 */
function get_compare_post_type()
{
    $compares = get_compare_list();

    if (empty($compares)) {
        return false;
    }

    $first_post_type = get_post_type($compares[0]);

    // Kiểm tra xem tất cả bài viết đều cùng post type
    foreach ($compares as $post_id) {
        if (get_post_type($post_id) !== $first_post_type) {
            return false;
        }
    }

    return $first_post_type;
}

/**
 * Bổ sung JavaScript cho chức năng so sánh
 */
function add_compare_scripts()
{
    // Script đã được nạp trong add_favorite_scripts

    // Truyền dữ liệu so sánh vào script
    wp_localize_script('main-office-script', 'compareData', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'compares' => get_compare_list(),
        'post_type' => get_compare_post_type(),
        'valid_post_types' => array('office-rent', 'office-rent-full', 'business-space', 'real-estate')
    ));
}
add_action('wp_enqueue_scripts', 'add_compare_scripts');

/**
 * Hàm để xóa tất cả bài viết khỏi danh sách so sánh
 */
add_action('wp_ajax_clear_compares', 'clear_compares_callback');
add_action('wp_ajax_nopriv_clear_compares', 'clear_compares_callback');

function clear_compares_callback()
{
    // Xóa cookie so sánh
    setcookie(
        'office_compares',
        '',
        time() - 3600,
        COOKIEPATH,
        COOKIE_DOMAIN,
        is_ssl(),
        true
    );

    wp_send_json_success(array(
        'message' => 'Đã xóa tất cả bài viết khỏi danh sách so sánh',
        'compares' => array()
    ));
}


/**
 * Ajax handler for getting office listings based on area calculation
 */
function get_office_listings_by_area()
{
    // Get parameters from the request
    $dien_tich = isset($_POST['dien_tich']) ? intval($_POST['dien_tich']) : 0;
    $so_luong = isset($_POST['so_luong']) ? intval($_POST['so_luong']) : 0;
    $muc_dien_tich = isset($_POST['muc_dien_tich']) ? floatval($_POST['muc_dien_tich']) : 0;
    $phong_hop = isset($_POST['phong_hop']) ? floatval($_POST['phong_hop']) : 0;
    $pantry = isset($_POST['pantry']) ? floatval($_POST['pantry']) : 0;

    // Thiết lập khoảng diện tích cho tìm kiếm
    // Có thể điều chỉnh biên độ theo nhu cầu, ví dụ: +/- 20% của diện tích
    $min_area = max(0, $dien_tich * 0.8); // 80% của diện tích nhập vào
    $max_area = $dien_tich * 1.2; // 120% của diện tích nhập vào

    // dynamic_field_thong_so_toa_nha_hang-toa-nha
    // dynamic_field_ts_toa_nha_tron_goi_hang-toa-nha

    // Query bài viết từ cả hai post type
    $args_offices = array(
        'post_type' => array('office-rent', 'office-rent-full'),
        'posts_per_page' => 24,
        'paged' => 1,
        'meta_query' => array(
            'relation' => 'OR',
            // Tìm kiếm theo numeric_area cho office-rent
            array(
                'key' => 'numeric_area',
                'value' => array($min_area, $max_area),
                'compare' => 'BETWEEN',
                'type' => 'NUMERIC'
            ),
            // Tìm kiếm theo numeric_seats cho office-rent-full
            array(
                'key' => 'numeric_seats',
                'value' => array($min_area, $max_area),
                'compare' => 'BETWEEN',
                'type' => 'NUMERIC'
            )
        ),
        'meta_key' => 'price_product',
        'orderby' => 'meta_value_num',
        'order' => 'ASC'
    );

    $query = new WP_Query($args_offices);

    ob_start();

    if ($query->have_posts()) {
        while ($query->have_posts()) : $query->the_post();
            // Sử dụng template hiển thị văn phòng
            $post_id = get_the_ID();
            if (get_post_type($post_id) == 'office-rent-full') {
                get_template_part('components/boxProduct/box-rent-2', '', ['post_id' => $post_id]);
            } else {
                get_template_part('components/boxProduct/box-rent-1', '', ['post_id' => $post_id]);
            }
        endwhile;
        wp_reset_postdata();
    } else {
        echo '<div class="col-span-full text-center">Không tìm thấy văn phòng phù hợp với diện tích ' . $dien_tich . 'm<sup>2</sup>.</div>';
    }

    $html = ob_get_clean();

    wp_send_json_success($html);
}
add_action('wp_ajax_get_office_listings_by_area', 'get_office_listings_by_area');
add_action('wp_ajax_nopriv_get_office_listings_by_area', 'get_office_listings_by_area');



// Add this at the end of the file

/**
 * AJAX handler for updating account information
 */
add_action('wp_ajax_update_account_info', 'update_account_info_callback');

function update_account_info_callback()
{
    // Parse the form data
    parse_str($_POST['formData'], $form_data);

    // Verify nonce
    if (!isset($form_data['account_info_nonce']) || !wp_verify_nonce($form_data['account_info_nonce'], 'update_account_info')) {
        wp_send_json_error(array('message' => __('Lỗi bảo mật. Vui lòng tải lại trang và thử lại.', 'canhcamtheme')));
    }

    // Get current user
    $current_user = wp_get_current_user();
    if (!$current_user->exists()) {
        wp_send_json_error(array('message' => __('Bạn cần đăng nhập để thực hiện thao tác này.', 'canhcamtheme')));
    }

    $response = array('message' => '');
    $is_updated = false;

    // Check if display name is being updated
    if (!empty($form_data['display_name']) && $form_data['display_name'] !== $current_user->display_name) {
        $user_id = $current_user->ID;

        // Update user display name
        $result = wp_update_user(array(
            'ID' => $user_id,
            'display_name' => sanitize_text_field($form_data['display_name'])
        ));

        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => __('Không thể cập nhật tên hiển thị.', 'canhcamtheme')));
        }

        $response['new_display_name'] = sanitize_text_field($form_data['display_name']);
        $response['message'] = __('Cập nhật thông tin tài khoản thành công.', 'canhcamtheme');
        $is_updated = true;
    }

    // Check if password is being updated
    if (!empty($form_data['current_password']) && !empty($form_data['new_password']) && !empty($form_data['confirm_password'])) {
        // Verify passwords match
        if ($form_data['new_password'] !== $form_data['confirm_password']) {
            wp_send_json_error(array('message' => __('Mật khẩu mới và mật khẩu xác nhận không khớp.', 'canhcamtheme')));
        }

        // Verify current password
        $user = wp_get_current_user();
        if (!wp_check_password($form_data['current_password'], $user->user_pass, $user->ID)) {
            wp_send_json_error(array('message' => __('Mật khẩu hiện tại không đúng.', 'canhcamtheme')));
        }

        // Update password
        wp_set_password($form_data['new_password'], $user->ID);

        // Log the user back in
        wp_set_auth_cookie($user->ID, true);

        $response['message'] = $is_updated ?
            __('Cập nhật thông tin tài khoản và mật khẩu thành công.', 'canhcamtheme') :
            __('Cập nhật mật khẩu thành công.', 'canhcamtheme');
        $is_updated = true;
    }

    // If nothing was updated
    if (!$is_updated) {
        wp_send_json_error(array('message' => __('Không có thông tin nào được thay đổi.', 'canhcamtheme')));
    }

    wp_send_json_success($response);
}




// OPTIMIZED: Get location data with counts using direct SQL queries
function get_locations_with_counts($location_type, $reference_field, $reference_value, $post_types)
{
    global $wpdb;

    // Build the meta query part
    $meta_condition = '';
    if ($reference_field) {
        $meta_condition = $wpdb->prepare(
            "AND EXISTS (
                SELECT 1 FROM {$wpdb->postmeta} rm 
                WHERE rm.post_id = p.ID 
                AND rm.meta_key = %s 
                AND rm.meta_value = %s
            )",
            $reference_field,
            $reference_value
        );
    }

    // Get all location items (first query)
    $locations_query = $wpdb->prepare(
        "SELECT p.ID, p.post_title
         FROM {$wpdb->posts} p
         WHERE p.post_type = %s
         AND p.post_status = 'publish'
         {$meta_condition}
         ORDER BY p.post_title ASC",
        $location_type
    );

    $locations = $wpdb->get_results($locations_query);
    if (empty($locations)) {
        return array();
    }

    // Extract IDs for the IN clause
    $location_ids = wp_list_pluck($locations, 'ID');
    $location_ids_placeholders = implode(',', array_fill(0, count($location_ids), '%d'));

    // Build the post types query part
    $post_types_placeholders = implode(',', array_fill(0, count($post_types), '%s'));

    // The meta key for the location relationship
    $relation_meta_key = "_office_{$location_type}";

    // Get counts with a single query (second query)
    $counts_query = $wpdb->prepare(
        "SELECT pm.meta_value AS location_id, COUNT(DISTINCT p.ID) AS count
         FROM {$wpdb->posts} p
         INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
         WHERE p.post_type IN ({$post_types_placeholders})
         AND p.post_status = 'publish'
         AND pm.meta_key = %s
         AND pm.meta_value IN ({$location_ids_placeholders})
         GROUP BY pm.meta_value",
        array_merge($post_types, [$relation_meta_key], $location_ids)
    );

    $counts = $wpdb->get_results($counts_query, OBJECT_K);

    // Combine the data
    $results = array();
    foreach ($locations as $location) {
        $location_id = $location->ID;
        if (isset($counts[$location_id]) && $counts[$location_id]->count > 0) {
            $results[] = array(
                'id' => $location_id,
                'title' => $location->post_title,
                'count' => intval($counts[$location_id]->count),
                'permalink' => get_permalink($location_id)
            );
        }
    }

    // Sort by count (highest first)
    usort($results, function ($a, $b) {
        return $b['count'] - $a['count'];
    });

    return $results;
}


/**
 * Add to Cart Functionality
 */
add_action('wp_ajax_add_to_cart', 'add_to_cart_callback');
add_action('wp_ajax_nopriv_add_to_cart', 'add_to_cart_callback');

/**
 * Handle adding a post to the cart
 */
function add_to_cart_callback() {
    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error(array(
            'message' => __('Bạn cần đăng nhập để thêm vào giỏ hàng', 'canhcamtheme')
        ));
        return;
    }

    // Check for post ID
    if (!isset($_POST['post_id'])) {
        wp_send_json_error(array(
            'message' => __('Thiếu ID sản phẩm', 'canhcamtheme')
        ));
        return;
    }

    $post_id = intval($_POST['post_id']);

    // Verify post exists and is published
    if (!get_post_status($post_id) === 'publish') {
        wp_send_json_error(array(
            'message' => __('Sản phẩm không hợp lệ', 'canhcamtheme')
        ));
        return;
    }

    $user_id = get_current_user_id();
    $cart_items = get_user_meta($user_id, 'user_cart', true);
    
    if (!is_array($cart_items)) {
        $cart_items = array();
    }

    // Check if post is already in cart
    if (in_array($post_id, $cart_items)) {
        wp_send_json_error(array(
            'message' => __('Sản phẩm đã có trong giỏ hàng', 'canhcamtheme'),
            'cart_count' => count($cart_items)
        ));
        return;
    }

    // Add to cart
    $cart_items[] = $post_id;
    update_user_meta($user_id, 'user_cart', $cart_items);

    // Return success with cart count
    wp_send_json_success(array(
        'message' => __('Đã thêm vào giỏ hàng', 'canhcamtheme'),
        'cart_count' => count($cart_items)
    ));
}

/**
 * Get user's cart items
 *
 * @return array
 */
function get_user_cart() {
    if (!is_user_logged_in()) {
        return array();
    }

    $user_id = get_current_user_id();
    $cart_items = get_user_meta($user_id, 'user_cart', true);
    
    if (!is_array($cart_items)) {
        $cart_items = array();
    }

    return $cart_items;
}



/**
 * Update all office posts status to draft
 */
// function update_all_offices_to_draft() {
//     $args = array(
//         'post_type' => array('office-rent', 'office-rent-full'),
//         'posts_per_page' => -1,
//         'post_status' => 'publish',
//         'fields' => 'ids'
//     );

//     $office_posts = get_posts($args);

//     if (!empty($office_posts)) {
//         foreach ($office_posts as $post_id) {
//             wp_update_post(array(
//                 'ID' => $post_id,
//                 'post_status' => 'draft'
//             ));
//         }
//         return true;
//     }

//     return false;
// }
// update_all_offices_to_draft();




// Thêm vào file functions.php hoặc tạo plugin riêng
function convert_area_meta() {
    global $wpdb;

    // Lấy tất cả bản ghi meta với meta_key phù hợp
    $results = $wpdb->get_results(
        "SELECT post_id, meta_value FROM $wpdb->postmeta 
        WHERE meta_key = 'dynamic_field_thong_tin_dien_tich_va_gia_thue_dien-tich-trong'"
    );
    
    $processed = 0;
    $errors = 0;

    foreach ($results as $result) {
        $post_id = $result->post_id;
        $area_string = $result->meta_value;
        
        // Bỏ qua nếu không có giá trị
        if (empty($area_string)) {
            continue;
        }
        
        // Xóa giá trị meta cũ
        delete_post_meta($post_id, 'numeric_area');
        delete_post_meta($post_id, 'min_area');
        delete_post_meta($post_id, 'max_area');
        delete_post_meta($post_id, 'area_values');
        
        // Loại bỏ ký tự không phải số, dấu chấm, dấu phẩy, dấu gạch ngang hoặc khoảng trắng
        $clean_area = preg_replace('/[^0-9\-,.\s]/', '', $area_string);
        
        // Chuẩn hóa dấu chấm trong số hàng nghìn
        $clean_area = preg_replace('/(\d)\.(\d{3})/', '$1$2', $clean_area);
        
        // Chuyển dấu phẩy thập phân thành dấu chấm (nếu cần)
        $clean_area = str_replace(',', '.', $clean_area);
        
        // Tách các giá trị theo dấu gạch ngang
        $areas = preg_split('/\s*\-\s*/', $clean_area);
        
        // Mảng lưu các giá trị đã xử lý
        $processed_areas = [];
        
        foreach ($areas as $area) {
            $area = trim($area);
            if (is_numeric($area)) {
                // Chuyển đổi sang số thực
                $area_value = floatval($area);
                
                // Chỉ lưu giá trị lớn hơn 0
                if ($area_value > 0) {
                    $processed_areas[] = $area_value;
                    
                    // No longer adding multiple numeric_area values
                }
            }
        }
        
        // Store the data differently
        if (!empty($processed_areas)) {
            // Store all values in an array
            update_post_meta($post_id, 'area_values', $processed_areas);
            
            // Add min and max area for range queries
            $min_area = min($processed_areas);
            $max_area = max($processed_areas);
            update_post_meta($post_id, 'min_area', $min_area);
            update_post_meta($post_id, 'max_area', $max_area);
            
            // Optional: keep the smallest area in numeric_area for compatibility
            update_post_meta($post_id, 'numeric_area', $min_area);
            $processed++;
        } else {
            $errors++;
        }
    }

    return [
        'success' => true,
        'processed' => $processed,
        'errors' => $errors,
        'message' => sprintf('Đã xử lý %d giá trị diện tích, có %d lỗi.', $processed, $errors)
    ];
}
// Thêm API endpoint để gọi từ admin
function area_meta_conversion_callback() {
    // Kiểm tra quyền admin
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Không đủ quyền thực hiện chức năng này.']);
        return;
    }
    
    $result = convert_area_meta();
    wp_send_json_success($result);
}
add_action('wp_ajax_convert_area_meta', 'area_meta_conversion_callback');

// Thêm menu trong admin để chạy công cụ chuyển đổi
function add_area_conversion_menu() {
    add_management_page(
        'Chuyển đổi dữ liệu diện tích',
        'Chuyển đổi diện tích',
        'manage_options',
        'area-conversion',
        'render_area_conversion_page'
    );
}
add_action('admin_menu', 'add_area_conversion_menu');

// Render trang admin
function render_area_conversion_page() {
    ?>
    <div class="wrap">
        <h1>Chuyển đổi dữ liệu diện tích</h1>
        <p>Công cụ này sẽ tách các giá trị diện tích từ trường "dynamic_field_thong_tin_dien_tich_va_gia_thue_dien-tich-trong" 
           và tạo các meta mới để phục vụ cho việc lọc và tính toán.</p>
        <p>Mỗi diện tích sẽ có hai giá trị: giá trị gốc (numeric_area).</p>
        
        <div class="notice notice-warning">
            <p><strong>Lưu ý:</strong> Hãy backup database trước khi thực hiện chuyển đổi.</p>
        </div>
        
        <button id="convert-area-btn" class="button button-primary">Bắt đầu chuyển đổi</button>
        
        <div id="conversion-results" style="margin-top: 20px; display: none;">
            <h3>Kết quả chuyển đổi:</h3>
            <div id="results-content"></div>
        </div>
        
        <script>
            jQuery(document).ready(function($) {
                $('#convert-area-btn').on('click', function() {
                    $(this).prop('disabled', true).text('Đang xử lý...');
                    
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'convert_area_meta'
                        },
                        success: function(response) {
                            $('#conversion-results').show();
                            
                            if (response.success) {
                                var content = '<div class="notice notice-success"><p>' + response.data.message + '</p></div>';
                                content += '<p>Số lượng xử lý: ' + response.data.processed + '</p>';
                                content += '<p>Số lỗi: ' + response.data.errors + '</p>';
                                $('#results-content').html(content);
                            } else {
                                $('#results-content').html('<div class="notice notice-error"><p>Có lỗi xảy ra: ' + response.data.message + '</p></div>');
                            }
                            
                            $('#convert-area-btn').prop('disabled', false).text('Bắt đầu chuyển đổi');
                        },
                        error: function() {
                            $('#conversion-results').show();
                            $('#results-content').html('<div class="notice notice-error"><p>Có lỗi xảy ra khi gọi AJAX.</p></div>');
                            $('#convert-area-btn').prop('disabled', false).text('Bắt đầu chuyển đổi');
                        }
                    });
                });
            });
        </script>
    </div>
    <?php
}


/**
 * Chuyển đổi trường số chỗ ngồi cho post type office-rent-full
 */
function convert_seats_meta() {
    global $wpdb;

    // Lấy tất cả bản ghi meta với meta_key phù hợp, chỉ cho post type office-rent-full
    $results = $wpdb->get_results(
        "SELECT pm.post_id, pm.meta_value 
        FROM $wpdb->postmeta pm
        JOIN $wpdb->posts p ON pm.post_id = p.ID
        WHERE pm.meta_key = 'dynamic_field_dich_vu_tron_goi_so-cho-ngoi'
        AND p.post_type = 'office-rent-full'"
    );
    
    $processed = 0;
    $errors = 0;

    foreach ($results as $result) {
        $post_id = $result->post_id;
        $seats_string = $result->meta_value;
        
        // Bỏ qua nếu không có giá trị
        if (empty($seats_string)) {
            continue;
        }
        
        // Xóa giá trị meta cũ nếu có
        delete_post_meta($post_id, 'numeric_seats');
        delete_post_meta($post_id, 'min_seats');
        delete_post_meta($post_id, 'max_seats');
        delete_post_meta($post_id, 'seats_values');
        
        // Loại bỏ ký tự không phải số, dấu chấm, dấu phẩy, dấu gạch ngang hoặc khoảng trắng
        $clean_seats = preg_replace('/[^0-9\-,.\s]/', '', $seats_string);
        
        // Chuẩn hóa dấu chấm trong số hàng nghìn
        $clean_seats = preg_replace('/(\d)\.(\d{3})/', '$1$2', $clean_seats);
        
        // Chuyển dấu phẩy thập phân thành dấu chấm (nếu cần)
        $clean_seats = str_replace(',', '.', $clean_seats);
        
        // Tách các giá trị theo dấu gạch ngang
        $seats = preg_split('/\s*\-\s*/', $clean_seats);
        
        // Mảng lưu các giá trị đã xử lý
        $processed_seats = [];
        
        foreach ($seats as $seat) {
            $seat = trim($seat);
            if (is_numeric($seat)) {
                // Chuyển đổi sang số nguyên
                $seat_value = intval($seat);
                
                // Chỉ lưu giá trị lớn hơn 0
                if ($seat_value > 0) {
                    $processed_seats[] = $seat_value;
                }
            }
        }
        
        // Lưu dữ liệu đã xử lý
        if (!empty($processed_seats)) {
            // Lưu tất cả giá trị vào một mảng
            update_post_meta($post_id, 'seats_values', $processed_seats);
            
            // Thêm số chỗ ngồi nhỏ nhất và lớn nhất cho truy vấn phạm vi
            $min_seats = min($processed_seats);
            $max_seats = max($processed_seats);
            update_post_meta($post_id, 'min_seats', $min_seats);
            update_post_meta($post_id, 'max_seats', $max_seats);
            
            // Lưu giá trị số chỗ ngồi nhỏ nhất làm numeric_seats để tương thích
            update_post_meta($post_id, 'numeric_seats', $min_seats);
            $processed++;
        } else {
            $errors++;
        }
    }

    return [
        'success' => true,
        'processed' => $processed,
        'errors' => $errors,
        'message' => sprintf('Đã xử lý %d giá trị số chỗ ngồi, có %d lỗi.', $processed, $errors)
    ];
}

/**
 * AJAX endpoint để chuyển đổi số chỗ ngồi
 */
function seats_meta_conversion_callback() {
    // Kiểm tra quyền admin
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Không đủ quyền thực hiện chức năng này.']);
        return;
    }
    
    $result = convert_seats_meta();
    wp_send_json_success($result);
}
add_action('wp_ajax_convert_seats_meta', 'seats_meta_conversion_callback');

/**
 * Thêm tính năng chuyển đổi số chỗ ngồi vào trang admin hiện có
 */
function update_area_conversion_page() {
    // Không tạo trang mới, chỉ bổ sung vào trang hiện có
    add_action('admin_head-tools_page_area-conversion', 'add_seats_conversion_script');
}
add_action('admin_init', 'update_area_conversion_page');

/**
 * Thêm script cho chức năng chuyển đổi số chỗ ngồi vào trang admin
 */
function add_seats_conversion_script() {
    ?>
    <script>
        jQuery(document).ready(function($) {
            // Thêm button chuyển đổi số chỗ ngồi
            $('.wrap h1').after('<div class="seats-conversion" style="margin-top: 20px;">' +
                '<h2>Chuyển đổi số chỗ ngồi</h2>' +
                '<p>Công cụ này sẽ chuyển đổi các giá trị từ trường "dynamic_field_dich_vu_tron_goi_so-cho-ngoi" ' +
                'thành các meta mới để phục vụ cho việc lọc và tính toán trên post type office-rent-full.</p>' +
                '<div class="notice notice-warning">' +
                '<p><strong>Lưu ý:</strong> Hãy backup database trước khi thực hiện chuyển đổi.</p>' +
                '</div>' +
                '<button id="convert-seats-btn" class="button button-primary">Bắt đầu chuyển đổi số chỗ ngồi</button>' +
                '<div id="seats-conversion-results" style="margin-top: 20px; display: none;">' +
                '<h3>Kết quả chuyển đổi số chỗ ngồi:</h3>' +
                '<div id="seats-results-content"></div>' +
                '</div>' +
                '</div>');
            
            // Xử lý sự kiện click chuyển đổi số chỗ ngồi
            $('#convert-seats-btn').on('click', function() {
                $(this).prop('disabled', true).text('Đang xử lý...');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'convert_seats_meta'
                    },
                    success: function(response) {
                        $('#seats-conversion-results').show();
                        
                        if (response.success) {
                            var content = '<div class="notice notice-success"><p>' + response.data.message + '</p></div>';
                            content += '<p>Số lượng xử lý: ' + response.data.processed + '</p>';
                            content += '<p>Số lỗi: ' + response.data.errors + '</p>';
                            $('#seats-results-content').html(content);
                        } else {
                            $('#seats-results-content').html('<div class="notice notice-error"><p>Có lỗi xảy ra: ' + response.data.message + '</p></div>');
                        }
                        
                        $('#convert-seats-btn').prop('disabled', false).text('Bắt đầu chuyển đổi số chỗ ngồi');
                    },
                    error: function() {
                        $('#seats-conversion-results').show();
                        $('#seats-results-content').html('<div class="notice notice-error"><p>Có lỗi xảy ra khi gọi AJAX.</p></div>');
                        $('#convert-seats-btn').prop('disabled', false).text('Bắt đầu chuyển đổi số chỗ ngồi');
                    }
                });
            });
        });
    </script>
    <?php
}





/**
 * Lấy danh sách sản phẩm trong giỏ hàng
 * 
 * @param int $user_id ID của người dùng (mặc định là người dùng hiện tại)
 * @return array Mảng các bài viết trong giỏ hàng
 */
function get_cart_products($user_id = 0) {
    // Nếu không có user_id, lấy user hiện tại
    if (!$user_id && is_user_logged_in()) {
        $user_id = get_current_user_id();
    }
    
    // Nếu không có user, trả về mảng rỗng
    if (!$user_id) {
        return array();
    }
    
    // Lấy danh sách ID sản phẩm từ user meta
    $cart_items = get_user_meta($user_id, 'user_cart', true);
    if (!is_array($cart_items) || empty($cart_items)) {
        return array();
    }
    
    // Lọc các ID không hợp lệ
    $cart_items = array_filter($cart_items, 'is_numeric');
    
    // Lấy thông tin chi tiết các sản phẩm
    $args = array(
        'post_type' => array('office-rent', 'office-rent-full', 'business-space', 'real-estate'),
        'post_status' => 'publish',
        'posts_per_page' => -1,
        'post__in' => $cart_items,
        'orderby' => 'post__in' // Giữ nguyên thứ tự như trong giỏ hàng
    );
    
    $query = new WP_Query($args);
    
    // Trả về danh sách sản phẩm
    return $query->posts;
}

/**
 * Kiểm tra xem một sản phẩm có trong giỏ hàng không
 * 
 * @param int $post_id ID của bài viết cần kiểm tra
 * @return boolean true nếu sản phẩm có trong giỏ hàng, false nếu không
 */
function is_product_in_cart($post_id) {
    if (!is_user_logged_in()) {
        return false;
    }
    
    $user_id = get_current_user_id();
    $cart_items = get_user_meta($user_id, 'user_cart', true);
    
    if (!is_array($cart_items)) {
        return false;
    }
    
    return in_array(intval($post_id), $cart_items);
}

/**
 * Xóa sản phẩm khỏi giỏ hàng
 * 
 * @param int $post_id ID của bài viết cần xóa
 * @return boolean true nếu xóa thành công, false nếu không
 */
function remove_from_cart($post_id) {
    if (!is_user_logged_in()) {
        return false;
    }
    
    $user_id = get_current_user_id();
    $cart_items = get_user_meta($user_id, 'user_cart', true);
    
    if (!is_array($cart_items)) {
        return false;
    }
    
    // Tìm vị trí của sản phẩm trong giỏ hàng
    $key = array_search(intval($post_id), $cart_items);
    
    // Nếu không tìm thấy
    if ($key === false) {
        return false;
    }
    
    // Xóa sản phẩm khỏi mảng
    unset($cart_items[$key]);
    
    // Cập nhật user meta
    update_user_meta($user_id, 'user_cart', array_values($cart_items));
    
    return true;
}

/**
 * Xử lý AJAX để xóa sản phẩm khỏi giỏ hàng
 */
function remove_from_cart_callback() {
    // Kiểm tra người dùng đã đăng nhập chưa
    if (!is_user_logged_in()) {
        wp_send_json_error(array(
            'message' => __('Bạn cần đăng nhập để thực hiện chức năng này', 'canhcamtheme')
        ));
        return;
    }
    
    // Kiểm tra ID bài viết
    if (!isset($_POST['post_id'])) {
        wp_send_json_error(array(
            'message' => __('Thiếu ID bài viết', 'canhcamtheme')
        ));
        return;
    }
    
    $post_id = intval($_POST['post_id']);
    
    // Xóa sản phẩm khỏi giỏ hàng
    $success = remove_from_cart($post_id);
    
    if ($success) {
        // Lấy số lượng sản phẩm còn lại trong giỏ hàng
        $cart_count = count(get_user_meta(get_current_user_id(), 'user_cart', true) ?: array());
        
        wp_send_json_success(array(
            'message' => __('Đã xóa sản phẩm khỏi giỏ hàng', 'canhcamtheme'),
            'cart_count' => $cart_count
        ));
    } else {
        wp_send_json_error(array(
            'message' => __('Không thể xóa sản phẩm khỏi giỏ hàng', 'canhcamtheme')
        ));
    }
}
add_action('wp_ajax_remove_from_cart', 'remove_from_cart_callback');

/**
 * Xóa tất cả sản phẩm khỏi giỏ hàng
 * 
 * @return boolean true nếu xóa thành công, false nếu không
 */
function clear_cart() {
    if (!is_user_logged_in()) {
        return false;
    }
    
    $user_id = get_current_user_id();
    
    // Cập nhật user meta với mảng rỗng
    update_user_meta($user_id, 'user_cart', array());
    
    return true;
}

/**
 * Xử lý AJAX để xóa tất cả sản phẩm khỏi giỏ hàng
 */
function clear_cart_callback() {
    // Kiểm tra người dùng đã đăng nhập chưa
    if (!is_user_logged_in()) {
        wp_send_json_error(array(
            'message' => __('Bạn cần đăng nhập để thực hiện chức năng này', 'canhcamtheme')
        ));
        return;
    }
    
    // Xóa tất cả sản phẩm khỏi giỏ hàng
    $success = clear_cart();
    
    if ($success) {
        wp_send_json_success(array(
            'message' => __('Đã xóa tất cả sản phẩm khỏi giỏ hàng', 'canhcamtheme'),
            'cart_count' => 0
        ));
    } else {
        wp_send_json_error(array(
            'message' => __('Không thể xóa giỏ hàng', 'canhcamtheme')
        ));
    }
}
add_action('wp_ajax_clear_cart', 'clear_cart_callback');


// ... existing code ...

/**
 * ===================================================================
 * CHỨC NĂNG ĐÁNH GIÁ (RATING) ĐỚN GIẢN
 * ===================================================================
 */

// Đăng ký AJAX actions cho rating
add_action('wp_ajax_submit_rating', 'submit_rating_callback');
add_action('wp_ajax_nopriv_submit_rating', 'submit_rating_callback');

/**
 * Xử lý AJAX để gửi đánh giá
 */
function submit_rating_callback() {
    // Kiểm tra các tham số bắt buộc
    if (!isset($_POST['post_id']) || !isset($_POST['rating'])) {
        wp_send_json_error(array(
            'message' => 'Thiếu thông tin đánh giá.'
        ));
        return;
    }

    $post_id = intval($_POST['post_id']);
    $rating = intval($_POST['rating']);

    // Kiểm tra post tồn tại và được publish
    if (!get_post_status($post_id) || get_post_status($post_id) !== 'publish') {
        wp_send_json_error(array(
            'message' => 'Bài viết không hợp lệ.'
        ));
        return;
    }

    // Kiểm tra rating hợp lệ (1-5 sao)
    if ($rating < 1 || $rating > 5) {
        wp_send_json_error(array(
            'message' => 'Đánh giá không hợp lệ.'
        ));
        return;
    }

    // Kiểm tra điều kiện: chỉ cho phép đánh giá 4-5 sao
    if ($rating < 4) {
        wp_send_json_error(array(
            'message' => 'Chúng tôi chỉ chấp nhận đánh giá từ 4 sao trở lên. Cảm ơn bạn đã quan tâm!',
            'type' => 'rating_too_low'
        ));
        return;
    }

    // Lấy IP để tránh spam
    $user_ip = $_SERVER['REMOTE_ADDR'];
    $user_id = is_user_logged_in() ? get_current_user_id() : 0;

    // Kiểm tra xem đã đánh giá chưa
    if (has_user_rated_simple($post_id, $user_ip, $user_id)) {
        wp_send_json_error(array(
            'message' => 'Bạn đã đánh giá bài viết này rồi.'
        ));
        return;
    }

    // Lưu đánh giá vào meta data
    $current_ratings = get_post_meta($post_id, '_post_ratings', true);
    if (!is_array($current_ratings)) {
        $current_ratings = array();
    }

    // Thêm đánh giá mới
    $current_ratings[] = array(
        'rating' => $rating,
        'user_id' => $user_id,
        'user_ip' => $user_ip,
        'date' => current_time('mysql')
    );

    // Cập nhật meta
    update_post_meta($post_id, '_post_ratings', $current_ratings);

    // Tính toán và cập nhật thống kê
    $stats = calculate_rating_stats($current_ratings);
    update_post_meta($post_id, '_rating_average', $stats['average']);
    update_post_meta($post_id, '_rating_total', $stats['total']);

    wp_send_json_success(array(
        'message' => 'Cảm ơn bạn đã đánh giá!',
        'stats' => $stats
    ));
}

/**
 * Kiểm tra xem user đã đánh giá post này chưa (đơn giản)
 */
function has_user_rated_simple($post_id, $user_ip, $user_id = 0) {
    $ratings = get_post_meta($post_id, '_post_ratings', true);
    
    if (!is_array($ratings)) {
        return false;
    }

    foreach ($ratings as $rating_data) {
        // Kiểm tra theo user_id nếu đã đăng nhập
        if ($user_id > 0 && isset($rating_data['user_id']) && $rating_data['user_id'] == $user_id) {
            return true;
        }
        
        // Kiểm tra theo IP nếu chưa đăng nhập
        if ($user_id == 0 && isset($rating_data['user_ip']) && $rating_data['user_ip'] == $user_ip && $rating_data['user_id'] == 0) {
            return true;
        }
    }

    return false;
}

/**
 * Tính toán thống kê rating
 */
function calculate_rating_stats($ratings) {
    if (empty($ratings)) {
        return array(
            'total' => 0,
            'average' => 0
        );
    }

    $total = count($ratings);
    $sum = 0;

    foreach ($ratings as $rating_data) {
        $sum += $rating_data['rating'];
    }

    $average = $sum / $total;

    return array(
        'total' => $total,
        'average' => round($average, 1)
    );
}

/**
 * Lấy thống kê rating của post
 */
function get_post_rating_stats_simple($post_id) {
    $total = get_post_meta($post_id, '_rating_total', true) ?: 0;
    $average = get_post_meta($post_id, '_rating_average', true) ?: 0;
    
    return array(
        'total' => intval($total),
        'average' => floatval($average)
    );
}

/**
 * Hiển thị rating HTML theo template bạn cung cấp
 */
function display_rating_html($post_id = null, $interactive = true) {
    if (!$post_id) {
        global $post;
        $post_id = $post->ID;
    }
    
    $stats = get_post_rating_stats_simple($post_id);
    $user_ip = $_SERVER['REMOTE_ADDR'];
    $user_id = is_user_logged_in() ? get_current_user_id() : 0;
    $has_rated = has_user_rated_simple($post_id, $user_ip, $user_id);
    
    ob_start();
    ?>
    <div class="rate-page" data-post-id="<?php echo $post_id; ?>" <?php echo $interactive && !$has_rated ? 'data-interactive="true"' : ''; ?>>
        <div class="container">
            <div class="wrapper flex items-center gap-3">
                <div class="stars text-[#FFBF00] flex items-center gap-2">
                    <?php for ($i = 1; $i <= 5; $i++): ?>
                        <i class="fa-solid fa-star star-<?php echo $i; ?> <?php echo $i <= $stats['average'] ? 'active' : ''; ?>" 
                           data-rating="<?php echo $i; ?>"
                           <?php echo $interactive && !$has_rated ? 'style="cursor: pointer;"' : ''; ?>></i>
                    <?php endfor; ?>
                </div>
                <div class="content">
                    <p>
                        <strong class="current"><?php echo $stats['average'] > 0 ? number_format($stats['average'], 1) : '5.0'; ?></strong> 
                        trên <strong class="total">5</strong> với 
                        <strong class="rating-count"><?php echo $stats['total']; ?></strong> xếp hạng
                    </p>
                </div>
                <?php /* if ($has_rated): ?>
                    <div class="rated-message text-green-600 text-sm">
                        <i class="fa-solid fa-check"></i> Đã đánh giá
                    </div>
                <?php endif;*/ ?>
            </div>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * Thêm data cho rating vào script hiện có
 */
function add_rating_data_to_script() {
    wp_localize_script('main-office-script', 'ratingData', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'messages' => array(
            'rating_success' => 'Cảm ơn bạn đã đánh giá!',
            'rating_error' => 'Có lỗi xảy ra. Vui lòng thử lại.',
            'already_rated' => 'Bạn đã đánh giá bài viết này rồi.',
            'rating_too_low' => 'Chúng tôi chỉ chấp nhận đánh giá từ 4 sao trở lên. Cảm ơn bạn đã quan tâm!'
        )
    ));
}
add_action('wp_enqueue_scripts', 'add_rating_data_to_script');

/**
 * Shortcode để hiển thị rating
 */
function rating_shortcode_simple($atts) {
    $atts = shortcode_atts(array(
        'post_id' => get_the_ID(),
        'interactive' => true
    ), $atts);
    
    return display_rating_html($atts['post_id'], $atts['interactive']);
}
add_shortcode('simple_rating', 'rating_shortcode_simple');








/**
 * ===================================================================
 * FUNCTION CẬP NHẬT TỔNG RATING CHO TẤT CẢ POSTS VÀ PAGES
 * ===================================================================
 */

/**
 * ===================================================================
 * FUNCTION CẬP NHẬT TỔNG RATING VÀ TẠO RANDOM RATING CHO PAGES CHƯA CÓ
 * ===================================================================
 */

/**
 * Tạo random rating data cho một post/page
 */
function generate_random_rating_data($post_id) {
    // Random số lượng đánh giá từ 5-20
    $total_ratings = rand(5, 20);
    
    // Random rating trung bình từ 3.5 đến 5.0
    $average_rating = rand(35, 50) / 10; // 3.5 - 5.0
    
    // Tạo dữ liệu rating giả
    $ratings_data = array();
    
    for ($i = 0; $i < $total_ratings; $i++) {
        // Tạo rating xung quanh average (có độ lệch nhỏ)
        $individual_rating = $average_rating + (rand(-5, 5) / 10);
        $individual_rating = max(1, min(5, $individual_rating)); // Giới hạn 1-5
        
        $ratings_data[] = array(
            'rating' => round($individual_rating, 1),
            'user_id' => rand(1000, 9999), // Fake user ID
            'date' => date('Y-m-d H:i:s', strtotime('-' . rand(1, 365) . ' days')), // Random date trong năm qua
            'comment' => '', // Không có comment
        );
    }
    
    return $ratings_data;
}

/**
 * Cập nhật tổng rating cho tất cả các post/page (tạo random cho những page chưa có)
 */
function update_all_posts_rating() {
    global $wpdb;
    
    // Lấy tất cả posts và pages
    $all_posts = $wpdb->get_results(
        "SELECT ID, post_type, post_title
         FROM {$wpdb->posts} 
         WHERE post_status = 'publish'
         AND post_type IN ('post', 'page', 'office-rent-full', 'office-rent-shared')"
    );
    
    $processed = 0;
    $updated = 0;
    $created_random = 0;
    $by_type = array();
    
    foreach ($all_posts as $post_data) {
        $post_id = $post_data->ID;
        $post_type = $post_data->post_type;
        
        // Khởi tạo counter cho post type
        if (!isset($by_type[$post_type])) {
            $by_type[$post_type] = array('total' => 0, 'existing' => 0, 'random_created' => 0);
        }
        $by_type[$post_type]['total']++;
        
        // Lấy rating data hiện tại
        $ratings_data = get_post_meta($post_id, '_post_ratings', true);
        
        if (is_array($ratings_data) && !empty($ratings_data)) {
            // Đã có rating data - tính toán lại thống kê
            $stats = calculate_rating_stats($ratings_data);
            $by_type[$post_type]['existing']++;
            
            error_log(sprintf(
                'Cập nhật rating có sẵn %s ID %d (%s): Average=%.2f, Total=%d',
                $post_type,
                $post_id,
                $post_data->post_title,
                $stats['average'],
                $stats['total']
            ));
        } else {
            // Chưa có rating data - tạo random
            $ratings_data = generate_random_rating_data($post_id);
            $stats = calculate_rating_stats($ratings_data);
            
            // Lưu rating data giả
            update_post_meta($post_id, '_post_ratings', $ratings_data);
            
            $by_type[$post_type]['random_created']++;
            $created_random++;
            
            error_log(sprintf(
                'Tạo random rating %s ID %d (%s): Average=%.2f, Total=%d (Generated %d ratings)',
                $post_type,
                $post_id,
                $post_data->post_title,
                $stats['average'],
                $stats['total'],
                count($ratings_data)
            ));
        }
        
        // Cập nhật meta fields
        update_post_meta($post_id, '_rating_average', $stats['average']);
        update_post_meta($post_id, '_rating_total', $stats['total']);
        
        $processed++;
        $updated++;
    }
    
    return array(
        'processed' => $processed,
        'updated' => $updated,
        'created_random' => $created_random,
        'by_type' => $by_type,
        'message' => "Đã cập nhật {$updated} bài viết/trang (tạo random cho {$created_random} items)"
    );
}

/**
 * Cập nhật rating theo post type (tạo random cho những item chưa có)
 */
function update_ratings_by_post_type($post_type = 'page') {
    global $wpdb;
    
    // Lấy tất cả posts của post type này
    $all_posts = $wpdb->get_results($wpdb->prepare(
        "SELECT ID, post_title
         FROM {$wpdb->posts} 
         WHERE post_status = 'publish'
         AND post_type = %s",
        $post_type
    ));
    
    $processed = 0;
    $updated = 0;
    $existing_rating = 0;
    $random_created = 0;
    
    foreach ($all_posts as $post_data) {
        $post_id = $post_data->ID;
        
        // Lấy rating data hiện tại
        $ratings_data = get_post_meta($post_id, '_post_ratings', true);
        
        if (is_array($ratings_data) && !empty($ratings_data)) {
            // Đã có rating data
            $stats = calculate_rating_stats($ratings_data);
            $existing_rating++;
        } else {
            // Chưa có rating data - tạo random
            $ratings_data = generate_random_rating_data($post_id);
            $stats = calculate_rating_stats($ratings_data);
            
            // Lưu rating data giả
            update_post_meta($post_id, '_post_ratings', $ratings_data);
            $random_created++;
        }
        
        // Cập nhật meta fields
        update_post_meta($post_id, '_rating_average', $stats['average']);
        update_post_meta($post_id, '_rating_total', $stats['total']);
        
        $processed++;
        $updated++;
    }
    
    return array(
        'post_type' => $post_type,
        'processed' => $processed,
        'updated' => $updated,
        'existing_rating' => $existing_rating,
        'random_created' => $random_created,
        'message' => "Đã cập nhật {$updated} {$post_type} (có sẵn: {$existing_rating}, tạo random: {$random_created})"
    );
}

/**
 * Tạo random rating cho một post cụ thể
 */
function create_random_rating_for_post($post_id) {
    $post = get_post($post_id);
    if (!$post) {
        return false;
    }
    
    // Kiểm tra xem đã có rating chưa
    $existing_ratings = get_post_meta($post_id, '_post_ratings', true);
    if (is_array($existing_ratings) && !empty($existing_ratings)) {
        return array('error' => 'Post đã có rating data');
    }
    
    // Tạo random rating
    $ratings_data = generate_random_rating_data($post_id);
    $stats = calculate_rating_stats($ratings_data);
    
    // Lưu dữ liệu
    update_post_meta($post_id, '_post_ratings', $ratings_data);
    update_post_meta($post_id, '_rating_average', $stats['average']);
    update_post_meta($post_id, '_rating_total', $stats['total']);
    
    error_log(sprintf(
        'Tạo random rating cho %s ID %d: Average=%.2f, Total=%d',
        $post->post_type,
        $post_id,
        $stats['average'],
        $stats['total']
    ));
    
    return $stats;
}

/**
 * Lấy thống kê tổng quan
 */
function get_rating_overview_stats() {
    global $wpdb;
    
    // Thống kê tổng số posts/pages
    $total_stats = $wpdb->get_results(
        "SELECT post_type, COUNT(*) as total_count
         FROM {$wpdb->posts} 
         WHERE post_status = 'publish'
         AND post_type IN ('post', 'page', 'office-rent-full', 'office-rent-shared')
         GROUP BY post_type"
    );
    
    // Thống kê có rating data
    $with_rating_stats = $wpdb->get_results(
        "SELECT p.post_type, COUNT(*) as with_rating_count
         FROM {$wpdb->postmeta} pm
         INNER JOIN {$wpdb->posts} p ON pm.post_id = p.ID
         WHERE pm.meta_key = '_post_ratings' 
         AND p.post_status = 'publish'
         AND p.post_type IN ('post', 'page', 'office-rent-full', 'office-rent-shared')
         GROUP BY p.post_type"
    );
    
    // Kết hợp dữ liệu
    $stats = array();
    foreach ($total_stats as $total) {
        $stats[$total->post_type] = array(
            'total' => $total->total_count,
            'with_rating' => 0,
            'without_rating' => $total->total_count
        );
    }
    
    foreach ($with_rating_stats as $with_rating) {
        if (isset($stats[$with_rating->post_type])) {
            $stats[$with_rating->post_type]['with_rating'] = $with_rating->with_rating_count;
            $stats[$with_rating->post_type]['without_rating'] = 
                $stats[$with_rating->post_type]['total'] - $with_rating->with_rating_count;
        }
    }
    
    return $stats;
}

/**
 * AJAX handlers
 */
function update_all_ratings_ajax() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Không đủ quyền');
        return;
    }
    
    $result = update_all_posts_rating();
    wp_send_json_success($result);
}
add_action('wp_ajax_update_all_ratings', 'update_all_ratings_ajax');

function update_ratings_by_type_ajax() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Không đủ quyền');
        return;
    }
    
    $post_type = sanitize_text_field($_POST['post_type'] ?? 'page');
    $result = update_ratings_by_post_type($post_type);
    wp_send_json_success($result);
}
add_action('wp_ajax_update_ratings_by_type', 'update_ratings_by_type_ajax');

/**
 * Menu admin
 */
function add_simple_rating_menu() {
    add_management_page(
        'Cập nhật Rating',
        'Cập nhật Rating', 
        'manage_options',
        'update-ratings',
        'simple_rating_page'
    );
}
add_action('admin_menu', 'add_simple_rating_menu');

/**
 * Trang admin
 */
function simple_rating_page() {
    $stats = get_rating_overview_stats();
    ?>
    <div class="wrap">
        <h1>Cập nhật Rating cho Posts & Pages</h1>
        
        <div class="card">
            <h2>Thống kê tổng quan</h2>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Post Type</th>
                        <th>Tổng số</th>
                        <th>Có rating</th>
                        <th>Chưa có rating</th>
                        <th>Hành động</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($stats as $post_type => $stat): ?>
                    <tr>
                        <td><strong><?php echo esc_html($post_type); ?></strong></td>
                        <td><?php echo esc_html($stat['total']); ?></td>
                        <td style="color: green;"><?php echo esc_html($stat['with_rating']); ?></td>
                        <td style="color: orange;"><?php echo esc_html($stat['without_rating']); ?></td>
                        <td>
                            <button class="button update-by-type-btn" data-type="<?php echo esc_attr($post_type); ?>">
                                Cập nhật <?php echo esc_html($post_type); ?>
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <div class="card">
            <h2>Cập nhật tất cả với Random Rating</h2>
            <div class="rating-info">
                <h3>🎲 Tính năng Random Rating:</h3>
                <ul>
                    <li>✅ <strong>Có rating:</strong> Tính toán lại thống kê từ dữ liệu có sẵn</li>
                    <li>🎲 <strong>Chưa có rating:</strong> Tạo random rating từ 3.5-5.0 sao với 5-20 đánh giá</li>
                    <li>📊 Random rating sẽ có độ phân tán tự nhiên</li>
                    <li>📅 Ngày đánh giá random trong vòng 1 năm qua</li>
                </ul>
            </div>
            
            <button id="update-all-ratings-btn" class="button button-primary">
                🚀 Cập nhật tất cả Rating (có Random)
            </button>
        </div>
        
        <div id="results" style="margin-top: 20px; display: none;"></div>
        
        <script>
        jQuery(document).ready(function($) {
            // Cập nhật tất cả
            $('#update-all-ratings-btn').on('click', function() {
                var $btn = $(this);
                $btn.prop('disabled', true).text('🔄 Đang cập nhật...');
                $('#results').show().html('<div class="notice notice-info"><p>🔄 Đang xử lý tất cả posts/pages và tạo random rating...</p></div>');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: { action: 'update_all_ratings' },
                    success: function(response) {
                        if (response.success) {
                            var message = '<strong>✅ ' + response.data.message + '</strong>';
                            
                            if (response.data.by_type) {
                                message += '<br><br><strong>📊 Chi tiết theo loại:</strong><ul>';
                                $.each(response.data.by_type, function(type, stats) {
                                    message += '<li><strong>' + type + ':</strong> ' + stats.total + ' items ';
                                    message += '(có sẵn: ' + stats.existing + ', 🎲 tạo random: ' + stats.random_created + ')</li>';
                                });
                                message += '</ul>';
                            }
                            
                            if (response.data.created_random > 0) {
                                message += '<br><br><strong>🎉 Đã tạo random rating cho ' + response.data.created_random + ' items!</strong>';
                            }
                            
                            $('#results').html('<div class="notice notice-success"><p>' + message + '</p></div>');
                        } else {
                            $('#results').html('<div class="notice notice-error"><p>❌ Có lỗi xảy ra: ' + (response.data || 'Unknown error') + '</p></div>');
                        }
                    },
                    error: function() {
                        $('#results').html('<div class="notice notice-error"><p>❌ Lỗi kết nối</p></div>');
                    },
                    complete: function() {
                        $btn.prop('disabled', false).text('🚀 Cập nhật tất cả Rating (có Random)');
                        // Reload page để cập nhật thống kê
                        setTimeout(function() {
                            location.reload();
                        }, 3000);
                    }
                });
            });
            
            // Cập nhật theo post type
            $('.update-by-type-btn').on('click', function() {
                var $btn = $(this);
                var postType = $btn.data('type');
                
                $btn.prop('disabled', true).text('🔄 Đang cập nhật...');
                $('#results').show().html('<div class="notice notice-info"><p>🔄 Đang xử lý ' + postType + ' và tạo random rating...</p></div>');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: { 
                        action: 'update_ratings_by_type',
                        post_type: postType
                    },
                    success: function(response) {
                        if (response.success) {
                            var message = '<strong>✅ ' + response.data.message + '</strong>';
                            if (response.data.random_created > 0) {
                                message += '<br><strong>🎲 Đã tạo random rating cho ' + response.data.random_created + ' ' + postType + '!</strong>';
                            }
                            $('#results').html('<div class="notice notice-success"><p>' + message + '</p></div>');
                        } else {
                            $('#results').html('<div class="notice notice-error"><p>❌ Có lỗi xảy ra</p></div>');
                        }
                    },
                    complete: function() {
                        $btn.prop('disabled', false).text('Cập nhật ' + postType);
                        // Reload page để cập nhật thống kê
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    }
                });
            });
        });
        </script>
        
        <style>
        .card { 
            background: #fff; 
            border: 1px solid #ccd0d4; 
            box-shadow: 0 1px 1px rgba(0,0,0,.04); 
            padding: 20px; 
            margin: 20px 0; 
        }
        .card h2 { 
            margin-top: 0; 
        }
        .rating-info {
            background: #f0f8ff;
            border: 1px solid #0073aa;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .rating-info h3 {
            margin-top: 0;
            color: #0073aa;
        }
        .rating-info ul {
            margin: 10px 0;
        }
        .rating-info ul li {
            margin: 8px 0;
            font-size: 14px;
        }
        </style>
    </div>
    <?php
}