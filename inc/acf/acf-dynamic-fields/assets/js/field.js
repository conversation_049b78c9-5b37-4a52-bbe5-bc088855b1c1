/**
 * JavaScript cho field type dynamic_fields
 */
(function($) {
    function initialize_field($field) {
        // Khởi tạo Select2 cho các select box
        $field.find('select').each(function(){
            var $select = $(this);
            var options = {
                width: '100%',
                placeholder: $select.find('option:first').text(),
                allowClear: true
            };
            
            // Thêm tùy chọn đa lựa chọn nếu select có thuộc tính multiple
            if ($select.attr('multiple')) {
                options.multiple = true;
                options.closeOnSelect = false;
            }
            
            // Khởi tạo Select2
            $select.select2(options);
        });
    }

    if (typeof acf.add_action !== 'undefined') {
        // Chạy initialize_field khi field được tải
        acf.add_action('ready_field/type=dynamic_fields', initialize_field);
        acf.add_action('append_field/type=dynamic_fields', initialize_field);
    }
})(jQuery);