/**
 * CSS cho field type dynamic_fields
 */
 .acf-dynamic-fields-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* <PERSON>a thành 2 cột */
    gap: 15px;
    width: 100%;
}

.acf-dynamic-field-row {
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.acf-dynamic-field-row label {
    font-weight: bold;
    margin-bottom: 5px;
}

.acf-dynamic-field-row input,
.acf-dynamic-field-row select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.acf-dynamic-select {
    height: 38px;
}

.acf-dynamic-fields-select {
    width: 100%;
}

.acf-dynamic-fields-select .select2-container {
    width: 100% !important;
}

.acf-dynamic-fields-select .select2-selection--multiple {
    min-height: 80px;
}

/* Responsive: khi màn hình nhỏ hơn 768px, chuyển về 1 cột */
@media screen and (max-width: 768px) {
    .acf-dynamic-fields-container {
        grid-template-columns: 1fr;
    }
}