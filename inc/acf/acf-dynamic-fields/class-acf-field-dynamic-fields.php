<?php
/**
 * Field type mới lấy dữ liệu từ options page
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class acf_field_dynamic_fields
 */
class acf_field_dynamic_fields extends acf_field {
    /**
     * Hiển thị trong REST API
     *
     * @var bool
     */
    public $show_in_rest = true;

    /**
     * Thông tin về môi trường
     *
     * @var array
     */
    private $env;
    
    /**
     * Prefixes for field names
     * 
     * @var string
     */
    private $field_prefix = 'dynamic_field_';

    /**
     * Constructor.
     */
    public function __construct() {
        // Tên field type (không dấu cách, có thể dùng gạch dưới)
        $this->name = 'dynamic_fields';

        // Label hiển thị trong admin
        $this->label = __('Trường dữ liệu động', 'canhcamtheme');

        // Nhóm field
        $this->category = 'basic';

        // Mô tả field
        $this->description = __('Tạo trường dữ liệu dựa trên options page', 'canhcamtheme');

        // Giá trị mặc định
        $this->defaults = array(
            'data_source' => 'thong_so_toa_nha',
            'return_format' => 'value',
            'allow_multiple' => 0,
        );

        // Đường dẫn
        $this->env = array(
            'url' => site_url(str_replace(ABSPATH, '', __DIR__)),
            'version' => '1.0',
        );

        parent::__construct();
        
        // Xử lý lưu các trường dữ liệu cá nhân
        add_action('acf/save_post', array($this, 'save_individual_fields'), 20);
    }

    /**
     * Cài đặt hiển thị khi cấu hình field
     *
     * @param array $field
     * @return void
     */
    public function render_field_settings($field) {
        // Nguồn dữ liệu từ options page
        acf_render_field_setting(
            $field,
            array(
                'label' => __('Nguồn dữ liệu từ Options', 'canhcamtheme'),
                'instructions' => __('Chọn nguồn dữ liệu từ options page', 'canhcamtheme'),
                'type' => 'select',
                'name' => 'data_source',
                'choices' => array(
                    'thong_so_toa_nha' => __('Thông số tòa nhà', 'canhcamtheme'),
                    'thong_tin_dien_tich_va_gia_thue' => __('Thông tin diện tích và giá thuê', 'canhcamtheme'),
                    'tien_ich_toa_nha' => __('Tiện ích tòa nhà', 'canhcamtheme'),
                    'ts_toa_nha_tron_goi' => __('Thông số tòa nhà trọn gói', 'canhcamtheme'),
                    'dich_vu_tron_goi' => __('Dịch vụ trọn gói', 'canhcamtheme'),
                    'tien_ich_tron_goi' => __('Tiện ích trọn gói', 'canhcamtheme'),
                ),
            )
        );
        
        // Định dạng trả về
        acf_render_field_setting(
            $field,
            array(
                'label' => __('Định dạng trả về', 'canhcamtheme'),
                'instructions' => __('Chọn định dạng dữ liệu trả về khi lấy giá trị field', 'canhcamtheme'),
                'type' => 'radio',
                'name' => 'return_format',
                'layout' => 'horizontal',
                'choices' => array(
                    'value' => __('Giá trị', 'canhcamtheme'),
                    'label' => __('Label', 'canhcamtheme'),
                    'array' => __('Cả hai (Array)', 'canhcamtheme'),
                ),
            )
        );
        
        // Cho phép chọn nhiều giá trị
        acf_render_field_setting(
            $field,
            array(
                'label' => __('Cho phép chọn nhiều', 'canhcamtheme'),
                'instructions' => __('Cho phép người dùng chọn nhiều giá trị cho các trường select (áp dụng cho các trường hỗ trợ, như Hướng tòa nhà)', 'canhcamtheme'),
                'type' => 'true_false',
                'name' => 'allow_multiple',
                'ui' => 1,
            )
        );
    }

    /**
     * Hiển thị HTML khi sửa field trên trang edit
     *
     * @param array $field
     * @return void
     */
    public function render_field($field) {
        // Lưu thông tin về field này
        echo '<input type="hidden" name="acf_dynamic_field_info" value="' . esc_attr($field['key']) . '">';
        
        $this->render_option_based_field($field);
    }
    
    /**
     * Hiển thị field dựa trên options page
     *
     * @param array $field
     * @return void
     */
    private function render_option_based_field($field) {
        // Lấy nguồn dữ liệu từ cài đặt
        $data_source = $field['data_source'];
        
        // Lấy dữ liệu từ options page
        $options_data = get_field($data_source, 'option');
        
        // Nếu không có dữ liệu, hiển thị thông báo
        if (!$options_data || !is_array($options_data)) {
            echo '<p>' . __('Không tìm thấy dữ liệu từ nguồn đã chọn.', 'canhcamtheme') . '</p>';
            return;
        }
        
        // Thiết lập cho phép chọn nhiều (chỉ áp dụng cho Hướng tòa nhà)
        $allow_multiple = isset($field['allow_multiple']) ? $field['allow_multiple'] : 0;
        
        // Container cho các input
        echo '<div class="acf-dynamic-fields-container">';
        
        // Lấy post_id hiện tại
        $post_id = isset($GLOBALS['post']) ? $GLOBALS['post']->ID : 0;

        $meta_keys = array();
        foreach ($options_data as $index => $item) {
            $item_id = sanitize_title($item['ten_thong_so']);
            $item_key = $item['item_key'];
            $item_label = $item['ten_thong_so'];
            
            // Tạo khóa meta bao gồm cả nguồn dữ liệu để tránh trùng lặp
            $meta_keys[] = $this->field_prefix . $data_source . '_' . $item_key;
        }

        if(!empty($meta_keys)) {
            echo '<pre style="display: none;">';
            print_r($meta_keys);
            echo '</pre>';
        }

        // Tạo input dựa trên dữ liệu từ options
        foreach ($options_data as $index => $item) {
            $item_id = sanitize_title($item['ten_thong_so']);
            $item_key = $item['item_key'];
            $item_label = $item['ten_thong_so'];
            
            // Tạo khóa meta bao gồm cả nguồn dữ liệu để tránh trùng lặp
            $meta_key = $this->field_prefix . $data_source . '_' . $item_key;
            
            // Lấy giá trị hiện tại của field này
            $item_value = get_post_meta($post_id, $meta_key, true);
            
            echo '<div class="acf-dynamic-field-row">';
            echo '<label>' . esc_html($item_label) . '</label>';
            
            // Kiểm tra nếu là Hạng tòa nhà hoặc Hướng tòa nhà
            if ($item_key === 'hang-toa-nha') {
                // Lấy tất cả post từ post type building-class
                $building_classes = get_posts(array(
                    'post_type' => 'building-class',
                    'numberposts' => -1,
                    'post_status' => 'publish',
                ));
                
                // Xử lý giá trị (Hạng tòa nhà luôn chỉ chọn một)
                echo '<select name="' . esc_attr($meta_key) . '" class="acf-dynamic-select">';
                echo '<option value="">' . __('Chọn Hạng tòa nhà', 'canhcamtheme') . '</option>';
                
                foreach ($building_classes as $class) {
                    $selected = ($item_value == $class->ID) ? ' selected="selected"' : '';
                    echo '<option value="' . esc_attr($class->ID) . '"' . $selected . '>' . esc_html($class->post_title) . '</option>';
                }
                
                echo '</select>';
            }
            elseif ($item_key === 'huong-toa-nha') {
                // Lấy tất cả post từ post type building-orientation
                $orientations = get_posts(array(
                    'post_type' => 'building-orientation',
                    'numberposts' => -1,
                    'post_status' => 'publish',
                ));
                
                // Xử lý giá trị (Hướng tòa nhà luôn cho phép chọn nhiều)
                $selected_values = array();
                
                // Chuyển đổi giá trị thành mảng
                if (!empty($item_value)) {
                    $selected_values = maybe_unserialize($item_value);
                    if (!is_array($selected_values)) {
                        $selected_values = array($selected_values);
                    }
                }
                
                echo '<select name="' . esc_attr($meta_key) . '[]" class="acf-dynamic-select" multiple="multiple">';
                
                foreach ($orientations as $orientation) {
                    $selected = in_array($orientation->ID, $selected_values) ? ' selected="selected"' : '';
                    echo '<option value="' . esc_attr($orientation->ID) . '"' . $selected . '>' . esc_html($orientation->post_title) . '</option>';
                }
                
                echo '</select>';
            }
            elseif ($item_key === 'ten-toa-nha') {
                // Hiển thị 2 trường cho tên tòa nhà
                echo '<div class="building-name-fields">';
                // Trường tên tòa nhà
                echo '<div class="building-name-field" style="margin-bottom: 10px;">';
                echo '<input type="text" name="' . esc_attr($meta_key) . '" value="' . esc_attr($item_value) . '" placeholder="Tên tòa nhà" />';
                echo '</div>';
                
                // Trường URL tòa nhà
                $url_meta_key = $this->field_prefix . $data_source . '_url-toa-nha';
                $url_value = get_post_meta($post_id, $url_meta_key, true);
                echo '<div class="building-url-field">';
                echo '<input type="url" name="' . esc_attr($url_meta_key) . '" value="' . esc_attr($url_value) . '" placeholder="URL tòa nhà" />';
                echo '</div>';
                echo '</div>';
            }
            else {
                // Input text thông thường cho các trường khác
                echo '<input type="text" name="' . esc_attr($meta_key) . '" value="' . esc_attr($item_value) . '" />';
            }
            
            echo '</div>';
        }
        
        echo '</div>';
    }

    /**
     * Tải CSS và JS cho field type
     *
     * @return void
     */
    public function input_admin_enqueue_scripts() {
        $url = trailingslashit($this->env['url']);
        $version = $this->env['version'];

        // CSS
        wp_register_style(
            'acf-dynamic-fields',
            "{$url}assets/css/field.css",
            array('acf-input'),
            $version
        );
        wp_enqueue_style('acf-dynamic-fields');

        // JavaScript
        wp_register_script(
            'acf-dynamic-fields',
            "{$url}assets/js/field.js",
            array('acf-input', 'jquery'),
            $version
        );
        wp_enqueue_script('acf-dynamic-fields');
        
        // Load select2 nếu chưa được load
        if (!wp_script_is('select2', 'enqueued')) {
            wp_enqueue_script('select2', 'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js', array('jquery'), '4.0.13', true);
            wp_enqueue_style('select2', 'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css', array(), '4.0.13');
        }
    }

    /**
     * Xử lý lưu giá trị khi cập nhật post
     *
     * @param mixed $value
     * @param int $post_id
     * @param array $field
     * @return mixed
     */
    public function update_value($value, $post_id, $field) {
        // Không cần làm gì trong phương thức này vì chúng ta sẽ lưu trực tiếp trong save_individual_fields()
        return null;
    }

    /**
     * Xác thực giá trị
     *
     * @param mixed $valid
     * @param mixed $value
     * @param array $field
     * @param string $input
     * @return mixed
     */
    public function validate_value($valid, $value, $field, $input) {
        // Chấp nhận mọi giá trị
        return $valid;
    }

    /**
     * Định dạng giá trị để sử dụng trong template
     *
     * @param mixed $value
     * @param int|string $post_id
     * @param array $field
     * @return mixed
     */
    public function format_value($value, $post_id, $field) {
        // Custom implementation để lấy tất cả field tương ứng
        $result = array();
        
        // Lấy dữ liệu options
        $options_data = get_field($field['data_source'], 'option');
        
        if (is_array($options_data)) {
            foreach ($options_data as $item) {
                $item_id = sanitize_title($item['ten_thong_so']);
                $item_key = $item['item_key'];
                
                // Sử dụng cùng định dạng meta_key để lấy dữ liệu
                $meta_key = $this->field_prefix . $field['data_source'] . '_' . $item_key;
                $field_value = get_post_meta($post_id, $meta_key, true);
                
                // Xử lý đặc biệt cho các trường chọn post
                if ($item_key === 'hang-toa-nha') {
                    // Hạng tòa nhà luôn trả về một giá trị
                    if (!empty($field_value) && is_numeric($field_value)) {
                        $post = get_post($field_value);
                        
                        if ($post) {
                            if ($field['return_format'] === 'array') {
                                $result[$item_key] = array(
                                    'id' => $post->ID,
                                    'title' => $post->post_title,
                                    'slug' => $post->post_name,
                                    'permalink' => get_permalink($post->ID),
                                );
                            } elseif ($field['return_format'] === 'label') {
                                $result[$item_key] = $post->post_title;
                            } else {
                                $result[$item_key] = $field_value;
                            }
                        } else {
                            $result[$item_key] = $field_value;
                        }
                    } else {
                        $result[$item_key] = $field_value;
                    }
                }
                elseif ($item_key === 'huong-toa-nha') {
                    // Hướng tòa nhà luôn trả về mảng
                    $field_value = maybe_unserialize($field_value);
                    if (!is_array($field_value)) {
                        $field_value = array($field_value);
                    }
                    
                    // Xử lý các giá trị trong mảng
                    if (!empty($field_value)) {
                        $processed_values = array();
                        
                        foreach ($field_value as $post_id_value) {
                            if (!empty($post_id_value) && is_numeric($post_id_value)) {
                                $post = get_post($post_id_value);
                                
                                if ($post) {
                                    if ($field['return_format'] === 'array') {
                                        $processed_values[] = array(
                                            'id' => $post->ID,
                                            'title' => $post->post_title,
                                            'slug' => $post->post_name,
                                            'permalink' => get_permalink($post->ID),
                                        );
                                    } elseif ($field['return_format'] === 'label') {
                                        $processed_values[] = $post->post_title;
                                    } else {
                                        $processed_values[] = $post_id_value;
                                    }
                                }
                            }
                        }
                        
                        $result[$item_key] = $processed_values;
                    } else {
                        $result[$item_key] = array();
                    }
                }
                elseif ($item_key === 'ten-toa-nha') {
                    // Xử lý cả tên và URL tòa nhà
                    $url_meta_key = $this->field_prefix . $field['data_source'] . '_url-toa-nha';
                    $url_value = get_post_meta($post_id, $url_meta_key, true);
                    
                    $result[$item_key] = $field_value;
                    $result['url-toa-nha'] = $url_value;
                }
                else {
                    $result[$item_key] = $field_value;
                }
            }
        }
        
        return $result;
    }
    
    /**
     * Lưu các trường dữ liệu cá nhân
     * 
     * @param int $post_id
     * @return void
     */
    public function save_individual_fields($post_id) {
        // Kiểm tra nếu có request POST
        if (!isset($_POST) || empty($_POST)) {
            return;
        }
        
        // Tìm tất cả các field có prefix
        foreach ($_POST as $key => $value) {
            if (strpos($key, $this->field_prefix) === 0) {
                // Xử lý mảng giá trị (từ select multiple)
                if (is_array($value)) {
                    // Lưu dưới dạng serialized array
                    update_post_meta($post_id, $key, $value);
                } else {
                    update_post_meta($post_id, $key, $value);
                }
            }
        }
    }
}

// Không cần đăng ký constructor mở rộng nữa vì đã được thêm vào __construct