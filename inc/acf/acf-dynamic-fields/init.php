<?php
/**
 * <PERSON><PERSON>ng ký ACF field type mới
 */

if (!defined('ABSPATH')) {
    exit;
}

add_action('init', 'include_acf_field_dynamic_fields');
/**
 * Đăng ký field type mới
 */
function include_acf_field_dynamic_fields() {
    if (!function_exists('acf_register_field_type')) {
        return;
    }

    require_once __DIR__ . '/class-acf-field-dynamic-fields.php';

    acf_register_field_type('acf_field_dynamic_fields');
}