<?xml version="1.0"?>
<psalm
    errorLevel="1"
    resolveFromConfigFile="true"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="https://getpsalm.org/schema/config"
    xsi:schemaLocation="https://getpsalm.org/schema/config vendor/vimeo/psalm/config.xsd"
    findUnusedBaselineEntry="true"
    findUnusedCode="true"
    phpVersion="8.2.0"
>
    <!-- TODO: Update phpVersion when raising the minimum supported version -->
    <projectFiles>
        <directory name="src" />
        <ignoreFiles>
            <directory name="vendor" />
        </ignoreFiles>
    </projectFiles>
    <issueHandlers>
        <!-- Turn off dead code warnings for externally called functions -->
        <PossiblyUnusedProperty errorLevel="suppress" />
        <PossiblyUnusedMethod errorLevel="suppress" />
        <PossiblyUnusedReturnValue errorLevel="suppress" />
    </issueHandlers>
</psalm>
