on:
  pull_request:
    branches:
      - "*"
  workflow_dispatch: {}

name: "Pull Request"

permissions:
  contents: read

jobs:
  test:
    name: "Test"

    permissions:
      contents: read
      security-events: write

    uses: ./.github/workflows/part_test.yml

  docs:
    name: "Doc<PERSON>"

    uses: ./.github/workflows/part_docs.yml

  dependabot:
    name: "Dependabot"

    if: ${{ github.actor == 'dependabot[bot]'}}

    permissions:
      pull-requests: write
      contents: write

    uses: ./.github/workflows/part_dependabot.yml

  dependency-review:
    name: Dependency Review
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@cb605e52c26070c328afc4562f0b4ada7618a84e # v2.10.4
        with:
          egress-policy: audit

      - name: 'Checkout Repository'
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: 'Dependency Review'
        uses: actions/dependency-review-action@3b139cfc5fae8b618d3eae3675e383bb1769c019 # v4.5.0
