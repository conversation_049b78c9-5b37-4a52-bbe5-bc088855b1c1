<?php

function get_district_by_city_id()
{
    $city_id = isset($_POST['city_id']) ? $_POST['city_id'] : '';
    $args = array(
        'post_type' => 'district',
        'posts_per_page' => -1,
    );
    $query = new WP_Query($args);
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            $city_id_post = get_field('city_id', get_the_ID());
            if ($city_id == $city_id_post) {
                echo '<div class="checkbox-item radio district-item" data-value="' . get_the_ID() . '">' . get_the_title() . '</div>';
            }
        }
    }
    wp_reset_postdata();
    die();
}
add_action('wp_ajax_get_district_by_city_id', 'get_district_by_city_id');
add_action('wp_ajax_nopriv_get_district_by_city_id', 'get_district_by_city_id');


function get_address_by_district_id()
{
    $district_id = isset($_POST['district_id']) ? $_POST['district_id'] : '';

    $args_ward = array(
        'post_type' => 'ward',
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => 'district_id',
                'value' => $district_id,
                'compare' => '='
            )
        )
    );
    $query_ward = new WP_Query($args_ward);


    $args_address = array(
        'post_type' => 'street',
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => 'district_id',
                'value' => $district_id,
                'compare' => '='
            )
        )
    );
    $query_address = new WP_Query($args_address);


    $args_block = array(
        'post_type' => 'block',
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => 'district_id',
                'value' => $district_id,
                'compare' => '='
            )
        )
    );
    $query_block = new WP_Query($args_block);
    $wards = '';
    $streets = '';
    $blocks = '';
    if ($query_ward->have_posts()) {
        while ($query_ward->have_posts()) {
            $query_ward->the_post();
            $wards .= '<div class="checkbox-item radio ward-item" data-value="' . get_the_ID() . '">' . get_the_title() . '</div>';
        }
        wp_reset_postdata();
    }

    if ($query_address->have_posts()) {
        while ($query_address->have_posts()) {
            $query_address->the_post();
            $streets .= '<div class="checkbox-item radio street-item" data-value="' . get_the_ID() . '">' . get_the_title() . '</div>';
        }
        wp_reset_postdata();
    }

    if ($query_block->have_posts()) {
        while ($query_block->have_posts()) {
            $query_block->the_post();
            $blocks .= '<div class="checkbox-item radio block-item" data-value="' . get_the_ID() . '">' . get_the_title() . '</div>';
        }
    }
    wp_reset_postdata();

    echo json_encode(array(
        'ward' => $wards,
        'street' => $streets,
        'block' => $blocks
    ));
    die();
}
add_action('wp_ajax_get_address_by_district_id', 'get_address_by_district_id');
add_action('wp_ajax_nopriv_get_address_by_district_id', 'get_address_by_district_id');
