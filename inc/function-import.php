<?php
// Thêm vào file hiện tại

/**
 * Excel import functionality
 */

/**
 * <PERSON><PERSON><PERSON> thư mục để lưu file Excel
 */
function excel_upload_dir()
{
    $upload_dir = wp_upload_dir();
    $excel_dir = $upload_dir['basedir'] . '/excel-imports';

    // Kiểm tra và tạo thư mục nếu chưa tồn tại
    if (!file_exists($excel_dir)) {
        if (!wp_mkdir_p($excel_dir)) {
            // Nếu không thể tạo thư mục, sử dụng thư mục upload mặc định
            error_log('Không thể tạo thư mục excel-imports, sử dụng thư mục upload mặc định');
            return $upload_dir['basedir'];
        }
    }

    // Kiểm tra quyền ghi
    if (!is_writable($excel_dir)) {
        chmod($excel_dir, 0755);
        if (!is_writable($excel_dir)) {
            error_log('Không có quyền ghi vào thư mục excel-imports, sử dụng thư mục upload mặc định');
            return $upload_dir['basedir'];
        }
    }

    return $excel_dir;
}

/**
 * Thêm menu quản lý import Excel
 */
function add_excel_import_page()
{
    add_menu_page(
        'Import Excel Data',
        'Excel Import',
        'manage_options',
        'excel-import',
        'render_excel_import_page',
        'dashicons-media-spreadsheet',
        30
    );
}
add_action('admin_menu', 'add_excel_import_page');

/**
 * Hiển thị giao diện import Excel
 */
function render_excel_import_page()
{
    // Xử lý xóa file nếu có
    if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['file'])) {
        handle_excel_file_delete($_GET['file']);
    }

    // Xử lý upload file nếu có
    if (isset($_POST['excel_import_submit']) && isset($_FILES['excel_file'])) {
        handle_excel_upload();
    }

    // Lấy danh sách file đã upload
    $uploaded_files = get_excel_files_list();

    // Hiển thị form import
?>
    <div class="wrap">
        <h1>Import Excel Data</h1>

        <div class="card">
            <h2>Upload Excel File</h2>
            <form method="post" enctype="multipart/form-data">
                <table class="form-table">
                    <tr>
                        <th><label for="excel_file">Chọn file Excel</label></th>
                        <td>
                            <input type="file" name="excel_file" id="excel_file" accept=".xlsx, .xls">
                            <p class="description">Định dạng: .xlsx, .xls</p>
                        </td>
                    </tr>
                </table>

                <?php wp_nonce_field('excel_import_action', 'excel_import_nonce'); ?>
                <input type="submit" name="excel_import_submit" class="button button-primary" value="Upload và Xử lý">
            </form>
        </div>

        <div class="card" style="margin-top: 20px;">
            <h2>Export Dữ Liệu</h2>
            <p>Export dữ liệu từ bài viết ra file Excel</p>
            <form method="post" action="<?php echo admin_url('admin-post.php'); ?>">
                <input type="hidden" name="action" value="export_office_data">
                <?php wp_nonce_field('export_office_data_nonce', 'export_nonce'); ?>

                <p>
                    <label for="export_limit">Số lượng bài viết:</label>
                    <input type="number" id="export_limit" name="export_limit" value="100" min="1" max="1000" step="1">
                </p>

                <p>
                    <button type="submit" class="button button-primary">Export Dữ Liệu</button>
                </p>
            </form>
        </div>

        <!-- <div class="card" style="margin-top: 20px;">
            <h2>Export Dữ Liệu trọn gói</h2>
            <p>Export dữ liệu từ bài viết ra file Excel</p>
            <form method="post" action="<?php echo admin_url('admin-post.php'); ?>">
                <input type="hidden" name="action" value="export_office_data_full">
                <?php wp_nonce_field('export_office_data_full_nonce', 'export_full_nonce'); ?>

                <p>
                    <label for="export_limit">Số lượng bài viết:</label>
                    <input type="number" id="export_limit" name="export_limit" value="100" min="1" max="1000" step="1">
                </p>

                <p>
                    <button type="submit" class="button button-primary">Export Dữ Liệu</button>
                </p>
            </form>
        </div> -->

        <?php if (!empty($uploaded_files)) : ?>
            <div class="card-full" style="margin-top: 20px;">
                <h2>File đã upload</h2>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>Tên file</th>
                            <th>Ngày upload</th>
                            <th>Số dòng dữ liệu</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($uploaded_files as $file) : ?>
                            <tr>
                                <td><?php echo esc_html($file['name']); ?></td>
                                <td><?php echo esc_html($file['date']); ?></td>
                                <td><?php echo get_excel_total_rows($file['full_path']); ?></td>
                                <td>
                                    <?php
                                    $import_status = get_option('excel_import_status_' . md5($file['full_path']), 'Chưa import');
                                    echo '<span class="status-' . sanitize_html_class($import_status) . '">' . esc_html($import_status) . '</span>';
                                    ?>
                                </td>
                                <td>
                                    <a href="<?php echo admin_url('admin.php?page=excel-import&action=view&file=' . urlencode($file['full_path'])); ?>" class="button button-small">Xem dữ liệu</a>
                                    <a href="<?php echo admin_url('admin.php?page=excel-import&action=delete&file=' . urlencode($file['full_path'])); ?>"
                                       class="button button-small button-link-delete"
                                       onclick="return confirm('Bạn có chắc chắn muốn xóa file này?');">Xóa</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <?php
        // Hiển thị preview nếu được yêu cầu
        if (isset($_GET['action']) && $_GET['action'] == 'view' && isset($_GET['file'])) {
            $file_path = sanitize_text_field(urldecode($_GET['file']));

            // Đọc dữ liệu từ file và hiển thị trực tiếp, không cần JavaScript
            $excel_data = read_excel_file_paged($file_path, 0, 2); // Đọc 2 dòng đầu tiên

            if ($excel_data && !isset($excel_data['error'])) {
        ?>
                <div class="card-full" style="margin-top: 20px;">
                    <h2>Xem trước dữ liệu Excel</h2>

                    <div style="margin-bottom: 15px;">
                        <p>Tổng số dòng dữ liệu: <strong><?php echo $excel_data['total_rows']; ?></strong></p>
                        <p>Hiển thị dòng thứ hai</p>
                    </div>

                    <div>
                        <ul class="excel-data-list">
                            <?php
                            // Bỏ qua dòng tiêu đề, lấy dòng thứ hai dữ liệu
                            $start_index = 2;
                            if (isset($excel_data['data'][$start_index])) {
                                $row = $excel_data['data'][$start_index];
                                // Hiển thị từng cặp tiêu đề/nội dung
                                for ($i = 0; $i < count($row); $i++) {
                                    $header = $excel_data['headers'][$i] ?: 'Không có tiêu đề';
                                    $cell = is_null($row[$i]) ? '' : $row[$i];
                            ?>
                                    <li>
                                        <strong><?php echo esc_html($header); ?>:</strong>
                                        <span><?php echo esc_html($cell); ?></span>
                                    </li>
                            <?php
                                }
                            } else {
                                echo '<li>Không có dữ liệu hoặc chỉ có một dòng dữ liệu</li>';
                            }
                            ?>
                        </ul>
                    </div>
                </div>

                <style>
                    .excel-data-list {
                        list-style: none;
                        padding: 0;
                        margin: 0;
                    }

                    .excel-data-list li {
                        display: flex;
                        padding: 8px 0;
                        border-bottom: 1px solid #eee;
                    }

                    .excel-data-list li strong {
                        width: 200px;
                        margin-right: 15px;
                    }

                    .excel-data-list li span {
                        flex: 1;
                    }
                </style>
            <?php
            } else {
            ?>
                <div class="notice notice-error inline">
                    <p>Không thể đọc dữ liệu từ file Excel. <?php echo isset($excel_data['message']) ? esc_html($excel_data['message']) : ''; ?></p>
                </div>
        <?php
            }
        }
        ?>
        <!-- Phần import tự động - Chỉ có HTML, script được thêm qua extension -->
        <div id="global-import-results" class="card-full" style="margin-top: 20px; display: none;">
            <h2>Quá trình Import (tự động)</h2>
            <div class="results-content"></div>
            <div class="import-log" style="max-height:300px;overflow-y:auto;margin-top:15px;border:1px solid #eee;padding:10px;"></div>
        </div>
    </div>
    <?php
}

/**
 * Xử lý upload file Excel
 */
function handle_excel_upload()
{
    // Kiểm tra nonce
    if (!isset($_POST['excel_import_nonce']) || !wp_verify_nonce($_POST['excel_import_nonce'], 'excel_import_action')) {
        wp_die('Lỗi bảo mật. Vui lòng thử lại.');
    }

    // Kiểm tra file
    if (!isset($_FILES['excel_file']) || $_FILES['excel_file']['error'] !== UPLOAD_ERR_OK) {
        add_settings_error('excel_import', 'file_upload', 'Upload thất bại. Lỗi: ' . $_FILES['excel_file']['error'], 'error');
        return;
    }

    // Chuẩn bị để upload file
    require_once(ABSPATH . 'wp-admin/includes/file.php');
    require_once(ABSPATH . 'wp-admin/includes/media.php');
    require_once(ABSPATH . 'wp-admin/includes/image.php');

    // Thêm filter để chuyển đổi thư mục upload
    add_filter('upload_dir', 'excel_upload_dir_filter');

    // Sử dụng wp_handle_upload để upload file
    $upload = wp_handle_upload($_FILES['excel_file'], array('test_form' => false));

    // Loại bỏ filter sau khi upload xong
    remove_filter('upload_dir', 'excel_upload_dir_filter');

    if (isset($upload['error'])) {
        // Nếu có lỗi
        add_settings_error('excel_import', 'file_upload', 'Lỗi upload: ' . $upload['error'], 'error');
        error_log('Excel import upload error: ' . $upload['error']);
        return;
    }

    // Nếu upload thành công, $upload['file'] sẽ chứa đường dẫn đầy đủ đến file
    $file_path = $upload['file'];

    // Kiểm tra đọc file để xác nhận tính hợp lệ
    try {
        // Sử dụng trực tiếp thư viện PhpSpreadsheet đã cài đặt
        require_once(get_template_directory() . '/inc/lib/vendor/autoload.php');
        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($file_path);
        $spreadsheet = $reader->load($file_path);

        // Lấy số dòng dữ liệu
        $worksheet = $spreadsheet->getActiveSheet();
        $rowCount = $worksheet->getHighestRow() - 1; // Trừ đi dòng tiêu đề

        // Lưu thông tin file vào bảng options để quản lý
        $uploaded_files = get_option('excel_imported_files', array());
        $file_info = array(
            'name' => basename($file_path),
            'date' => current_time('mysql'),
            'full_path' => $file_path
        );
        $uploaded_files[] = $file_info;
        update_option('excel_imported_files', $uploaded_files);

        add_settings_error('excel_import', 'file_upload', 'File Excel đã upload thành công. Tổng số dòng dữ liệu: ' . $rowCount, 'success');
    } catch (Exception $e) {
        // Xóa file nếu có lỗi
        @unlink($file_path);
        add_settings_error('excel_import', 'file_upload', 'Lỗi xử lý file Excel: ' . $e->getMessage(), 'error');
        error_log('Excel import processing error: ' . $e->getMessage());
    }
}

/**
 * Filter để chuyển hướng đường dẫn upload
 */
function excel_upload_dir_filter($upload)
{
    // Tạo thư mục excel-imports nếu chưa tồn tại
    $excel_dir = $upload['basedir'] . '/excel-imports';

    if (!file_exists($excel_dir)) {
        wp_mkdir_p($excel_dir);
    }

    // Cập nhật đường dẫn lưu file
    $upload['path'] = $excel_dir;
    $upload['url'] = $upload['baseurl'] . '/excel-imports';

    return $upload;
}

/**
 * Xử lý xóa file Excel đã upload
 */
function handle_excel_file_delete($file_path)
{
    $file_path = urldecode($file_path);
    $uploaded_files = get_option('excel_imported_files', array());

    // Tìm và xóa file khỏi danh sách
    $updated_files = array();
    $file_deleted = false;

    foreach ($uploaded_files as $file) {
        if ($file['full_path'] !== $file_path) {
            $updated_files[] = $file;
        } else {
            // Xóa file vật lý
            if (file_exists($file_path)) {
                @unlink($file_path);
            }
            $file_deleted = true;
        }
    }

    if ($file_deleted) {
        update_option('excel_imported_files', $updated_files);
        add_settings_error('excel_import', 'file_delete', 'File đã được xóa thành công.', 'success');
    } else {
        add_settings_error('excel_import', 'file_delete', 'Không tìm thấy file để xóa.', 'error');
    }
}

/**
 * Lấy danh sách file Excel đã upload
 */
function get_excel_files_list()
{
    $files = get_option('excel_imported_files', array());

    // Lọc các file không tồn tại
    $filtered_files = array();
    foreach ($files as $file) {
        if (file_exists($file['full_path'])) {
            $filtered_files[] = $file;
        }
    }

    // Cập nhật lại option nếu có file bị xóa
    if (count($filtered_files) !== count($files)) {
        update_option('excel_imported_files', $filtered_files);
    }

    return $filtered_files;
}

/**
 * Đọc file Excel và trả về dữ liệu dạng mảng
 */
function read_excel_file($file_path)
{
    // Kiểm tra file tồn tại
    if (!file_exists($file_path)) {
        return false;
    }

    try {
        // Sử dụng trực tiếp thư viện PhpSpreadsheet
        require_once(get_template_directory() . '/inc/lib/vendor/autoload.php');

        // Tạo reader dựa trên loại file
        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($file_path);
        $reader->setReadDataOnly(true);

        // Load bảng tính
        $spreadsheet = $reader->load($file_path);
        $worksheet = $spreadsheet->getActiveSheet();

        // Lấy dữ liệu dạng mảng
        $data = $worksheet->toArray();

        return $data;
    } catch (Exception $e) {
        return array(
            array('Lỗi'),
            array('Không thể đọc file Excel: ' . $e->getMessage())
        );
    }
}

/**
 * Đọc file Excel theo phân trang (mỗi lần đọc số dòng giới hạn)
 *
 * @param string $file_path Đường dẫn đến file Excel
 * @param int $start_row Dòng bắt đầu (0 là dòng đầu tiên)
 * @param int $limit Số dòng đọc mỗi lần
 * @return array|bool Dữ liệu đọc được hoặc false nếu có lỗi
 */
function read_excel_file_paged($file_path, $start_row = 0, $limit = 10)
{
    // Kiểm tra file tồn tại
    if (!file_exists($file_path)) {
        return false;
    }

    try {
        // Sử dụng trực tiếp thư viện PhpSpreadsheet
        require_once(get_template_directory() . '/inc/lib/vendor/autoload.php');

        // Tạo reader dựa trên loại file
        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($file_path);

        // Chỉ đọc dữ liệu (không đọc định dạng)
        $reader->setReadDataOnly(true);

        // File nhỏ, đọc toàn bộ rồi cắt
        $spreadsheet = $reader->load($file_path);
        $worksheet = $spreadsheet->getActiveSheet();

        // Lấy dữ liệu dạng mảng
        $all_data = $worksheet->toArray();

        // Lấy tiêu đề
        $headers = $all_data[0];

        // Lấy phần cần thiết
        $data = [];

        // Nếu start_row là 0, thêm hàng tiêu đề
        if ($start_row == 0) {
            $data[] = $headers;
            $data_rows = array_slice($all_data, 1, $limit);
        } else {
            $data_rows = array_slice($all_data, $start_row + 1, $limit);
        }

        // Ghép dữ liệu
        foreach ($data_rows as $row) {
            $data[] = $row;
        }

        return [
            'data' => $data,
            'headers' => $headers,
            'total_rows' => count($all_data) - 1, // Trừ dòng tiêu đề
            'start_row' => $start_row,
            'limit' => $limit,
            'has_more' => ($start_row + $limit < count($all_data) - 1)
        ];
    } catch (Exception $e) {
        return [
            'error' => true,
            'message' => 'Không thể đọc file Excel: ' . $e->getMessage()
        ];
    }
}

/**
 * Lấy tổng số dòng trong file Excel (không tính tiêu đề)
 */
function get_excel_total_rows($file_path)
{
    try {
        // Sử dụng trực tiếp thư viện PhpSpreadsheet
        require_once(get_template_directory() . '/inc/lib/vendor/autoload.php');

        // Đọc chỉ để đếm số dòng
        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($file_path);
        $reader->setReadDataOnly(true);

        $spreadsheet = $reader->load($file_path);
        $worksheet = $spreadsheet->getActiveSheet();

        // Đếm tổng số dòng
        $highestRow = $worksheet->getHighestRow();

        // Trừ 1 cho tiêu đề
        return $highestRow - 1;
    } catch (Exception $e) {
        return 0;
    }
}

/**
 * Thêm tab Import Data vào trang xem dữ liệu Excel
 */

function render_excel_import_page_extensions()
{
    add_action('admin_footer', function () {

        if($_GET['page'] == 'excel-import'){

            ?>
            <div id="global-import-results" style="display:none; margin-top: 20px; margin-bottom: 20px; padding: 15px; background-color: #fff; border: 1px solid #ddd; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                <h3 style="margin-top: 0;">Tiến trình Import</h3>
                <div class="progress-bar" style="width: 100%; background-color: #f1f1f1; border-radius: 4px; margin-bottom: 15px;">
                    <div class="progress-fill" style="width: 0%; height: 20px; background-color: #4CAF50; border-radius: 4px; transition: width 0.3s;"></div>
                </div>
                <div class="progress-text" style="margin-bottom: 15px; font-weight: bold;">Đang chuẩn bị...</div>
                <div class="results-content"></div>
                <div class="import-log" style="max-height: 300px; overflow-y: auto; margin-top: 15px; padding: 10px; border: 1px solid #eee; background-color: #fafafa; font-family: monospace; font-size: 12px;">
                    <div class="log-header" style="font-weight: bold; margin-bottom: 10px; color: #333;">Log chi tiết:</div>
                </div>
            </div>

            <script>
                jQuery(document).ready(function($) {
                    // Clean up any existing handlers to prevent duplicates
                    $(document).off('click', '.batch-import-button');
                    $(document).off('click', '.continue-batch-import');
                    $(document).off('click', '.close-import-result');

                    // Avoid global processing state - track each import separately
                    const importSessions = {};
                    let importSessionId = 0;

                    // Thêm nút Import
                    $('.button-small').each(function() {
                        if ($(this).text() === 'Xem dữ liệu' && $(this).next('.batch-import-button').length === 0) {
                            var filePath = $(this).attr('href').split('file=')[1];
                            if (filePath) {
                                filePath = decodeURIComponent(filePath);
                                $(this).after('&nbsp;<button type="button" class="button button-primary batch-import-button" data-file="' + filePath + '">Import Tự Động</button>');
                            }
                        }
                    });

                    // Start import button click handler
                    $(document).on('click', '.batch-import-button', function(e) {
                        e.preventDefault();

                        const button = $(this);
                        const filePath = button.data('file');

                        // Avoid multiple imports
                        if (button.prop('disabled')) return;

                        // Confirm before starting
                        if (!confirm('Bắt đầu import tự động từng dòng? Quá trình sẽ tiếp tục cho đến khi hoàn tất.')) {
                            return;
                        }

                        // Create a new import session ID
                        const sessionId = ++importSessionId;
                        importSessions[sessionId] = {
                            filePath: filePath,
                            originalButton: button,
                            currentRow: 1,
                            totalRows: 0,
                            completed: false
                        };

                        // Disable button and show processing
                        button.prop('disabled', true).text('Đang import...');

                        // Reset and show results container
                        $('#global-import-results .import-log').empty();
                        $('#global-import-results').show();
                        $('#global-import-results .results-content').html(
                            '<div class="import-loading">' +
                            '<p>Đang bắt đầu import dữ liệu...</p>' +
                            '<div class="spinner is-active" style="float:none; display:inline-block; visibility:visible;"></div>' +
                            '</div>'
                        );

                        // Scroll to top to show progress
                        $('html, body').animate({
                            scrollTop: 0
                        }, 300);

                        // Start the import process
                        processNextRow(sessionId);
                    });

                    // Function to process the next row
                    function processNextRow(sessionId) {
                        // Check if session exists
                        if (!importSessions[sessionId]) {
                            console.error("Import session not found:", sessionId);
                            return;
                        }

                        const session = importSessions[sessionId];
                        console.log(`Processing row ${session.currentRow} for session ${sessionId}`);

                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'import_excel_batch',
                                nonce: '<?php echo wp_create_nonce('excel_import_ajax_nonce'); ?>',
                                file_path: session.filePath,
                                start_row: session.currentRow
                            },
                            dataType: 'json',
                            success: function(response) {
                                console.log(`Row ${session.currentRow} response:`, response);

                                if (response.success) {
                                    // Update UI with results
                                    $('#global-import-results .results-content').html(response.data.html);

                                    // Update session data
                                    session.totalRows = response.data.total_rows;
                                    session.currentRow = response.data.next_row;

                                    // Update progress bar
                                    const progressPercent = response.data.progress;
                                    $('#global-import-results .progress-fill').css('width', progressPercent + '%');
                                    $('#global-import-results .progress-text').text(`Đang xử lý: ${response.data.current_row}/${response.data.total_rows} dòng (${progressPercent}%)`);

                                    // Add detailed log
                                    let logMessage = `[${new Date().toLocaleTimeString()}] Dòng #${response.data.current_row}: `;
                                    let logClass = '';

                                    if ($('#global-import-results .notice-success').length) {
                                        const successText = $('#global-import-results .notice-success p').first().text();
                                        if (successText.includes('cập nhật')) {
                                            logMessage += 'Cập nhật thành công (đã tồn tại)';
                                        } else {
                                            logMessage += 'Import thành công (tạo mới)';
                                        }
                                        logClass = 'success';
                                    } else if ($('#global-import-results .notice-error').length) {
                                        logMessage += $('#global-import-results .notice-error p').text();
                                        logClass = 'error';
                                    } else {
                                        logMessage += 'Đã xử lý';
                                        logClass = 'info';
                                    }

                                    $('#global-import-results .import-log').append(`<div class="log-entry log-${logClass}">${logMessage}</div>`);

                                    // Auto scroll log to bottom
                                    const logContainer = $('#global-import-results .import-log')[0];
                                    logContainer.scrollTop = logContainer.scrollHeight;

                                    // Check if there are more rows to process
                                    if (response.data.has_more) {
                                        // Continue with next row after a delay
                                        setTimeout(function() {
                                            processNextRow(sessionId);
                                        }, 1000); // 1 second delay
                                    } else {
                                        // Import completed
                                        console.log(`Import session ${sessionId} completed`);
                                        session.completed = true;

                                        // Re-enable the original button
                                        if (session.originalButton) {
                                            session.originalButton.prop('disabled', false).text('Import Tự Động');
                                        }

                                        // Clean up session
                                        delete importSessions[sessionId];
                                    }
                                } else {
                                    // Handle error
                                    console.error("Error in response:", response.data.message);
                                    $('#global-import-results .results-content').html(
                                        '<div class="notice notice-error"><p>' + response.data.message + '</p></div>'
                                    );

                                    // Re-enable the original button
                                    if (session.originalButton) {
                                        session.originalButton.prop('disabled', false).text('Import Tự Động');
                                    }

                                    // Clean up session
                                    delete importSessions[sessionId];
                                }
                            },
                            error: function(xhr, status, error) {
                                // Handle AJAX error
                                console.error("AJAX error:", error, xhr.responseText);
                                $('#global-import-results .results-content').html(
                                    '<div class="notice notice-error"><p>Lỗi AJAX: ' + error + '</p></div>'
                                );

                                // Re-enable the original button
                                if (session.originalButton) {
                                    session.originalButton.prop('disabled', false).text('Import Tự Động');
                                }

                                // Clean up session
                                delete importSessions[sessionId];
                            }
                        });
                    }

                    // Close results button handler
                    $(document).on('click', '.close-import-result', function() {
                        $('#global-import-results').slideUp();
                    });
                });
            </script>
            <style>
                .progress-bar {
                    height: 20px;
                    background-color: #f0f0f0;
                    border-radius: 4px;
                    margin: 10px 0;
                    overflow: hidden;
                }

                .progress-bar-inner {
                    height: 100%;
                    background-color: #0073aa;
                    width: 0%;
                    transition: width 0.3s;
                }

                .import-progress {
                    margin-bottom: 15px;
                }

                .import-log {
                    font-family: monospace;
                    font-size: 12px;
                    line-height: 1.4;
                }

                .import-log .log-entry {
                    margin: 2px 0;
                    padding: 4px 8px;
                    border-radius: 3px;
                    border-left: 3px solid #ddd;
                }

                .import-log .log-success {
                    background-color: #f0f8f0;
                    border-left-color: #4CAF50;
                    color: #2e7d32;
                }

                .import-log .log-error {
                    background-color: #fef5f5;
                    border-left-color: #f44336;
                    color: #c62828;
                }

                .import-log .log-info {
                    background-color: #f5f5f5;
                    border-left-color: #2196F3;
                    color: #1565c0;
                }

                .status-Chưa-import {
                    color: #666;
                    font-style: italic;
                }

                .status-Đang-import {
                    color: #ff9800;
                    font-weight: bold;
                }

                .status-Đã-import {
                    color: #4CAF50;
                    font-weight: bold;
                }

                .button-link-delete {
                    color: #a00 !important;
                }

                .button-link-delete:hover {
                    color: #dc3232 !important;
                }
            </style>
        <?php
        }

    });
}
add_action('admin_init', 'render_excel_import_page_extensions');

/**
 * AJAX endpoint để import từng dòng dữ liệu và chạy tự động
 */
add_action('wp_ajax_import_excel_batch', 'ajax_import_excel_batch');

function ajax_import_excel_batch()
{
    // Kiểm tra nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'excel_import_ajax_nonce')) {
        wp_send_json_error(array('message' => 'Lỗi bảo mật! Vui lòng tải lại trang.'));
    }

    // Lấy đường dẫn file Excel
    $file_path = isset($_POST['file_path']) ? sanitize_text_field($_POST['file_path']) : '';

    if (empty($file_path) || !file_exists($file_path)) {
        wp_send_json_error(array('message' => 'Không tìm thấy file Excel.'));
    }

    // Lấy vị trí bắt đầu import
    $start_row = isset($_POST['start_row']) ? intval($_POST['start_row']) : 1;
    $batch_size = 1; // Chỉ import 1 dòng mỗi lần

    // Đọc dữ liệu Excel
    $data = read_excel_file($file_path);

    if (!$data) {
        wp_send_json_error(array('message' => 'Không thể đọc dữ liệu từ file Excel.'));
    }

    // Lấy tiêu đề và dữ liệu
    $headers = $data[0];
    $data = array_slice($data, 1);
    $total_rows = count($data);

    if ($start_row > $total_rows) {
        wp_send_json_error(array('message' => 'Vị trí bắt đầu vượt quá tổng số dòng dữ liệu.'));
    }

    // Lấy dữ liệu cần import (chỉ 1 dòng)
    $row_index = $start_row;
    $row = $data[$start_row - 1];

    // Biến theo dõi kết quả
    $imported_posts = array();
    $errors = array();
    $success = false;

    // Bỏ qua dòng trống
    if (!empty(array_filter($row))) {
        // Kết hợp header và dữ liệu
        $row_data = array();
        foreach ($headers as $i => $header) {
            if (isset($row[$i])) {
                $row_data[$header] = $row[$i];
            }
        }

        // Chuyển đổi định dạng ngày từ Excel sang MySQL
        $date_fields = array(
            'date_created' => current_time('mysql'),
            'date_modified' => current_time('mysql')
        );

        // Tìm và chuyển đổi giá trị ngày
        foreach ($row_data as $header => $value) {
            $header_lower = strtolower(trim($header));

            if (strpos($header_lower, 'ngày đăng') !== false || strpos($header_lower, 'ngay dang') !== false) {
                if (is_numeric($value)) {
                    $date_fields['date_created'] = excel_date_to_mysql_date($value);
                    $row_data['excel_date_created'] = $value;
                }
            } else if (strpos($header_lower, 'ngày cập nhật') !== false || strpos($header_lower, 'ngay cap nhat') !== false) {
                if (is_numeric($value)) {
                    $date_fields['date_modified'] = excel_date_to_mysql_date($value);
                    $row_data['excel_date_modified'] = $value;
                }
            }
        }

        // Tạo post mới
        try {
            $post_id = create_office_post_from_data($row_data, $date_fields);

            if ($post_id && !is_wp_error($post_id)) {
                $success = true;
                $imported_posts[] = array(
                    'id' => $post_id,
                    'title' => get_the_title($post_id),
                    'row' => $row_index,
                    'edit_link' => get_edit_post_link($post_id, '')
                );
            } else {
                $error_message = is_wp_error($post_id) ? $post_id->get_error_message() : 'Lỗi không xác định khi tạo post.';
                $errors[] = "Dòng #{$row_index}: {$error_message}";
            }
        } catch (Exception $e) {
            $errors[] = "Dòng #{$row_index}: " . $e->getMessage();
        }
    }

    // Kiểm tra còn dữ liệu không
    $next_row = $start_row + 1;
    $has_more = $next_row <= $total_rows;
    $progress_percent = round(($start_row / $total_rows) * 100);

    // Tạo HTML kết quả
    ob_start();
    ?>
    <div class="import-progress">
        <div class="progress-bar">
            <div class="progress-bar-inner" style="width: <?php echo $progress_percent; ?>%"></div>
        </div>
        <p>Đang xử lý: <?php echo $start_row; ?> / <?php echo $total_rows; ?> dòng (<?php echo $progress_percent; ?>%)</p>
    </div>

    <?php if ($success) : ?>
        <div class="notice notice-success inline">
            <p>Đã import thành công dòng #<?php echo $row_index; ?>: <?php echo esc_html($imported_posts[0]['title']); ?></p>
            <p>Link bài viết: <a href="<?php echo esc_url($imported_posts[0]['edit_link']); ?>" target="_blank">Xem/Sửa</a></p>
        </div>
    <?php elseif (!empty($errors)) : ?>
        <div class="notice notice-error inline">
            <p><?php echo esc_html($errors[0]); ?></p>
        </div>
    <?php endif; ?>

    <?php if ($has_more) : ?>
        <div style="display:none;">
            <button type="button" id="auto-continue-import" class="button continue-batch-import" data-file="<?php echo esc_attr($file_path); ?>" data-start="<?php echo $next_row; ?>">
                Tiếp tục
            </button>
        </div>
        <script>
            // Tự động import dòng tiếp theo sau 500ms
            jQuery(document).ready(function($) {
                setTimeout(function() {
                    $('#auto-continue-import').trigger('click');
                }, 500);
            });
        </script>
    <?php else : ?>
        <div class="notice notice-success inline">
            <p><strong>Đã hoàn thành import toàn bộ dữ liệu!</strong></p>
        </div>
        <button type="button" class="button close-import-result">Đóng kết quả</button>
    <?php endif; ?>
<?php
    $result_html = ob_get_clean();

    wp_send_json_success(array(
        'html' => $result_html,
        'has_more' => $has_more,
        'next_row' => $next_row,
        'current_row' => $start_row,
        'total_rows' => $total_rows,
        'progress' => $progress_percent
    ));
}

/**
 * Tạo post mới với dữ liệu từ Excel
 *
 * @param array $data Dữ liệu từ Excel
 * @param array $date_fields Ngày tháng đã chuyển đổi
 * @return int|WP_Error ID của post mới hoặc lỗi
 */
function create_office_post_from_data($data, $date_fields)
{
    // Thêm ở đầu hàm nếu bạn muốn kiểm tra theo ID
    $existing_post_id = null;
    $lang = 'vi';
    if (isset($data['ID']) && !empty($data['ID']) && is_numeric($data['ID'])) {
        $existing_post_id = check_if_post_exists_by_id($data['ID'], 'office-rent');
        $post_title = $data['Tên văn phòng (VI)'];
    }

    // Nếu không tìm thấy theo ID, kiểm tra theo slug từ đường dẫn VI hoặc URL web cũ
    if (!$existing_post_id) {
        // Ưu tiên kiểm tra theo đường dẫn VI trước
        if (isset($data['Đường dẫn (VI)']) && !empty($data['Đường dẫn (VI)'])) {
            $vi_slug = trim($data['Đường dẫn (VI)'], '/');
            if (!empty($vi_slug)) {
                $existing_post_id = check_if_post_exists_by_direct_slug($vi_slug, 'office-rent');
            }
        }

        // Nếu vẫn không tìm thấy, kiểm tra theo URL web cũ
        if (!$existing_post_id && isset($data['URL web cũ']) && !empty($data['URL web cũ'])) {
            $existing_post_id = check_if_post_exists_by_slug($data['URL web cũ'], 'office-rent');
        }
    }

    // Nếu vẫn không tìm thấy theo slug, kiểm tra theo tiêu đề (chỉ check theo tên tiếng Việt)
    if (!$existing_post_id) {
        // Tìm tiêu đề từ dữ liệu - chỉ dùng tên tiếng Việt
        $post_title = '';
        if (isset($data['Tên văn phòng (VI)'])) {
            $post_title = $data['Tên văn phòng (VI)'];
        } else {
            // Tìm trường tiêu đề thích hợp nếu không có trường cụ thể
            foreach ($data as $key => $value) {
                if (stripos($key, 'tên') !== false || stripos($key, 'tiêu đề') !== false) {
                    $post_title = $value;
                    break;
                }
            }

            // Nếu vẫn không tìm thấy, dùng giá trị mặc định
            if (empty($post_title)) {
                $post_title = 'Văn phòng cho thuê ' . current_time('Y-m-d');
            }
        }

        // Kiểm tra xem bài viết đã tồn tại chưa - chỉ check theo tên tiếng Việt
        $existing_post_id = check_if_post_exists($post_title, 'office-rent');
    }

    $is_update = $existing_post_id ? true : false;
    $post_id = $existing_post_id; // Lưu lại ID cho việc xử lý sau này

    // Đảm bảo có post_title cho việc tạo post mới
    if (empty($post_title)) {
        if (isset($data['Tên văn phòng (VI)'])) {
            $post_title = $data['Tên văn phòng (VI)'];
        } else {
            $post_title = 'Văn phòng cho thuê ' . current_time('Y-m-d');
        }
    }

    // Tìm nội dung chi tiết
    $post_content = '';

    // Tìm mô tả ngắn
    $post_excerpt = '';

    // Chuẩn bị dữ liệu post
    $post_data = array(
        'post_title'    => $post_title,
        'post_content'  => $post_content,
        'post_excerpt'  => $post_excerpt,
        'post_status'   => 'publish',
        'post_type'     => 'office-rent',
        'post_date'     => $date_fields['date_created'],
        'post_modified' => $date_fields['date_modified'],
    );

    // Xử lý slug với ưu tiên: Đường dẫn (VI) > URL web cũ - chỉ set khi tạo mới
    if (!$existing_post_id) {
        $slug = get_slug_from_excel_data($data, 'vi');
        if ($slug) {
            $post_data['post_name'] = $slug;
        }
    }

    // Tạo post mới hoặc cập nhật post đã tồn tại
    if ($existing_post_id) {
        $post_data['ID'] = $existing_post_id;
        $post_id = wp_update_post($post_data);

        $is_update = true;
    } else {
        $post_id = wp_insert_post($post_data);
    }

    if (is_wp_error($post_id)) {
        return $post_id;
    }

    // Set a flag to indicate this is an update
    if ($is_update) {
        update_post_meta($post_id, '_is_updated_from_excel', '1');
    }


    // Cập nhật meta data
    foreach ($data as $key => $value) {
        if (!empty($value)) {
            // Chuyển đổi key thành meta_key
            $meta_key = sanitize_key(str_replace(' ', '_', strtolower($key)));

            // Lưu meta data
            update_post_meta($post_id, $meta_key, $value);
        }
    }

    // Xử lý các trường địa chỉ và tìm ID trong post type tương ứng

    // Xử lý Ngày đăng
    if (isset($data['Ngày đăng']) && !empty($data['Ngày đăng'])) {
        $date_value = $data['Ngày đăng'];
        if (is_numeric($date_value)) {
            update_field('ngay_gui', $date_fields['date_created'], $post_id);
        }
    }

    // Xử lý Ngày cập nhật
    if (isset($data['Ngày cập nhật']) && !empty($data['Ngày cập nhật'])) {
        $date_value = $data['Ngày cập nhật'];
        if (is_numeric($date_value)) {
            update_field('ngay_cap_nhat', $date_fields['date_modified'], $post_id);
        }
    }

    // Xử lý Hotline
    if (isset($data['Hotline']) && !empty($data['Hotline'])) {
        update_field('hotline', $data['Hotline'], $post_id);
        // Lưu thêm vào post meta để đảm bảo
        update_post_meta($post_id, 'hotline', $data['Hotline']);
    }

    // Xử lý show_or_off theo cột "Hiển thị"
    if (isset($data['Hiển thị']) && !empty($data['Hiển thị'])) {
        $show_value = strtolower(trim($data['Hiển thị']));
        // Chuyển đổi giá trị text thành boolean
        $show_or_off = ($show_value === 'hiện' || $show_value === 'hiển thị' || $show_value === 'true' || $show_value === '1') ? true : false;
        update_field('show_or_off', $show_or_off, $post_id);
    }

    // 1. Xử lý thành phố (mặc định là Tp. Hồ Chí Minh)
    $city_id = find_or_create_location_term('Tp. Hồ Chí Minh', 'city', $lang);
    if ($city_id) {
        update_post_meta($post_id, '_office_city', $city_id);
    }

    // 2. Xử lý quận huyện - kiểm tra cả 2 dạng tên trường
    if (isset($data['Địa chỉ quận (VI)']) && !empty($data['Địa chỉ quận (VI)'])) {
        $district_id = find_or_create_location_term($data['Địa chỉ quận (VI)'], 'district', $lang);
        if ($district_id) {
            update_post_meta($post_id, '_office_district', $district_id);
        }
    } elseif (isset($data['Địa chỉ quận']) && !empty($data['Địa chỉ quận'])) {
        $district_id = find_or_create_location_term($data['Địa chỉ quận'], 'district', $lang);
        if ($district_id) {
            update_post_meta($post_id, '_office_district', $district_id);
        }
    }

    // 3. Xử lý phường xã - kiểm tra cả 2 dạng tên trường
    if (isset($data['Địa chỉ phường (VI)']) && !empty($data['Địa chỉ phường (VI)'])) {
        $ward_id = find_or_create_location_term($data['Địa chỉ phường (VI)'], 'ward', $lang);
        if ($ward_id) {
            update_post_meta($post_id, '_office_ward', $ward_id);
        }
    } elseif (isset($data['Địa chỉ phường']) && !empty($data['Địa chỉ phường'])) {
        $ward_id = find_or_create_location_term($data['Địa chỉ phường'], 'ward', $lang);
        if ($ward_id) {
            update_post_meta($post_id, '_office_ward', $ward_id);
        }
    }

    // 4. Xử lý đường phố - kiểm tra cả 2 dạng tên trường
    if (isset($data['Địa chỉ đường (VI)']) && !empty($data['Địa chỉ đường (VI)'])) {
        $street_id = find_or_create_location_term($data['Địa chỉ đường (VI)'], 'street', $lang);
        if ($street_id) {
            update_post_meta($post_id, '_office_street', $street_id);
        }
    } elseif (isset($data['Địa chỉ đường']) && !empty($data['Địa chỉ đường'])) {
        $street_id = find_or_create_location_term($data['Địa chỉ đường'], 'street', $lang);
        if ($street_id) {
            update_post_meta($post_id, '_office_street', $street_id);
        }
    }

    // 5. Xử lý số nhà - kiểm tra cả 2 dạng tên trường
    if (isset($data['Địa chỉ (VI)']) && !empty($data['Địa chỉ (VI)'])) {
        update_post_meta($post_id, '_office_house_number', sanitize_text_field($data['Địa chỉ (VI)']));
    } elseif (isset($data['Địa chỉ']) && !empty($data['Địa chỉ'])) {
        update_post_meta($post_id, '_office_house_number', sanitize_text_field($data['Địa chỉ']));
    }

    // 6. Xử lý khu vực (block)
    if (isset($data['Khu vực (VI)']) && !empty($data['Khu vực (VI)'])) {
        $block_id = find_or_create_location_term($data['Khu vực (VI)'], 'block', $lang);
        if ($block_id) {
            update_post_meta($post_id, '_office_block', $block_id);
        }
    } elseif (isset($data['Khu vực']) && !empty($data['Khu vực'])) {
        $block_id = find_or_create_location_term($data['Khu vực'], 'block', $lang);
        if ($block_id) {
            update_post_meta($post_id, '_office_block', $block_id);
        }
    }

    // 7. Xử lý thương hiệu (brand)
    if (isset($data['Thương hiệu']) && !empty($data['Thương hiệu'])) {
        $brand_id = find_or_create_location_term($data['Thương hiệu'], 'brand', $lang);
        if ($brand_id) {
            update_post_meta($post_id, '_office_brand', $brand_id);
        }
    } elseif (isset($data['Thương hiệu (VI)']) && !empty($data['Thương hiệu (VI)'])) {
        $brand_id = find_or_create_location_term($data['Thương hiệu (VI)'], 'brand', $lang);
        if ($brand_id) {
            update_post_meta($post_id, '_office_brand', $brand_id);
        }
    } elseif (isset($data['Tên khác (VI)']) && !empty($data['Tên khác (VI)'])) {
        // Nếu không có thương hiệu nhưng có tên khác, có thể dùng tên khác làm thương hiệu
        $brand_id = find_or_create_location_term($data['Tên khác (VI)'], 'brand', $lang);
        if ($brand_id) {
            update_post_meta($post_id, '_office_brand', $brand_id);
        }
    }

    // 8. Xử lý khoảng diện tích
    if (isset($data['Khoảng diện tích']) && !empty($data['Khoảng diện tích'])) {
        $area_ids = process_area_ranges($data['Khoảng diện tích'], $lang);
        if (!empty($area_ids)) {
            update_post_meta($post_id, 'khoang_dien_tich', $area_ids);
        }
    }

    // Lưu thumbnail từ cột "Ảnh đại diện"
    if (isset($data['Ảnh đại diện']) && !empty($data['Ảnh đại diện'])) {
        // Đảm bảo URL hợp lệ trước khi xử lý
        $image_url = trim($data['Ảnh đại diện']);
        if (filter_var($image_url, FILTER_VALIDATE_URL)) {
            $thumbnail_id = import_remote_image($image_url, $post_id);
            if ($thumbnail_id) {
                set_post_thumbnail($post_id, $thumbnail_id);
            }
        }
    }

    // Xử lý thư viện ảnh từ cột "Thư viện ảnh"
    if (isset($data['Thư viện ảnh']) && !empty($data['Thư viện ảnh'])) {
        // Đảm bảo có các URL hợp lệ và được phân tách đúng
        $urls_string = trim($data['Thư viện ảnh']);
        if (!empty($urls_string)) {
            $gallery_ids = import_gallery_images($urls_string, $post_id);
            if (!empty($gallery_ids)) {
                // Lưu dưới dạng mảng ID đúng định dạng ACF
                update_field('gallery', $gallery_ids, $post_id);

                // Log để kiểm tra
                error_log('Đã import gallery cho post ' . $post_id . ': ' . print_r($gallery_ids, true));
            }
        }
    }

    // Bài viết giới thiệu - cập nhật theo field mới
    if (isset($data['Tiêu đề bài viết giới thiệu (VI)']) && !empty($data['Tiêu đề bài viết giới thiệu (VI)'])) {
        update_field('td_bai_viet_gioi_thieu', $data['Tiêu đề bài viết giới thiệu (VI)'], $post_id);
    }
    if (isset($data['Nội dung bài viết giới thiệu (VI)']) && !empty($data['Nội dung bài viết giới thiệu (VI)'])) {
        update_field('nd_bai_viet_gioi_thieu', $data['Nội dung bài viết giới thiệu (VI)'], $post_id);
    }

    // Bài viết chi tiết - cập nhật theo field mới
    if (isset($data['Tiêu đề bài viết chi tiết (VI)']) && !empty($data['Tiêu đề bài viết chi tiết (VI)'])) {
        update_field('td_bai_viet_chi_tiet', $data['Tiêu đề bài viết chi tiết (VI)'], $post_id);
    }
    if (isset($data['Nội dung bài viết chi tiết (VI)']) && !empty($data['Nội dung bài viết chi tiết (VI)'])) {
        update_field('nd_bai_viet_chi_tiet', $data['Nội dung bài viết chi tiết (VI)'], $post_id);
    }

    // Xử lý các trường vị trí
    if (isset($data['Tiêu đề vị trí (VI)']) && !empty($data['Tiêu đề vị trí (VI)'])) {
        update_field('title_location', $data['Tiêu đề vị trí (VI)'], $post_id);
    } else {
        update_field('title_location', 'Vị trí', $post_id);
    }

    if (isset($data['Tọa độ (VI)']) && !empty($data['Tọa độ (VI)'])) {
        update_field('toa_do', $data['Tọa độ (VI)'], $post_id);
    }

    if (isset($data['Google map (VI)']) && !empty($data['Google map (VI)'])) {
        update_field('google_map', $data['Google map (VI)'], $post_id);
    }

    if (isset($data['Google Street View 360 (VI)']) && !empty($data['Google Street View 360 (VI)'])) {
        update_field('google_street_view', $data['Google Street View 360 (VI)'], $post_id);
    }

    // Xử lý tiện ích
    if (isset($data['Tiêu đề tiện ích (VI)']) && !empty($data['Tiêu đề tiện ích (VI)'])) {
        update_field('title_utilities', $data['Tiêu đề tiện ích (VI)'], $post_id);
    } else {
        update_field('title_utilities', 'Tiện ích tòa nhà', $post_id);
    }

    if (isset($data['Nội dung tiện ích (VI)']) && !empty($data['Nội dung tiện ích (VI)'])) {
        update_field('content_utilities', $data['Nội dung tiện ích (VI)'], $post_id);
    }

    if (isset($data['Giá tiền']) && !empty($data['Giá tiền'])) {
        update_field('price_product', $data['Giá tiền'], $post_id);
    }

    if (isset($data['Khoảng giá']) && !empty($data['Khoảng giá'])) {
        update_field('price_range', $data['Khoảng giá'], $post_id);
    }

    if (isset($data['Phí quản lý']) && !empty($data['Phí quản lý'])) {
        update_field('price_management', $data['Phí quản lý'], $post_id);
    }

    // Chủ đề 1
    if (isset($data['Chủ đề 1']) && !empty($data['Chủ đề 1'])) {
        $topic1_page = get_page_by_title($data['Chủ đề 1'], OBJECT, 'page');
        if ($topic1_page) {
            update_field('chu_de_1', $topic1_page->ID, $post_id);
        }
    }

    // Chủ đề 2
    if (isset($data['Chủ đề 2']) && !empty($data['Chủ đề 2'])) {
        $topic2_page = get_page_by_title($data['Chủ đề 2'], OBJECT, 'page');
        if ($topic2_page) {
            update_field('chu_de_2', $topic2_page->ID, $post_id);
        }
    }

    // Chủ đề 3
    if (isset($data['Chủ đề 3']) && !empty($data['Chủ đề 3'])) {
        $topic3_page = get_page_by_title($data['Chủ đề 3'], OBJECT, 'page');
        if ($topic3_page) {
            update_field('chu_de_3', $topic3_page->ID, $post_id);
        }
    }

    if (isset($data['Danh sách tiện ích lân cận (VI)']) && !empty($data['Danh sách tiện ích lân cận (VI)'])) {
        $tien_ich_items = array_map('trim', explode(';', $data['Danh sách tiện ích lân cận (VI)']));

        $tien_ich_data = array();
        foreach ($tien_ich_items as $item) {
            $parts = array_map('trim', explode('|', $item));
            if (count($parts) >= 3) {
                $tien_ich_data[] = array(
                    'ten' => $parts[0],
                    'khoang_cach' => $parts[1],
                    'thoi_gian' => $parts[2]
                );
            }
        }

        if (!empty($tien_ich_data)) {
            update_field('ds_tien_ich_lan_can', $tien_ich_data, $post_id);
        }
    }

    // Ánh xạ các trường Excel vào các trường ACF dynamic_field cho các thông số tòa nhà
    $excel_to_acf_mapping = array(
        'Chủ đầu tư (VI)' => 'dynamic_field_thong_so_toa_nha_chu-dau-tu',
        'Kết cấu cao ốc (VI)' => 'dynamic_field_thong_so_toa_nha_ket-cau-cao-oc',
        'Năm hoàn thành (VI)' => 'dynamic_field_thong_so_toa_nha_nam-hoan-thanh',
        'Hạng tòa nhà (VI)' => 'dynamic_field_thong_so_toa_nha_hang-toa-nha',
        'Thông số thang máy (VI)' => 'dynamic_field_thong_so_toa_nha_thang-may',
        'Độ cao sàn đến trần' => 'dynamic_field_thong_so_toa_nha_do-cao-san-den-tran',
        'Hướng tòa nhà (VI)' => 'dynamic_field_thong_so_toa_nha_huong-toa-nha',
        'Diện tích một sàn (VI)' => 'dynamic_field_thong_so_toa_nha_dien-tich-san-dien-hinhm2',
        'Tổng diện tích sử dụng (VI)' => 'dynamic_field_thong_so_toa_nha_tong-dien-tich-su-dung',
        'Chứng nhận tòa nhà (VI)' => 'dynamic_field_thong_so_toa_nha_chung-nhan-toa-nha'
    );

    // Ánh xạ các trường Excel vào các trường ACF dynamic_field cho thông tin diện tích và giá thuê
    $excel_to_acf_price_mapping = array(
        'Diện tích cho thuê (VI)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_dien-tich-trong',
        'Giá thuê (VI)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_gia-thue',
        'Phí quản lý (VI)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_phi-quan-ly',
        'Tiền điện (VI)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_tien-dien',
        'Phí gửi xe máy (VI)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_phi-gui-xe-may',
        'Phí gửi ô tô (VI)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_phi-gui-o-to',
        'Phí làm việc ngoài giờ (VI)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_phi-lam-viec-ngoai-gio',
        'VAT (VI)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_vat',
        'Thời hạn thuê (VI)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_thoi-han-thue',
        'Đặt cọc (VI)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_dat-coc',
        'Thanh toán (VI)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_thanh-toan',
    );

    // Ánh xạ các trường mới
    $excel_to_acf_mapping_utilities = array(
        // 'Thông tin tiện ích (VI)' => 'content_utilities',
        'Thang máy (VI)' => 'dynamic_field_tien_ich_toa_nha_thang-may',
        'Phòng cháy chữa cháy (VI)' => 'dynamic_field_tien_ich_toa_nha_phong-chay-chua-chay',
        'Máy lạnh (VI)' => 'dynamic_field_tien_ich_toa_nha_may-lanh',
        'Bãi xe (VI)' => 'dynamic_field_tien_ich_toa_nha_bai-xe',
        'Đội ngũ quản lý vận hành (VI)' => 'dynamic_field_tien_ich_toa_nha_doi-ngu-quan-ly-van-hanh',
        'Điện dự phòng (VI)' => 'dynamic_field_tien_ich_toa_nha_dien-du-phong',
        'Hệ thống chiếu sáng (VI)' => 'dynamic_field_tien_ich_toa_nha_he-thong-chieu-sang',
        'Dịch vụ dọn dẹp vệ sinh (VI)' => 'dynamic_field_tien_ich_toa_nha_dich-vu-don-dep-ve-sinh',
        'Cổng an ninh (VI)' => 'dynamic_field_tien_ich_toa_nha_cong-an-ninh',
        'Tiện ích chung (VI)' => 'dynamic_field_tien_ich_toa_nha_tien-ich-chung',
        'Tiện ích nổi bật (VI)' => 'dynamic_field_tien_ich_toa_nha_tien-ich-noi-bat',
        'Dịch vụ hỗ trợ IT (VI)' => 'dynamic_field_tien_ich_toa_nha_dich-vu-ho-tro-it', // bỏ
        'Đăng ký địa chỉ kinh doanh Nhanh chóng dễ dàng (VI)' => 'dynamic_field_tien_ich_toa_nha_dang-ky-dia-chi-kinh-doanh-nhanh-chong-de-dang', // bỏ
    );

    // Cập nhật các trường ACF dynamic field từ dữ liệu Excel
    if (isset($data['Tiêu đề diện tích và cho thuê (VI)']) && !empty($data['Tiêu đề diện tích và cho thuê (VI)'])) {
        update_field('title_parameter', $data['Tiêu đề diện tích và cho thuê (VI)'], $post_id);
    } else {
        update_field('title_parameter', 'Thông số tòa nhà', $post_id);
    }

    foreach ($excel_to_acf_mapping as $excel_field => $acf_field) {
        if (isset($data[$excel_field]) && !empty($data[$excel_field])) {
            update_field($acf_field, $data[$excel_field], $post_id);
        }
    }

    // Cập nhật các trường ACF liên quan đến giá thuê và diện tích
    if (isset($data['Tiêu đề diện tích và cho thuê (VI)']) && !empty($data['Tiêu đề diện tích và cho thuê (VI)'])) {
        update_field('title_info', $data['Tiêu đề diện tích và cho thuê (VI)'], $post_id);
    } else {
        update_field('title_info', 'Thông tin diện tích và giá thuê', $post_id);
    }

    foreach ($excel_to_acf_price_mapping as $excel_field => $acf_field) {
        if (isset($data[$excel_field]) && !empty($data[$excel_field])) {
            update_field($acf_field, $data[$excel_field], $post_id);
        }
    }

    // Cập nhật các trường ACF liên quan đến tiện ích - đã xử lý ở trên
    foreach ($excel_to_acf_mapping_utilities as $excel_field => $acf_field) {
        if (isset($data[$excel_field]) && !empty($data[$excel_field])) {
            update_field($acf_field, $data[$excel_field], $post_id);
        }
    }

    // google street view
    if (isset($data['Google Street View 360 (VI)']) && !empty($data['Google Street View 360 (VI)'])) {
        update_field('google_street_view', $data['Google Street View 360 (VI)'], $post_id);
    }

    // Xử lý hạng tòa nhà (lấy post ID từ post type building-class)
    if (isset($data['Hạng tòa nhà (VI)']) && !empty($data['Hạng tòa nhà (VI)'])) {
        $class_id = find_or_create_location_term($data['Hạng tòa nhà (VI)'], 'building-class');
        if ($class_id) {
            update_field('dynamic_field_thong_so_toa_nha_hang-toa-nha', $class_id, $post_id);
        }
    } elseif (isset($data['Xếp hạng (VI)']) && !empty($data['Xếp hạng (VI)'])) {
        // Dùng trường thay thế nếu không có "Hạng tòa nhà"
        $class_id = find_or_create_location_term($data['Xếp hạng (VI)'], 'building-class');
        if ($class_id) {
            update_field('dynamic_field_thong_so_toa_nha_hang-toa-nha', $class_id, $post_id);
        }
    }

    // Xử lý hướng tòa nhà (lấy post IDs từ post type building-orientation)
    if (isset($data['Hướng tòa nhà (VI)']) && !empty($data['Hướng tòa nhà (VI)'])) {
        // Phân tách các hướng nếu có nhiều
        $orientations = array_map('trim', explode(',', $data['Hướng tòa nhà (VI)']));
        $orientation_ids = array();

        foreach ($orientations as $orientation) {
            $orientation_id = find_or_create_location_term($orientation, 'building-orientation');
            if ($orientation_id) {
                $orientation_ids[] = $orientation_id;
            }
        }

        if (!empty($orientation_ids)) {
            update_field('dynamic_field_thong_so_toa_nha_huong-toa-nha', $orientation_ids, $post_id);
        }
    }

    // Xử lý VAT (nếu có)
    // Mặc định để trống hoặc lấy từ dữ liệu nếu có
    $vat_value = '10%';
    if (isset($data['VAT (VI']) && !empty($data['VAT (VI'])) {
        $vat_value = $data['VAT (VI'];
    }
    update_field('dynamic_field_thong_tin_dien_tich_va_gia_thue_vat', $vat_value, $post_id);

    // Sau khi đã tạo/cập nhật post tiếng Việt thành công và có $post_id
    // Thêm đoạn code sau vào cuối hàm create_office_post_from_data, trước return $post_id

    // Xử lý bản tiếng Anh
    // Khi đến phần xử lý tiếng Anh, sửa lại như sau:
    if ($post_id && !is_wp_error($post_id) && isset($data['Tên văn phòng (EN)']) && !empty($data['Tên văn phòng (EN)'])) {
        // Kiểm tra xem đã có bản dịch tiếng Anh chưa
        $lang = 'en';
        $english_post_id = get_post_meta($post_id, '_office_english_version', true);

        // Thêm debug
        error_log("Processing English version for Vietnamese post $post_id. Found linked English post ID: " . ($english_post_id ? $english_post_id : 'none'));

        // QUAN TRỌNG: Kiểm tra kỹ xem bản tiếng Anh có tồn tại thực sự không
        $english_post_exists = false;
        if ($english_post_id) {
            $english_post = get_post($english_post_id);
            if ($english_post && $english_post->post_type == 'office-rent' && $english_post->post_status != 'trash') {
                $english_post_exists = true;
                error_log("Confirmed English post $english_post_id exists and is valid");
            } else {
                error_log("English post $english_post_id doesn't exist or is invalid");
            }
        }

        if ($english_post_exists) {
            // Cập nhật post hiện có
            $english_post_data = array(
                'ID'            => $english_post_id,
                'post_title'    => $data['Tên văn phòng (EN)'],
                'post_status'   => 'publish',
                'post_date'     => $date_fields['date_created'],
                'post_modified' => $date_fields['date_modified']
            );
            $english_post_id = wp_update_post($english_post_data);
            error_log("Updated existing English post: $english_post_id");
        } else {
            // Tạo post mới cho bản tiếng Anh
            $english_post_data = array(
                'post_title'    => $data['Tên văn phòng (EN)'],
                'post_content'  => '',
                'post_excerpt'  => '',
                'post_status'   => 'publish',
                'post_type'     => 'office-rent',
                'post_date'     => $date_fields['date_created'],
                'post_modified' => $date_fields['date_modified']
            );

            // Thêm slug cho bản tiếng Anh với ưu tiên: Đường dẫn (EN) > URL web cũ + -en
            $en_slug = get_slug_from_excel_data($data, 'en');
            if ($en_slug) {
                $english_post_data['post_name'] = $en_slug;
            }

            $english_post_id = wp_insert_post($english_post_data);
            error_log("Created new English post: $english_post_id");

            // Lưu mối quan hệ hai chiều
            if (!is_wp_error($english_post_id)) {
                update_post_meta($post_id, '_office_english_version', $english_post_id);
                update_post_meta($english_post_id, '_office_vietnamese_version', $post_id);
                error_log("Linked posts: VI=$post_id <-> EN=$english_post_id");
            }
        }

        // Liên kết ngôn ngữ nếu có plugin WPML và nếu english_post_id hợp lệ
        if (!is_wp_error($english_post_id) && function_exists('icl_object_id')) {
            global $sitepress;

            // Đảm bảo bản tiếng Việt được set đúng ngôn ngữ
            $wpml_element_type = apply_filters('wpml_element_type', 'office-rent');

            // Set ngôn ngữ cho bản tiếng Việt
            $vi_set_language_args = array(
                'element_id'    => $post_id,
                'element_type'  => $wpml_element_type,
                'trid'          => false,
                'language_code' => 'vi'
            );
            do_action('wpml_set_element_language_details', $vi_set_language_args);

            // Lấy trid của bản tiếng Việt
            $trid = apply_filters('wpml_element_trid', null, $post_id, $wpml_element_type);

            if ($trid) {
                // Set ngôn ngữ cho bản tiếng Anh và liên kết với bản tiếng Việt
                $en_set_language_args = array(
                    'element_id'    => $english_post_id,
                    'element_type'  => $wpml_element_type,
                    'trid'          => $trid,
                    'language_code' => 'en',
                    'source_language_code' => 'vi'
                );
                do_action('wpml_set_element_language_details', $en_set_language_args);

                error_log("WPML: Linked VI post $post_id with EN post $english_post_id using trid $trid");
            } else {
                error_log("WPML: Could not get trid for post $post_id");
            }
        }

        if ($english_post_id && !is_wp_error($english_post_id)) {
            // Đánh dấu cập nhật từ Excel
            update_post_meta($english_post_id, '_is_updated_from_excel', '1');

            // Lưu lại mối quan hệ nếu chưa được lưu
            if (!get_post_meta($english_post_id, '_office_vietnamese_version', true)) {
                update_post_meta($english_post_id, '_office_vietnamese_version', $post_id);
            }
            if (!get_post_meta($post_id, '_office_english_version', true)) {
                update_post_meta($post_id, '_office_english_version', $english_post_id);
            }

            // Copy thumbnail từ bản tiếng Việt - đảm bảo đúng cách
            $thumbnail_id = get_post_thumbnail_id($post_id);
            if ($thumbnail_id) {
                set_post_thumbnail($english_post_id, $thumbnail_id);
            }

            // Copy gallery từ bản tiếng Việt - kiểm tra xem gallery được lưu đúng format
            $gallery = get_field('gallery', $post_id);
            if (!empty($gallery) && is_array($gallery)) {
                update_field('gallery', $gallery, $english_post_id);
                // Debug log
                error_log('Copied gallery to English post ' . $english_post_id . ': ' . print_r($gallery, true));
            }

            // Cập nhật meta data
            foreach ($data as $key => $value) {
                if (!empty($value) && strpos($key, '(EN)') !== false) {
                    // Chuyển đổi key thành meta_key
                    $meta_key = sanitize_key(str_replace(' ', '_', strtolower($key)));
                    // Lưu meta data
                    update_post_meta($english_post_id, $meta_key, $value);
                }
            }

            // Hotline (giữ nguyên như bản tiếng Việt)
            if (isset($data['Hot Line (EN)']) && !empty($data['Hot Line (EN)'])) {
                update_field('hotline', $data['Hot Line (EN)'], $english_post_id);
                update_post_meta($english_post_id, 'hotline', $data['Hot Line (EN)']);
            } elseif (isset($data['Hotline']) && !empty($data['Hotline'])) {
                update_field('hotline', $data['Hotline'], $english_post_id);
                update_post_meta($english_post_id, 'hotline', $data['Hotline']);
            }

            // Copy show_or_off từ bản tiếng Việt
            $show_or_off = get_field('show_or_off', $post_id);
            if ($show_or_off !== null) {
                update_field('show_or_off', $show_or_off, $english_post_id);
            }

            // 1. Xử lý thành phố (mặc định là Tp. Hồ Chí Minh)
            $city_id = find_or_create_location_term('Ho Chi Minh City', 'city', $lang);
            if ($city_id) {
                update_post_meta($english_post_id, '_office_city', $city_id);
            }

            // 2. Xử lý quận huyện
            if (isset($data['Địa chỉ quận (EN)']) && !empty($data['Địa chỉ quận (EN)'])) {
                $district_id = find_or_create_location_term($data['Địa chỉ quận (EN)'], 'district', $lang);
                if ($district_id) {
                    update_post_meta($english_post_id, '_office_district', $district_id);
                }
            }

            // 3. Xử lý phường xã
            if (isset($data['Địa chỉ phường (EN)']) && !empty($data['Địa chỉ phường (EN)'])) {
                $ward_id = find_or_create_location_term($data['Địa chỉ phường (EN)'], 'ward', $lang);
                if ($ward_id) {
                    update_post_meta($english_post_id, '_office_ward', $ward_id);
                }
            }

            // 4. Xử lý đường phố
            if (isset($data['Địa chỉ đường (EN)']) && !empty($data['Địa chỉ đường (EN)'])) {
                $street_id = find_or_create_location_term($data['Địa chỉ đường (EN)'], 'street', $lang);
                if ($street_id) {
                    update_post_meta($english_post_id, '_office_street', $street_id);
                }
            }

            // 5. Xử lý số nhà
            if (isset($data['Địa chỉ (EN)']) && !empty($data['Địa chỉ (EN)'])) {
                update_post_meta($english_post_id, '_office_house_number', sanitize_text_field($data['Địa chỉ (EN)']));
            }

            // 6. Xử lý khu vực (block)
            if (isset($data['Khu vực (EN)']) && !empty($data['Khu vực (EN)'])) {
                $block_id = find_or_create_location_term($data['Khu vực (EN)'], 'block', $lang);
                if ($block_id) {
                    update_post_meta($english_post_id, '_office_block', $block_id);
                }
            } else {
                // Copy từ bản tiếng Việt
                $block_id = get_post_meta($post_id, '_office_block', true);
                if ($block_id) {
                    update_post_meta($english_post_id, '_office_block', $block_id);
                }
            }

            // 7. Xử lý thương hiệu (brand)
            if (isset($data['Thương hiệu (EN)']) && !empty($data['Thương hiệu (EN)'])) {
                $brand_id = find_or_create_location_term($data['Thương hiệu (EN)'], 'brand', $lang);
                if ($brand_id) {
                    update_post_meta($english_post_id, '_office_brand', $brand_id);
                }
            } else {
                // Copy từ bản tiếng Việt
                $brand_id = get_post_meta($post_id, '_office_brand', true);
                if ($brand_id) {
                    update_post_meta($english_post_id, '_office_brand', $brand_id);
                }
            }

            // 8. Xử lý khoảng diện tích (copy từ bản tiếng Việt)
            $area_ids = get_post_meta($post_id, 'khoang_dien_tich', true);
            if (!empty($area_ids)) {
                update_post_meta($english_post_id, 'khoang_dien_tich', $area_ids);
            }

            // Bài viết giới thiệu
            if (isset($data['Bài viết giới thiệu (EN)']) && !empty($data['Bài viết giới thiệu (EN)'])) {
                update_field('td_bai_viet_gioi_thieu', 'Office Introduction ' . $data['Tên văn phòng (EN)'], $english_post_id);
                update_field('nd_bai_viet_gioi_thieu', $data['Bài viết giới thiệu (EN)'], $english_post_id);
            } elseif (isset($data['Nội dung bài viết giới thiệu (EN)']) && !empty($data['Nội dung bài viết giới thiệu (EN)'])) {
                update_field('td_bai_viet_gioi_thieu', $data['Tiêu đề bài viết giới thiệu (EN)'], $english_post_id);
                update_field('nd_bai_viet_gioi_thieu', $data['Nội dung bài viết giới thiệu (EN)'], $english_post_id);
            }

            // Bài viết chi tiết
            if (isset($data['Bài viết chi tiết (EN)']) && !empty($data['Bài viết chi tiết (EN)'])) {
                update_field('td_bai_viet_chi_tiet', 'Detailed Office Information ' . $data['Tên văn phòng (EN)'], $english_post_id);
                update_field('nd_bai_viet_chi_tiet', $data['Bài viết chi tiết (EN)'], $english_post_id);
            } elseif (isset($data['Nội dung bài viết chi tiết (EN)']) && !empty($data['Nội dung bài viết chi tiết (EN)'])) {
                update_field('td_bai_viet_chi_tiet', $data['Tiêu đề bài viết chi tiết (EN)'], $english_post_id);
                update_field('nd_bai_viet_chi_tiet', $data['Nội dung bài viết chi tiết (EN)'], $english_post_id);
            }

            // Tọa độ
            if (isset($data['Tọa độ (EN)']) && !empty($data['Tọa độ (EN)'])) {
                update_field('toa_do', $data['Tọa độ (EN)'], $english_post_id);
            } else {
                // Copy từ bản tiếng Việt
                $toa_do = get_field('toa_do', $post_id);
                if ($toa_do) {
                    update_field('toa_do', $toa_do, $english_post_id);
                }
            }

            // Google Map - Sử dụng cùng giá trị với bản tiếng Việt
            if (isset($data['Google map (VI)']) && !empty($data['Google map (VI)'])) {
                update_field('google_map', $data['Google map (VI)'], $english_post_id);
            }

            // Google Street View
            if (isset($data['Map Street View 360']) && !empty($data['Map Street View 360'])) {
                update_field('google_street_view', $data['Map Street View 360'], $english_post_id);
            }

            // Giá tiền
            if (isset($data['Giá tiền']) && !empty($data['Giá tiền'])) {
                update_field('price_product', $data['Giá tiền'], $english_post_id);
            }

            if (isset($data['Khoảng giá']) && !empty($data['Khoảng giá'])) {
                update_field('price_range', $data['Khoảng giá'], $english_post_id);
            }

            // Phí dịch vụ
            if (isset($data['Phí dịch vụ (EN)']) && !empty($data['Phí dịch vụ (EN)'])) {
                update_field('price_management', $data['Phí dịch vụ (EN)'], $english_post_id);
            }

            // Chủ đề
            if (isset($data['Chủ đề (EN)']) && !empty($data['Chủ đề (EN)'])) {
                $topic1_page = get_page_by_title($data['Chủ đề (EN)'], OBJECT, 'page');
                if ($topic1_page) {
                    update_field('chu_de_1', $topic1_page->ID, $english_post_id);
                }
            } else {
                // Copy từ bản tiếng Việt
                $chu_de_1 = get_field('chu_de_1', $post_id);
                if ($chu_de_1) {
                    update_field('chu_de_1', $chu_de_1, $english_post_id);
                }
            }

            // Chủ đề con 1
            if (isset($data['Chủ đề con 1 (EN)']) && !empty($data['Chủ đề con 1 (EN)'])) {
                $topic2_page = get_page_by_title($data['Chủ đề con 1 (EN)'], OBJECT, 'page');
                if ($topic2_page) {
                    update_field('chu_de_2', $topic2_page->ID, $english_post_id);
                }
            } else {
                // Copy từ bản tiếng Việt
                $chu_de_2 = get_field('chu_de_2', $post_id);
                if ($chu_de_2) {
                    update_field('chu_de_2', $chu_de_2, $english_post_id);
                }
            }

            // Chủ đề con 2
            if (isset($data['Chủ đề con 2 (EN)']) && !empty($data['Chủ đề con 2 (EN)'])) {
                $topic3_page = get_page_by_title($data['Chủ đề con 2 (EN)'], OBJECT, 'page');
                if ($topic3_page) {
                    update_field('chu_de_3', $topic3_page->ID, $english_post_id);
                }
            } else {
                // Copy từ bản tiếng Việt
                $chu_de_3 = get_field('chu_de_3', $post_id);
                if ($chu_de_3) {
                    update_field('chu_de_3', $chu_de_3, $english_post_id);
                }
            }

            // Tiện ích lân cận
            if (isset($data['Danh sách tiện ích lân cận (EN)']) && !empty($data['Danh sách tiện ích lân cận (EN)'])) {
                $tien_ich_items = array_map('trim', explode(';', $data['Danh sách tiện ích lân cận (EN)']));

                $tien_ich_data = array();
                foreach ($tien_ich_items as $item) {
                    $parts = array_map('trim', explode('|', $item));
                    if (count($parts) >= 3) {
                        $tien_ich_data[] = array(
                            'ten' => $parts[0],
                            'khoang_cach' => $parts[1],
                            'thoi_gian' => $parts[2]
                        );
                    }
                }

                if (!empty($tien_ich_data)) {
                    update_field('ds_tien_ich_lan_can', $tien_ich_data, $english_post_id);
                }
            } else {
                // Copy từ bản tiếng Việt
                $tien_ich_data = get_field('ds_tien_ich_lan_can', $post_id);
                if ($tien_ich_data) {
                    update_field('ds_tien_ich_lan_can', $tien_ich_data, $english_post_id);
                }
            }

            // Ánh xạ các trường Excel vào các trường ACF dynamic_field cho các thông số tòa nhà
            $excel_to_acf_mapping = array(
                'Chủ đầu tư (EN)' => 'dynamic_field_thong_so_toa_nha_chu-dau-tu',
                'Kết cấu cao ốc (EN)' => 'dynamic_field_thong_so_toa_nha_ket-cau-cao-oc',
                'Năm hoàn thành (VI)' => 'dynamic_field_thong_so_toa_nha_nam-hoan-thanh',
                'Hạng tòa nhà (EN)' => 'dynamic_field_thong_so_toa_nha_hang-toa-nha',
                'Thông số thang máy (EN)' => 'dynamic_field_thong_so_toa_nha_thang-may',
                'Độ cao sàn đến trần' => 'dynamic_field_thong_so_toa_nha_do-cao-san-den-tran',
                'Hướng tòa nhà (EN)' => 'dynamic_field_thong_so_toa_nha_huong-toa-nha',
                'Diện tích một sàn (EN)' => 'dynamic_field_thong_so_toa_nha_dien-tich-san-dien-hinhm2',
                'Tổng diện tích sử dụng (EN)' => 'dynamic_field_thong_so_toa_nha_tong-dien-tich-su-dung',
                'Chứng nhận tòa nhà (EN)' => 'dynamic_field_thong_so_toa_nha_chung-nhan-toa-nha'
            );

            // Ánh xạ các trường Excel vào các trường ACF dynamic_field cho thông tin diện tích và giá thuê
            $excel_to_acf_price_mapping = array(
                'Diện tích cho thuê (EN)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_dien-tich-trong',
                'Giá thuê (EN)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_gia-thue',
                'Phí quản lý (EN)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_phi-quan-ly',
                'Tiền điện (EN)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_tien-dien',
                'Phí gửi xe máy (EN)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_phi-gui-xe-may',
                'Phí gửi ô tô (EN)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_phi-gui-o-to',
                'Phí làm việc ngoài giờ (EN)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_phi-lam-viec-ngoai-gio',
                'VAT (EN)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_vat',
                'Thời hạn thuê (EN)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_thoi-han-thue',
                'Đặt cọc (EN)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_dat-coc',
                'Thanh toán (EN)' => 'dynamic_field_thong_tin_dien_tich_va_gia_thue_thanh-toan',
            );

            // Ánh xạ các trường mới
            $excel_to_acf_mapping_utilities = array(
                // 'Thông tin tiện ích (EN)' => 'content_utilities',
                'Thang máy (EN)' => 'dynamic_field_tien_ich_toa_nha_thang-may',
                'Phòng cháy chữa cháy (EN)' => 'dynamic_field_tien_ich_toa_nha_phong-chay-chua-chay',
                'Máy lạnh (EN)' => 'dynamic_field_tien_ich_toa_nha_may-lanh',
                'Bãi xe (VI)' => 'dynamic_field_tien_ich_toa_nha_bai-xe',
                'Đội ngũ quản lý vận hành (EN)' => 'dynamic_field_tien_ich_toa_nha_doi-ngu-quan-ly-van-hanh',
                'Điện dự phòng (EN)' => 'dynamic_field_tien_ich_toa_nha_dien-du-phong',
                'Hệ thống chiếu sáng (EN)' => 'dynamic_field_tien_ich_toa_nha_he-thong-chieu-sang',
                'Dịch vụ dọn dẹp vệ sinh (EN)' => 'dynamic_field_tien_ich_toa_nha_dich-vu-don-dep-ve-sinh',
                'Cổng an ninh (EN)' => 'dynamic_field_tien_ich_toa_nha_cong-an-ninh',
                'Tiện ích chung (EN)' => 'dynamic_field_tien_ich_toa_nha_tien-ich-chung',
                'Tiện ích nổi bật (EN)' => 'dynamic_field_tien_ich_toa_nha_tien-ich-noi-bat',
                'Dịch vụ hỗ trợ IT (EN)' => 'dynamic_field_tien_ich_toa_nha_dich-vu-ho-tro-it', // bỏ
                'Đăng ký địa chỉ kinh doanh Nhanh chóng dễ dàng (EN)' => 'dynamic_field_tien_ich_toa_nha_dang-ky-dia-chi-kinh-doanh-nhanh-chong-de-dang', // bỏ
            );

            // Cập nhật các trường ACF dynamic field từ dữ liệu Excel
            if (isset($data['Tiêu đề diện tích và cho thuê (EN)']) && !empty($data['Tiêu đề diện tích và cho thuê (EN)'])) {
                update_field('title_parameter', $data['Tiêu đề diện tích và cho thuê (EN)'], $english_post_id);
            } else {
                update_field('title_parameter', 'Building Parameters', $english_post_id);
            }

            foreach ($excel_to_acf_mapping as $excel_field => $acf_field) {
                if (isset($data[$excel_field]) && !empty($data[$excel_field])) {
                    update_field($acf_field, $data[$excel_field], $english_post_id);
                }
            }

            // Cập nhật các trường ACF liên quan đến giá thuê và diện tích
            if (isset($data['Tiêu đề diện tích và cho thuê (EN)']) && !empty($data['Tiêu đề diện tích và cho thuê (EN)'])) {
                update_field('title_info', $data['Tiêu đề diện tích và cho thuê (EN)'], $english_post_id);
            }
            foreach ($excel_to_acf_price_mapping as $excel_field => $acf_field) {
                if (isset($data[$excel_field]) && !empty($data[$excel_field])) {
                    update_field($acf_field, $data[$excel_field], $english_post_id);
                }
            }

            // Bài viết giới thiệu tiếng Anh - cập nhật theo field mới
            if (isset($data['Tiêu đề bài viết giới thiệu (EN)']) && !empty($data['Tiêu đề bài viết giới thiệu (EN)'])) {
                update_field('td_bai_viet_gioi_thieu', $data['Tiêu đề bài viết giới thiệu (EN)'], $english_post_id);
            }
            if (isset($data['Nội dung bài viết giới thiệu (EN)']) && !empty($data['Nội dung bài viết giới thiệu (EN)'])) {
                update_field('nd_bai_viet_gioi_thieu', $data['Nội dung bài viết giới thiệu (EN)'], $english_post_id);
            }

            // Bài viết chi tiết tiếng Anh - cập nhật theo field mới
            if (isset($data['Tiêu đề bài viết chi tiết (EN)']) && !empty($data['Tiêu đề bài viết chi tiết (EN)'])) {
                update_field('td_bai_viet_chi_tiet', $data['Tiêu đề bài viết chi tiết (EN)'], $english_post_id);
            }
            if (isset($data['Nội dung bài viết chi tiết (EN)']) && !empty($data['Nội dung bài viết chi tiết (EN)'])) {
                update_field('nd_bai_viet_chi_tiet', $data['Nội dung bài viết chi tiết (EN)'], $english_post_id);
            }

            // Xử lý các trường vị trí cho bản tiếng Anh
            if (isset($data['Tiêu đề vị trí (EN)']) && !empty($data['Tiêu đề vị trí (EN)'])) {
                update_field('title_location', $data['Tiêu đề vị trí (EN)'], $english_post_id);
            } else {
                update_field('title_location', 'Building location', $english_post_id);
            }

            // Google Street View - sử dụng cùng giá trị với bản tiếng Việt
            if (isset($data['Google Street View 360 (VI)']) && !empty($data['Google Street View 360 (VI)'])) {
                update_field('google_street_view', $data['Google Street View 360 (VI)'], $english_post_id);
            }


            // Cập nhật các trường ACF liên quan đến tiện ích
            if (isset($data['Tiêu đề tiện ích (EN)']) && !empty($data['Tiêu đề tiện ích (EN)'])) {
                update_field('title_utilities', $data['Tiêu đề tiện ích (EN)'], $english_post_id);
            } else {
                update_field('title_utilities', 'Building Amenities', $english_post_id);
            }

            if (isset($data['Nội dung tiện ích (EN)']) && !empty($data['Nội dung tiện ích (EN)'])) {
                update_field('content_utilities', $data['Nội dung tiện ích (EN)'], $english_post_id);
            }

            foreach ($excel_to_acf_mapping_utilities as $excel_field => $acf_field) {
                if (isset($data[$excel_field]) && !empty($data[$excel_field])) {
                    update_field($acf_field, $data[$excel_field], $english_post_id);
                }
            }

            // Xử lý hạng tòa nhà (lấy post ID từ post type building-class)
            if (isset($data['Hạng tòa nhà (EN)']) && !empty($data['Hạng tòa nhà (EN)'])) {
                $class_id = find_or_create_location_term($data['Hạng tòa nhà (EN)'], 'building-class');
                if ($class_id) {
                    update_field('dynamic_field_thong_so_toa_nha_hang-toa-nha', $class_id, $english_post_id);
                }
            } elseif (isset($data['Xếp hạng (EN)']) && !empty($data['Xếp hạng (EN)'])) {
                // Dùng trường thay thế nếu không có "Hạng tòa nhà"
                $class_id = find_or_create_location_term($data['Xếp hạng (EN)'], 'building-class');
                if ($class_id) {
                    update_field('dynamic_field_thong_so_toa_nha_hang-toa-nha', $class_id, $english_post_id);
                }
            } else {
                // Copy từ bản tiếng Việt
                $class_id = get_field('dynamic_field_thong_so_toa_nha_hang-toa-nha', $post_id);
                if ($class_id) {
                    update_field('dynamic_field_thong_so_toa_nha_hang-toa-nha', $class_id, $english_post_id);
                }
            }

            // Xử lý hướng tòa nhà (lấy post IDs từ post type building-orientation)
            if (isset($data['Hướng tòa nhà (EN)']) && !empty($data['Hướng tòa nhà (EN)'])) {
                // Phân tách các hướng nếu có nhiều
                $orientations = array_map('trim', explode(',', $data['Hướng tòa nhà (EN)']));
                $orientation_ids = array();

                foreach ($orientations as $orientation) {
                    $orientation_id = find_or_create_location_term($orientation, 'building-orientation');
                    if ($orientation_id) {
                        $orientation_ids[] = $orientation_id;
                    }
                }

                if (!empty($orientation_ids)) {
                    update_field('dynamic_field_thong_so_toa_nha_huong-toa-nha', $orientation_ids, $english_post_id);
                }
            } else {
                // Copy từ bản tiếng Việt
                $orientation_ids = get_field('dynamic_field_thong_so_toa_nha_huong-toa-nha', $post_id);
                if ($orientation_ids) {
                    update_field('dynamic_field_thong_so_toa_nha_huong-toa-nha', $orientation_ids, $english_post_id);
                }
            }

            // Xử lý VAT (nếu có)
            // Mặc định để trống hoặc lấy từ dữ liệu nếu có
            $vat_value = '10%';
            if (isset($data['VAT (EN)']) && !empty($data['VAT (EN)'])) {
                $vat_value = $data['VAT (EN)'];
            }
            update_field('dynamic_field_thong_tin_dien_tich_va_gia_thue_vat', $vat_value, $english_post_id);
        }
    }

    return $post_id;
}

/**
 * Xử lý chuỗi khoảng diện tích từ Excel và chuyển đổi thành ID của các post type area
 *
 * @param string $area_ranges Chuỗi khoảng diện tích từ Excel (ví dụ: "< 100m2, 100 - 200m2, 200 - 300m2, 300 - 500 m2")
 * @return array Mảng các ID của post type area
 */
function process_area_ranges($area_ranges, $lang = 'vi')
{
    // Nếu không có dữ liệu
    if (empty($area_ranges)) {
        return array();
    }

    // Chuyển đổi chuỗi thành mảng
    $ranges = array_map('trim', explode(',', $area_ranges));
    $area_ids = array();

    foreach ($ranges as $range) {
        // Bỏ qua phần tử rỗng
        if (empty($range)) {
            continue;
        }

        // Tìm post type area với tên tương ứng
        $area_id = find_or_create_location_term($range, 'area', $lang);
        if ($area_id) {
            $area_ids[] = $area_id;
        }
    }

    return $area_ids;
}

/**
 * Tìm hoặc tạo mới một term trong post type location
 *
 * @param string $name Tên địa điểm/khoảng diện tích cần tìm
 * @param string $post_type Post type location (city, district, ward, street, block, area)
 * @return int|null ID của post nếu thành công, null nếu thất bại
 */
function find_or_create_location_term($name, $post_type, $lang = 'vi')
{
    // Tránh các trường hợp tên không hợp lệ
    if (empty($name)) {
        return null;
    }

    global $sitepress;
    $current_lang = 'vi';
    $sitepress->switch_lang($lang); // Chuyển sang ngôn ngữ tiếng Anh

    // Chuẩn hóa tên để tìm kiếm chính xác hơn
    $name = trim($name);

    // Tìm post type đã tồn tại với tên này
    $args = array(
        'post_type' => $post_type,
        'post_status' => 'publish',
        'posts_per_page' => 1,
        // 'lang' => $lang,
        // 'suppress_filters' => false,
        'title' => $name,
        'exact' => true,
    );

    $query = new WP_Query($args);

    if ($query->have_posts()) {
        // Nếu đã tồn tại, trả về ID
        $post = $query->posts[0];
        return $post->ID;
    } else {
        // Nếu chưa tồn tại, tạo mới
        $new_post = array(
            'post_title' => $name,
            'post_type' => $post_type,
            'post_status' => 'publish',
        );

        $post_id = wp_insert_post($new_post);

        if (is_wp_error($post_id)) {
            return null;
        }

        return $post_id;
    }

    $sitepress->switch_lang($current_lang); // Khôi phục ngôn ngữ ban đầu
}

/**
 * Import nhiều ảnh từ danh sách URL và lưu vào gallery
 *
 * @param string $urls_string Danh sách URL phân cách bởi dấu phẩy
 * @param int $post_id ID của post liên quan
 * @return array Mảng IDs của các ảnh đã tải
 */
function import_gallery_images($urls_string, $post_id)
{
    require_once(ABSPATH . 'wp-admin/includes/media.php');
    require_once(ABSPATH . 'wp-admin/includes/file.php');
    require_once(ABSPATH . 'wp-admin/includes/image.php');

    // Tách chuỗi URL thành mảng
    $urls = array_map('trim', explode(',', $urls_string));
    $attachment_ids = [];

    foreach ($urls as $url) {
        if (empty($url)) continue;

        // Kiểm tra ảnh đã tồn tại chưa
        $existing_image_id = image_exists_in_media_library($url);

        if ($existing_image_id) {
            // Nếu đã tồn tại, thêm vào danh sách
            $attachment_ids[] = $existing_image_id;
            continue;
        }

        // Nếu chưa tồn tại, tải xuống file
        $tmp = download_url($url);

        if (is_wp_error($tmp)) {
            continue; // Bỏ qua URL này nếu tải thất bại
        }

        // Chuẩn bị file để upload
        $file_array = array(
            'name' => basename($url),
            'tmp_name' => $tmp
        );

        // Thực hiện upload
        $attachment_id = media_handle_sideload($file_array, $post_id);

        // Xóa file tạm nếu có lỗi
        if (is_wp_error($attachment_id)) {
            @unlink($tmp);
            continue;
        }

        // Thêm ID vào danh sách
        $attachment_ids[] = $attachment_id;
    }

    return $attachment_ids;
}

/**
 * Import ảnh từ URL nếu chưa tồn tại trong thư viện
 *
 * @param string $url URL của ảnh
 * @param int $post_id ID của post liên quan
 * @return int|false ID của attachment hoặc false nếu có lỗi
 */
function import_remote_image($url, $post_id)
{
    // Kiểm tra xem ảnh đã tồn tại chưa
    $existing_image_id = image_exists_in_media_library($url);

    if ($existing_image_id) {
        return $existing_image_id;
    }

    require_once(ABSPATH . 'wp-admin/includes/media.php');
    require_once(ABSPATH . 'wp-admin/includes/file.php');
    require_once(ABSPATH . 'wp-admin/includes/image.php');

    // Tải xuống file
    $tmp = download_url($url);

    if (is_wp_error($tmp)) {
        return false;
    }

    // Chuẩn bị file để upload
    $file_array = array(
        'name' => basename($url),
        'tmp_name' => $tmp
    );

    // Thực hiện upload
    $attachment_id = media_handle_sideload($file_array, $post_id);

    // Xóa file tạm nếu có lỗi
    if (is_wp_error($attachment_id)) {
        @unlink($tmp);
        return false;
    }

    return $attachment_id;
}


/**
 * Lấy slug từ dữ liệu Excel với ưu tiên: Đường dẫn (VI/EN) > URL web cũ
 *
 * @param array $data Dữ liệu từ Excel
 * @param string $lang Ngôn ngữ ('vi' hoặc 'en')
 * @return string|false Slug được tạo hoặc false nếu không có
 */
function get_slug_from_excel_data($data, $lang = 'vi')
{
    $slug = '';

    // Ưu tiên 1: Đường dẫn theo ngôn ngữ
    if ($lang === 'vi' && isset($data['Đường dẫn (VI)']) && !empty($data['Đường dẫn (VI)'])) {
        $slug = trim($data['Đường dẫn (VI)']);
    } elseif ($lang === 'en' && isset($data['Đường dẫn (EN)']) && !empty($data['Đường dẫn (EN)'])) {
        $slug = trim($data['Đường dẫn (EN)']);
    }

    // Ưu tiên 2: URL web cũ (chỉ khi không có đường dẫn theo ngôn ngữ)
    if (empty($slug) && isset($data['URL web cũ']) && !empty($data['URL web cũ'])) {
        $old_url = trim($data['URL web cũ']);
        $parsed_url = parse_url($old_url);
        if (isset($parsed_url['path'])) {
            $path = $parsed_url['path'];
            // Bỏ .html ở cuối
            $slug = preg_replace('/\.html$/', '', $path);
            // Bỏ dấu / ở đầu
            $slug = ltrim($slug, '/');

            // Nếu là tiếng Anh và dùng URL web cũ, thêm -en
            if ($lang === 'en' && !empty($slug)) {
                $slug = $slug . '-en';
            }
        }
    }

    // Xử lý slug: bỏ dấu / ở đầu và cuối
    if (!empty($slug)) {
        $slug = trim($slug, '/');
        return !empty($slug) ? $slug : false;
    }

    return false;
}

/**
 * Kiểm tra xem bài viết đã tồn tại hay chưa dựa trên slug trực tiếp
 *
 * @param string $slug Slug cần kiểm tra
 * @param string $post_type Loại bài viết cần kiểm tra
 * @return int|false ID của bài viết nếu tồn tại, false nếu không tồn tại
 */
function check_if_post_exists_by_direct_slug($slug, $post_type = 'office-rent')
{
    if (empty($slug)) {
        return false;
    }

    $query = new WP_Query(array(
        'post_type' => $post_type,
        'post_status' => 'publish',
        'name' => $slug,
        'posts_per_page' => 1,
        'fields' => 'ids'
    ));

    if ($query->have_posts()) {
        return $query->posts[0];
    }

    return false;
}

/**
 * Kiểm tra xem bài viết đã tồn tại hay chưa dựa trên slug từ URL web cũ
 *
 * @param string $old_url URL web cũ để extract slug
 * @param string $post_type Loại bài viết cần kiểm tra
 * @return int|false ID của bài viết nếu tồn tại, false nếu không tồn tại
 */
function check_if_post_exists_by_slug($old_url, $post_type = 'office-rent')
{
    if (empty($old_url)) {
        return false;
    }

    // Lấy slug từ URL web cũ
    $parsed_url = parse_url(trim($old_url));
    if (!isset($parsed_url['path'])) {
        return false;
    }

    $path = $parsed_url['path'];
    // Bỏ .html ở cuối
    $slug = preg_replace('/\.html$/', '', $path);
    // Bỏ dấu / ở đầu
    $slug = ltrim($slug, '/');

    if (empty($slug)) {
        return false;
    }

    $query = new WP_Query(array(
        'post_type' => $post_type,
        'post_status' => 'publish',
        'posts_per_page' => 1,
        'name' => $slug,
    ));

    if ($query->have_posts()) {
        return $query->posts[0]->ID;
    }

    return false;
}

/**
 * Kiểm tra xem bài viết đã tồn tại hay chưa dựa trên tiêu đề và post type
 *
 * @param string $title Tiêu đề bài viết cần kiểm tra
 * @param string $post_type Loại bài viết cần kiểm tra
 * @return int|false ID của bài viết nếu tồn tại, false nếu không tồn tại
 */
function check_if_post_exists($title, $post_type = 'office-rent')
{
    if (empty($title)) {
        return false;
    }

    $query = new WP_Query(array(
        'post_type' => $post_type,
        'post_status' => 'publish',
        'posts_per_page' => 1,
        'title' => $title,
        'exact' => true,
    ));

    if ($query->have_posts()) {
        return $query->posts[0]->ID;
    }

    return false;
}

/**
 * Kiểm tra xem bài viết đã tồn tại hay chưa dựa trên ID và post type
 *
 * @param int $post_id ID của bài viết cần kiểm tra
 * @param string $post_type Loại bài viết cần kiểm tra
 * @return int|false ID của bài viết nếu tồn tại, false nếu không tồn tại
 */
function check_if_post_exists_by_id($post_id, $post_type = 'office-rent')
{
    if (empty($post_id) || !is_numeric($post_id)) {
        return false;
    }

    $post = get_post($post_id);

    // Kiểm tra post có tồn tại và có đúng post_type không
    if ($post && $post->post_type === $post_type && $post->post_status !== 'trash') {
        return $post->ID;
    }

    return false;
}

/**
 * Kiểm tra xem bài viết đã tồn tại hay chưa dựa trên tiêu đề hoặc ID
 *
 * @param string|int $identifier Tiêu đề hoặc ID của bài viết cần kiểm tra
 * @param string $post_type Loại bài viết cần kiểm tra
 * @return int|false ID của bài viết nếu tồn tại, false nếu không tồn tại
 */
function check_office_post_exists($identifier, $post_type = 'office-rent')
{
    // Kiểm tra xem identifier có phải là ID không
    if (is_numeric($identifier)) {
        return check_if_post_exists_by_id($identifier, $post_type);
    }

    // Nếu không phải ID, xử lý như tiêu đề
    return check_if_post_exists($identifier, $post_type);
}


/**
 * Kiểm tra xem ảnh đã tồn tại trong thư viện hay chưa dựa trên tên file
 *
 * @param string $url URL của ảnh cần kiểm tra
 * @return int|false ID của ảnh nếu tồn tại, false nếu không tồn tại
 */
function image_exists_in_media_library($url)
{
    $filename = basename($url);

    // Loại bỏ tham số query (nếu có)
    $filename = preg_replace('/\?.*/', '', $filename);

    $args = array(
        'post_type' => 'attachment',
        'post_status' => 'inherit',
        'posts_per_page' => 1,
        'meta_query' => array(
            array(
                'value' => $filename,
                'compare' => 'LIKE',
            ),
        ),
    );

    $query = new WP_Query($args);

    if ($query->have_posts()) {
        return $query->posts[0]->ID;
    }

    return false;
}

/**
 * Chuyển đổi ngày dạng số của Excel sang định dạng ngày MySQL
 *
 * @param mixed $excel_date Số ngày Excel (ví dụ: 45749.132881944)
 * @return string Ngày định dạng MySQL
 */
function excel_date_to_mysql_date($excel_date)
{
    // Kiểm tra xem giá trị có phải là số không
    if (!is_numeric($excel_date)) {
        return current_time('mysql');
    }

    // Chuyển đổi từ số Excel sang timestamp Unix
    // Excel sử dụng 1/1/1900 làm ngày 1, với bug cố hữu coi 1900 là năm nhuận
    $unix_date = ($excel_date - 25569) * 86400; // 25569 là số ngày từ 1/1/1900 đến 1/1/1970

    // Chỉ lấy phần ngày (bỏ phần thời gian)
    $date_only = date('Y-m-d', $unix_date);

    // Kết hợp với thời gian hiện tại
    $current_time = date('H:i:s');

    return $date_only . ' ' . $current_time;
}


/**
 * Xử lý action export dữ liệu
 */
add_action('admin_post_export_office_data', 'handle_export_office_data');

function handle_export_office_data()
{
    // Kiểm tra nonce
    if (!isset($_POST['export_nonce']) || !wp_verify_nonce($_POST['export_nonce'], 'export_office_data_nonce')) {
        wp_die('Lỗi bảo mật!');
    }

    // Lấy số lượng bài viết cần export
    $limit = isset($_POST['export_limit']) ? intval($_POST['export_limit']) : 100;
    $limit = max(1, min(1000, $limit)); // Giới hạn từ 1-1000

    // Cài đặt thư viện PhpSpreadsheet nếu chưa có
    require_once(get_template_directory() . '/inc/lib/vendor/autoload.php');

    // Tạo workbook và worksheet mới
    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->setTitle('Office Data');

    // Định nghĩa các cột cho file Excel
    $columns = [
        'A' => 'ID',
        'B' => 'Tên văn phòng (VI)',
        'C' => 'Tên văn phòng (EN)',
        'D' => 'Đường dẫn',
        'E' => 'Ảnh đại diện',
        'F' => 'Thư viện ảnh',
        'G' => 'Ngày đăng',
        'H' => 'Ngày cập nhật',
        'I' => 'Giá tiền',
        'J' => 'Khoảng giá',
        'K' => 'Diện tích đất',
        'L' => 'Diện tích xây dựng',
        'M' => 'Phí quản lý',
        'N' => 'Hotline',
        'O' => 'Khoảng diện tích',
        'P' => 'Chứng chỉ',
        'Q' => 'Chủ đề 1',
        'R' => 'Chủ đề 2',
        'S' => 'Chủ đề 3',

        'T' => 'Địa chỉ (VI)',
        'U' => 'Địa chỉ (EN)',
        'V' => 'Địa chỉ quận',
        'W' => 'Địa chỉ phường',
        'X' => 'Địa chỉ đường',
        'Y' => 'Khu vực (VI)',
        'Z' => 'Khu vực (EN)',
        'AA' => 'Thương hiệu (VI)',
        'AB' => 'Thương hiệu (EN)',

        'AC' => 'Chủ đầu tư (VI)',
        'AD' => 'Chủ đầu tư (EN)',
        'AE' => 'Kết cấu cao ốc (VI)',
        'AF' => 'Kết cấu cao ốc (EN)',
        'AG' => 'Năm hoàn thành (VI)',
        'AH' => 'Năm hoàn thành (EN)',
        'AI' => 'Hạng tòa nhà (VI)',
        'AJ' => 'Hạng tòa nhà (EN)',
        'AK' => 'Thông số thang máy (VI)',
        'AL' => 'Thông số thang máy (EN)',
        'AM' => 'Độ cao sàn đến trần (VI)',
        'AN' => 'Độ cao sàn đến trần (EN)',
        'AO' => 'Hướng tòa nhà (VI)',
        'AP' => 'Hướng tòa nhà (EN)',
        'AQ' => 'Diện tích một sàn (VI)',
        'AR' => 'Diện tích một sàn (EN)',
        'AS' => 'Tổng diện tích sử dụng (VI)',
        'AT' => 'Tổng diện tích sử dụng (EN)',
        'AU' => 'Tiêu đề diện tích và cho thuê (VI)',
        'AV' => 'Tiêu đề diện tích và cho thuê (EN)',

        'AW' => 'Diện tích cho thuê (VI)',
        'AX' => 'Diện tích cho thuê (EN)',
        'AY' => 'Giá thuê (VI)',
        'AZ' => 'Giá thuê (EN)',
        'BA' => 'Phí quản lý (VI)',
        'BB' => 'Phí quản lý (EN)',
        'BC' => 'Tiền điện (VI)',
        'BD' => 'Tiền điện (EN)',
        'BE' => 'Phí gửi xe máy (VI)',
        'BF' => 'Phí gửi xe máy (EN)',
        'BG' => 'Phí gửi ô tô (VI)',
        'BH' => 'Phí gửi ô tô (EN)',
        'BI' => 'Phí làm việc ngoài giờ (VI)',
        'BJ' => 'Phí làm việc ngoài giờ (EN)',
        'BK' => 'VAT (VI)',
        'BL' => 'VAT (EN)',
        'BM' => 'Thời hạn thuê (VI)',
        'BN' => 'Thời hạn thuê (EN)',
        'BO' => 'Đặt cọc (VI)',
        'BP' => 'Đặt cọc (EN)',
        'BQ' => 'Thanh toán (VI)',
        'BR' => 'Thanh toán (EN)',
        'BS' => 'Tiêu đề bài viết giới thiệu (VI)',
        'BT' => 'Tiêu đề bài viết giới thiệu (EN)',
        'BU' => 'Nội dung bài viết giới thiệu (VI)',
        'BV' => 'Nội dung bài viết giới thiệu (EN)',

        'BW' => 'Tiêu đề tiện ích (VI)',
        'BX' => 'Tiêu đề tiện ích (EN)',
        'BY' => 'Nội dung tiện ích (VI)',
        'BZ' => 'Nội dung tiện ích (EN)',

        'CA' => 'Thang máy (VI)',
        'CB' => 'Thang máy (EN)',
        'CC' => 'Phòng cháy chữa cháy (VI)',
        'CD' => 'Phòng cháy chữa cháy (EN)',
        'CE' => 'Máy lạnh (VI)',
        'CF' => 'Máy lạnh (EN)',
        'CG' => 'Bãi xe máy (VI)',
        'CH' => 'Bãi xe máy (EN)',
        'CI' => 'Bãi xe ô tô (VI)',
        'CJ' => 'Bãi xe ô tô (EN)',
        'CK' => 'Đội ngũ quản lý vận hành (VI)',
        'CL' => 'Đội ngũ quản lý vận hành (EN)',
        'CM' => 'Điện dự phòng (VI)',
        'CN' => 'Điện dự phòng (EN)',
        'CO' => 'Dịch vụ hỗ trợ IT (VI)',
        'CP' => 'Dịch vụ hỗ trợ IT (EN)',
        'CQ' => 'Dịch vụ dọn dẹp vệ sinh (VI)',
        'CR' => 'Dịch vụ dọn dẹp vệ sinh (EN)',
        'CS' => 'Đăng ký địa chỉ kinh doanh (VI)',
        'CT' => 'Đăng ký địa chỉ kinh doanh (EN)',

        'CU' => 'Tiêu đề vị trí (VI)',
        'CV' => 'Tiêu đề vị trí (EN)',
        'CW' => 'Tọa độ (VI)',
        'CX' => 'Tọa độ (EN)',
        'CY' => 'Google map (VI)',
        'CZ' => 'Google map (EN)',
        'DA' => 'Google Street View 360 (VI)',
        'DB' => 'Google Street View 360 (EN)',

        'DC' => 'Tiêu đề bài viết chi tiết (VI)',
        'DD' => 'Tiêu đề bài viết chi tiết (EN)',
        'DE' => 'Nội dung bài viết chi tiết (VI)',
        'DF' => 'Nội dung bài viết chi tiết (EN)',
    ];

    // Thêm tiêu đề cột vào hàng đầu tiên
    foreach ($columns as $col => $title) {
        $sheet->setCellValue($col . '1', $title);
        // Định dạng tiêu đề cột
        $sheet->getStyle($col . '1')->getFont()->setBold(true);
        $sheet->getStyle($col . '1')->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()->setARGB('FFD9EAD3');
    }

    // Truy vấn lấy dữ liệu từ bài viết
    $args = [
        'post_type' => 'office-rent',
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'orderby' => 'date',
        'order' => 'DESC',
    ];

    $query = new WP_Query($args);
    $row = 2; // Bắt đầu từ dòng 2 (sau tiêu đề)

    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            $post_id = get_the_ID();

            $gallery = get_field('gallery', $post_id);
            $gallery_urls = '';
            if (!empty($gallery)) {
                $urls = array();
                // error_log('Gallery for post ID ' . $post_id . ': ' . print_r($gallery, true));
                foreach ($gallery as $image) {
                    $image_id = $image['ID'];
                    $image_url = wp_get_attachment_url($image_id);
                    // error_log('Image ID: ' . $image_id . ', URL: ' . $image_url);
                    if ($image_url) {
                        $urls[] = $image_url;
                    }
                }
                $gallery_urls = implode(', ', $urls);
                // error_log('Final gallery URLs: ' . $gallery_urls);
            } else {
                // error_log('No gallery found for post ID ' . $post_id);
            }

            // Lấy các giá trị ACF và meta fields
            $hotline = get_field('hotline', $post_id);
            $toa_do = get_field('toa_do', $post_id);
            $google_street_view = get_field('google_street_view', $post_id);
            $price_product = get_field('price_product', $post_id);
            $price_management = get_field('price_management', $post_id);

            // Lấy các giá trị thông số tòa nhà
            $title_parameter = get_field('title_parameter', $post_id);
            $chu_dau_tu = get_field('dynamic_field_thong_so_toa_nha_chu-dau-tu', $post_id);
            $ket_cau = get_field('dynamic_field_thong_so_toa_nha_ket-cau-cao-oc', $post_id);
            $nam_hoan_thanh = get_field('dynamic_field_thong_so_toa_nha_nam-hoan-thanh', $post_id);
            $thong_so_thang_may = get_field('dynamic_field_thong_so_toa_nha_thang-may', $post_id);
            $do_cao_san = get_field('dynamic_field_thong_so_toa_nha_do-cao-san-den-tran', $post_id);
            $dien_tich_san = get_field('dynamic_field_thong_so_toa_nha_dien-tich-san-dien-hinhm2', $post_id);
            $tong_dien_tich = get_field('dynamic_field_thong_so_toa_nha_tong-dien-tich-su-dung', $post_id);

            // Lấy thông tin diện tích và giá thuê
            $dien_tich_trong = get_field('dynamic_field_thong_tin_dien_tich_va_gia_thue_dien-tich-trong', $post_id);
            $gia_thue = get_field('dynamic_field_thong_tin_dien_tich_va_gia_thue_gia-thue', $post_id);
            $phi_quan_ly = get_field('dynamic_field_thong_tin_dien_tich_va_gia_thue_phi-quan-ly', $post_id);
            $tien_dien = get_field('dynamic_field_thong_tin_dien_tich_va_gia_thue_tien-dien', $post_id);
            $phi_xe_may = get_field('dynamic_field_thong_tin_dien_tich_va_gia_thue_phi-gui-xe-may', $post_id);
            $phi_o_to = get_field('dynamic_field_thong_tin_dien_tich_va_gia_thue_phi-gui-o-to', $post_id);
            $phi_ngoai_gio = get_field('dynamic_field_thong_tin_dien_tich_va_gia_thue_phi-lam-viec-ngoai-gio', $post_id);
            $thoi_han_thue = get_field('dynamic_field_thong_tin_dien_tich_va_gia_thue_thoi-han-thue', $post_id);
            $dat_coc = get_field('dynamic_field_thong_tin_dien_tich_va_gia_thue_dat-coc', $post_id);
            $thanh_toan = get_field('dynamic_field_thong_tin_dien_tich_va_gia_thue_thanh-toan', $post_id);
            $vat = get_field('dynamic_field_thong_tin_dien_tich_va_gia_thue_vat', $post_id);

            // Lấy tiện ích tòa nhà
            $thang_may = get_field('dynamic_field_tien_ich_toa_nha_thang-may', $post_id);
            $pccc = get_field('dynamic_field_tien_ich_toa_nha_phong-chay-chua-chay', $post_id);
            $may_lanh = get_field('dynamic_field_tien_ich_toa_nha_may-lanh', $post_id);
            $bai_xe_may = get_field('dynamic_field_tien_ich_toa_nha_bai-xe-may', $post_id);
            $bai_o_to = get_field('dynamic_field_tien_ich_toa_nha_bai-xe-o-to', $post_id);
            $quan_ly = get_field('dynamic_field_tien_ich_toa_nha_doi-ngu-quan-ly-van-hanh', $post_id);
            $dien_du_phong = get_field('dynamic_field_tien_ich_toa_nha_dien-du-phong', $post_id);
            $ho_tro_it = get_field('dynamic_field_tien_ich_toa_nha_dich-vu-ho-tro-it', $post_id);
            $ve_sinh = get_field('dynamic_field_tien_ich_toa_nha_dich-vu-don-dep-ve-sinh', $post_id);
            $dang_ky_dia_chi = get_field('dynamic_field_tien_ich_toa_nha_dang-ky-dia-chi-kinh-doanh-nhanh-chong-de-dang', $post_id);


            // Lấy bài viết giới thiệu và chi tiết
            $nd_bai_viet_gioi_thieu = get_field('nd_bai_viet_gioi_thieu', $post_id);
            $nd_bai_viet_chi_tiet = get_field('nd_bai_viet_chi_tiet', $post_id);

            // Lấy hình ảnh chính và gallery
            $thumbnail_id = get_post_thumbnail_id($post_id);
            $thumbnail_url = $thumbnail_id ? wp_get_attachment_url($thumbnail_id) : '';


            $title_utilities = get_field('title_utilities', $post_id);
            $content_utilities = get_field('content_utilities', $post_id);

            // Lấy thông tin vị trí
            $district_id = get_post_meta($post_id, '_office_district', true);
            $district_name = $district_id ? get_the_title($district_id) : '';

            $ward_id = get_post_meta($post_id, '_office_ward', true);
            $ward_name = $ward_id ? get_the_title($ward_id) : '';

            $street_id = get_post_meta($post_id, '_office_street', true);
            $street_name = $street_id ? get_the_title($street_id) : '';

            $house_number = get_post_meta($post_id, '_office_house_number', true);

            // Ghép địa chỉ đầy đủ
            $full_address = $house_number;
            if (!empty($street_name)) $full_address .= ', ' . $street_name;
            if (!empty($ward_name)) $full_address .= ', ' . $ward_name;
            if (!empty($district_name)) $full_address .= ', ' . $district_name;

            // Lấy khu vực và thương hiệu
            $block_id = get_post_meta($post_id, '_office_block', true);
            $block_name = $block_id ? get_the_title($block_id) : '';

            $brand_id = get_post_meta($post_id, '_office_brand', true);
            $brand_name = $brand_id ? get_the_title($brand_id) : '';

            // Lấy tên khác/thương hiệu phụ
            $ten_khac = get_post_meta($post_id, 'ten_khac_vi', true);

            // Lấy hạng tòa nhà và hướng
            $hang_toa_nha_id = get_field('dynamic_field_thong_so_toa_nha_hang-toa-nha', $post_id);
            $hang_toa_nha = $hang_toa_nha_id ? get_the_title($hang_toa_nha_id) : '';

            $huong_toa_nha_ids = get_field('dynamic_field_thong_so_toa_nha_huong-toa-nha', $post_id);
            $huong_toa_nha = '';
            if (!empty($huong_toa_nha_ids) && is_array($huong_toa_nha_ids)) {
                $huong_names = [];
                foreach ($huong_toa_nha_ids as $id) {
                    $huong_names[] = get_the_title($id);
                }
                $huong_toa_nha = implode(', ', $huong_names);
            }

            // Lấy khoảng diện tích (xử lý để đảm bảo hiển thị đúng)
            $area_ids = get_post_meta($post_id, 'khoang_dien_tich', true);
            $area_ranges = '';
            if (!empty($area_ids) && is_array($area_ids)) {
                $area_names = [];
                foreach ($area_ids as $id) {
                    $name = get_the_title($id);
                    // Thay thế ký tự đặc biệt
                    $name = str_replace('&#8211;', '-', $name);
                    $name = str_replace('&lt;', '<', $name);
                    $name = str_replace('&gt;', '>', $name);
                    $area_names[] = $name;
                }
                $area_ranges = implode(', ', $area_names);
            }

            // Lấy chủ đề
            $chu_de_1_id = get_field('chu_de_1', $post_id);
            $chu_de_2_id = get_field('chu_de_2', $post_id);
            $chu_de_3_id = get_field('chu_de_3', $post_id);

            $chu_de_1 = $chu_de_1_id ? get_the_title($chu_de_1_id) : '';
            $chu_de_2 = $chu_de_2_id ? get_the_title($chu_de_2_id) : '';
            $chu_de_3 = $chu_de_3_id ? get_the_title($chu_de_3_id) : '';

            // Thêm dữ liệu vào excel
            $sheet->setCellValue('A' . $row, $post_id);
            $sheet->setCellValue('B' . $row, get_the_title());
            $sheet->setCellValue('C' . $row, get_the_title()); // Tên văn phòng (EN)
            $sheet->setCellValue('D' . $row, get_permalink($post_id));
            $sheet->setCellValue('E' . $row, $thumbnail_url);
            $sheet->setCellValue('F' . $row, $gallery_urls);
            $sheet->setCellValue('G' . $row, get_field('ngay_gui', $post_id) ? get_field('ngay_gui', $post_id) : get_the_date('Y-m-d H:i:s'));
            $sheet->setCellValue('H' . $row, get_field('ngay_cap_nhat', $post_id) ? get_field('ngay_cap_nhat', $post_id) : get_the_modified_date('Y-m-d H:i:s'));
            $sheet->setCellValue('I' . $row, $price_product);
            $sheet->setCellValue('J' . $row, get_field('price_range', $post_id)); // Khoảng giá
            $sheet->setCellValue('K' . $row, get_field('land_area', $post_id)); // Diện tích đất
            $sheet->setCellValue('L' . $row, get_field('construction_area', $post_id)); // Diện tích xây dựng
            $sheet->setCellValue('M' . $row, $price_management);
            $sheet->setCellValue('N' . $row, $hotline);
            $sheet->setCellValue('O' . $row, html_entity_decode($area_ranges));

            // Lấy chứng chỉ từ ACF
            $chung_chi_id = get_field('chung_chi', $post_id);
            $chung_chi = $chung_chi_id ? get_the_title($chung_chi_id) : '';
            $sheet->setCellValue('P' . $row, $chung_chi);

            $sheet->setCellValue('Q' . $row, $chu_de_1);
            $sheet->setCellValue('R' . $row, $chu_de_2);
            $sheet->setCellValue('S' . $row, $chu_de_3);

            $sheet->setCellValue('T' . $row, $full_address);
            $sheet->setCellValue('U' . $row, $full_address); // Địa chỉ (EN)
            $sheet->setCellValue('V' . $row, $district_name);
            $sheet->setCellValue('W' . $row, $ward_name);
            $sheet->setCellValue('X' . $row, $street_name);
            $sheet->setCellValue('Y' . $row, $block_name);
            $sheet->setCellValue('Z' . $row, $block_name); // Khu vực (EN)
            $sheet->setCellValue('AA' . $row, $brand_name);
            $sheet->setCellValue('AB' . $row, $brand_name); // Thương hiệu (EN)

            $sheet->setCellValue('AC' . $row, $chu_dau_tu);
            $sheet->setCellValue('AD' . $row, $chu_dau_tu); // Chủ đầu tư (EN)
            $sheet->setCellValue('AE' . $row, $ket_cau);
            $sheet->setCellValue('AF' . $row, $ket_cau); // Kết cấu cao ốc (EN)
            $sheet->setCellValue('AG' . $row, $nam_hoan_thanh);
            $sheet->setCellValue('AH' . $row, $nam_hoan_thanh); // Năm hoàn thành (EN)
            $sheet->setCellValue('AI' . $row, $hang_toa_nha);
            $sheet->setCellValue('AJ' . $row, $hang_toa_nha); // Hạng tòa nhà (EN)
            $sheet->setCellValue('AK' . $row, $thong_so_thang_may);
            $sheet->setCellValue('AL' . $row, $thong_so_thang_may); // Thông số thang máy (EN)
            $sheet->setCellValue('AM' . $row, $do_cao_san);
            $sheet->setCellValue('AN' . $row, $do_cao_san); // Độ cao sàn đến trần (EN)
            $sheet->setCellValue('AO' . $row, $huong_toa_nha);
            $sheet->setCellValue('AP' . $row, $huong_toa_nha); // Hướng tòa nhà (EN)
            $sheet->setCellValue('AQ' . $row, $dien_tich_san);
            $sheet->setCellValue('AR' . $row, $dien_tich_san); // Diện tích một sàn (EN)
            $sheet->setCellValue('AS' . $row, $tong_dien_tich);
            $sheet->setCellValue('AT' . $row, $tong_dien_tich); // Tổng diện tích sử dụng (EN)
            $sheet->setCellValue('AU' . $row, $title_parameter);
            $sheet->setCellValue('AV' . $row, $title_parameter); // Tiêu đề diện tích và cho thuê (EN)

            $sheet->setCellValue('AW' . $row, $dien_tich_trong);
            $sheet->setCellValue('AX' . $row, $dien_tich_trong); // Diện tích cho thuê (EN)
            $sheet->setCellValue('AY' . $row, $gia_thue);
            $sheet->setCellValue('AZ' . $row, $gia_thue); // Giá thuê (EN)
            $sheet->setCellValue('BA' . $row, $phi_quan_ly);
            $sheet->setCellValue('BB' . $row, $phi_quan_ly); // Phí quản lý (EN)
            $sheet->setCellValue('BC' . $row, $tien_dien);
            $sheet->setCellValue('BD' . $row, $tien_dien); // Tiền điện (EN)
            $sheet->setCellValue('BE' . $row, $phi_xe_may);
            $sheet->setCellValue('BF' . $row, $phi_xe_may); // Phí gửi xe máy (EN)
            $sheet->setCellValue('BG' . $row, $phi_o_to);
            $sheet->setCellValue('BH' . $row, $phi_o_to); // Phí gửi ô tô (EN)
            $sheet->setCellValue('BI' . $row, $phi_ngoai_gio);
            $sheet->setCellValue('BJ' . $row, $phi_ngoai_gio); // Phí làm việc ngoài giờ (EN)
            $sheet->setCellValue('BK' . $row, $vat);
            $sheet->setCellValue('BL' . $row, $vat); // VAT (EN)
            $sheet->setCellValue('BM' . $row, $thoi_han_thue);
            $sheet->setCellValue('BN' . $row, $thoi_han_thue); // Thời hạn thuê (EN)
            $sheet->setCellValue('BO' . $row, $dat_coc);
            $sheet->setCellValue('BP' . $row, $dat_coc); // Đặt cọc (EN)
            $sheet->setCellValue('BQ' . $row, $thanh_toan);
            $sheet->setCellValue('BR' . $row, $thanh_toan); // Thanh toán (EN)
            $sheet->setCellValue('BS' . $row, get_field('td_bai_viet_gioi_thieu', $post_id));
            $sheet->setCellValue('BT' . $row, get_field('td_bai_viet_gioi_thieu', $post_id)); // Tiêu đề bài viết giới thiệu (EN)
            $sheet->setCellValue('BU' . $row, $nd_bai_viet_gioi_thieu);
            $sheet->setCellValue('BV' . $row, $nd_bai_viet_gioi_thieu); // Nội dung bài viết giới thiệu (EN)

            $sheet->setCellValue('BW' . $row, $title_utilities);
            $sheet->setCellValue('BX' . $row, $title_utilities); // Tiêu đề tiện ích (EN)
            $sheet->setCellValue('BY' . $row, $content_utilities);
            $sheet->setCellValue('BZ' . $row, $content_utilities); // Nội dung tiện ích (EN)

            $sheet->setCellValue('CA' . $row, $thang_may);
            $sheet->setCellValue('CB' . $row, $thang_may); // Thang máy (EN)
            $sheet->setCellValue('CC' . $row, $pccc);
            $sheet->setCellValue('CD' . $row, $pccc); // Phòng cháy chữa cháy (EN)
            $sheet->setCellValue('CE' . $row, $may_lanh);
            $sheet->setCellValue('CF' . $row, $may_lanh); // Máy lạnh (EN)
            $sheet->setCellValue('CG' . $row, $bai_xe_may);
            $sheet->setCellValue('CH' . $row, $bai_xe_may); // Bãi xe máy (EN)
            $sheet->setCellValue('CI' . $row, $bai_o_to);
            $sheet->setCellValue('CJ' . $row, $bai_o_to); // Bãi xe ô tô (EN)
            $sheet->setCellValue('CK' . $row, $quan_ly);
            $sheet->setCellValue('CL' . $row, $quan_ly); // Đội ngũ quản lý vận hành (EN)
            $sheet->setCellValue('CM' . $row, $dien_du_phong);
            $sheet->setCellValue('CN' . $row, $dien_du_phong); // Điện dự phòng (EN)
            $sheet->setCellValue('CO' . $row, $ho_tro_it);
            $sheet->setCellValue('CP' . $row, $ho_tro_it); // Dịch vụ hỗ trợ IT (EN)
            $sheet->setCellValue('CQ' . $row, $ve_sinh);
            $sheet->setCellValue('CR' . $row, $ve_sinh); // Dịch vụ dọn dẹp vệ sinh (EN)
            $sheet->setCellValue('CS' . $row, $dang_ky_dia_chi);
            $sheet->setCellValue('CT' . $row, $dang_ky_dia_chi); // Đăng ký địa chỉ kinh doanh (EN)

            $sheet->setCellValue('CU' . $row, get_field('title_location', $post_id));
            $sheet->setCellValue('CV' . $row, get_field('title_location', $post_id)); // Tiêu đề vị trí (EN)
            $sheet->setCellValue('CW' . $row, $toa_do);
            $sheet->setCellValue('CX' . $row, $toa_do); // Tọa độ (EN)
            $sheet->setCellValue('CY' . $row, get_field('google_map', $post_id));
            $sheet->setCellValue('CZ' . $row, get_field('google_map', $post_id)); // Google map (EN)
            $sheet->setCellValue('DA' . $row, $google_street_view);
            $sheet->setCellValue('DB' . $row, $google_street_view); // Google Street View 360 (EN)

            $sheet->setCellValue('DC' . $row, get_field('td_bai_viet_chi_tiet', $post_id));
            $sheet->setCellValue('DD' . $row, get_field('td_bai_viet_chi_tiet', $post_id)); // Tiêu đề bài viết chi tiết (EN)
            $sheet->setCellValue('DE' . $row, $nd_bai_viet_chi_tiet);
            $sheet->setCellValue('DF' . $row, $nd_bai_viet_chi_tiet); // Nội dung bài viết chi tiết (EN)



            $row++;
        }

        wp_reset_postdata();
    }

    // Auto-size các cột để fit nội dung
    foreach (range('A', 'Z') as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }

    // Auto-size các cột AA-DF
    foreach (range('AA', 'DF') as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }

    // Áp dụng filter và border cho toàn bộ bảng
    $lastRow = $row - 1;
    $range = 'A1:DF' . $lastRow;

    $styleArray = [
        'borders' => [
            'allBorders' => [
                'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                'color' => ['argb' => 'FF000000'],
            ],
        ],
    ];
    $sheet->getStyle($range)->applyFromArray($styleArray);

    // Cập nhật phần định dạng nội dung - áp dụng riêng biệt cho từng phạm vi
    $sheet->getStyle('BQ2:BR' . $lastRow)->getAlignment()->setWrapText(true);
    $sheet->getStyle('BU2:BV' . $lastRow)->getAlignment()->setWrapText(true);
    $sheet->getStyle('BY2:BZ' . $lastRow)->getAlignment()->setWrapText(true);
    $sheet->getStyle('DE2:DF' . $lastRow)->getAlignment()->setWrapText(true);

    // Tạo writer và output file
    $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
    $filename = 'office-data-export-' . date('Y-m-d') . '.xlsx';

    // Set headers để download file
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $filename . '"');
    header('Cache-Control: max-age=0');

    // Output file
    $writer->save('php://output');
    exit;
}


add_action('admin_post_export_office_data_full', 'handle_export_office_data_full');
function handle_export_office_data_full()
{
    // Kiểm tra nonce
    if (!isset($_POST['export_full_nonce']) || !wp_verify_nonce($_POST['export_full_nonce'], 'export_office_data_full_nonce')) {
        wp_die('Lỗi bảo mật!');
    }

    // Lấy số lượng bài viết cần export
    $limit = isset($_POST['export_limit']) ? intval($_POST['export_limit']) : 100;
    $limit = max(1, min(1000, $limit)); // Giới hạn từ 1-1000

    // Cài đặt thư viện PhpSpreadsheet
    require_once(get_template_directory() . '/inc/lib/vendor/autoload.php');

    // Tạo workbook và worksheet mới
    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->setTitle('Office Data Full');

    // Hàm chuyển đổi mọi dữ liệu thành string an toàn cho excel
    function safe_for_excel($value)
    {
        if (is_array($value) || is_object($value)) {
            return json_encode($value, JSON_UNESCAPED_UNICODE);
        }
        return $value;
    }

    // Định nghĩa các cột cho file Excel dựa trên ACF
    $columns = [
        'A' => 'ID',
        'B' => 'Tên văn phòng (VI)',
        'C' => 'Tên văn phòng (EN)',
        'D' => 'Đường dẫn',
        'E' => 'Ảnh đại diện',
        'F' => 'Thư viện ảnh',
        'G' => 'Ngày đăng',
        'H' => 'Ngày cập nhật',
        'I' => 'Giá tiền',
        'J' => 'Khoảng giá',
        'K' => 'Diện tích đất',
        'L' => 'Diện tích xây dựng',
        'M' => 'Phí quản lý',
        'N' => 'Hotline',
        'O' => 'Khoảng diện tích',
        'P' => 'Chứng chỉ',
        'Q' => 'Chủ đề 1',
        'R' => 'Chủ đề 2',
        'S' => 'Chủ đề 3',
        'T' => 'Địa chỉ (VI)',
        'U' => 'Địa chỉ (EN)',
        'V' => 'Địa chỉ quận',
        'W' => 'Địa chỉ phường',
        'X' => 'Địa chỉ đường',
        'Y' => 'Khu vực (VI)',
        'Z' => 'Khu vực (EN)',
        'AA' => 'Thương hiệu (VI)',
        'AB' => 'Thương hiệu (EN)',

        // Thông số tòa nhà
        'AC' => 'Tiêu đề thông số tòa nhà (VI)',
        'AD' => 'Tiêu đề thông số tòa nhà (EN)',
        'AE' => 'Tên tòa nhà (VI)',
        'AF' => 'Tên tòa nhà (EN)',
        'AG' => 'Quy mô (VI)',
        'AH' => 'Quy mô (EN)',
        'AI' => 'Vị trí (VI)',
        'AJ' => 'Vị trí (EN)',
        'AK' => 'Hạng tòa nhà (VI)',
        'AL' => 'Hạng tòa nhà (EN)',
        'AM' => 'Thang máy (VI)',
        'AN' => 'Thang máy (EN)',
        'AO' => 'Hướng tòa nhà (VI)',
        'AP' => 'Hướng tòa nhà (EN)',

        // Dịch vụ tòa nhà
        'AQ' => 'Tiêu đề thông tin (VI)',
        'AR' => 'Tiêu đề thông tin (EN)',
        'AS' => 'Tầng (VI)',
        'AT' => 'Tầng (EN)',
        'AU' => 'Loại hình (VI)',
        'AV' => 'Loại hình (EN)',
        'AW' => 'Phòng làm việc riêng (VI)',
        'AX' => 'Phòng làm việc riêng (EN)',
        'AY' => 'Số chỗ ngồi (VI)',
        'AZ' => 'Số chỗ ngồi (EN)',
        'BA' => 'Giá thuê (VI)',
        'BB' => 'Giá thuê (EN)',
        'BC' => 'Phí gửi xe máy (VI)',
        'BD' => 'Phí gửi xe máy (EN)',
        'BE' => 'Phí gửi ô tô (VI)',
        'BF' => 'Phí gửi ô tô (EN)',
        'BG' => 'Thời gian thuê (VI)',
        'BH' => 'Thời gian thuê (EN)',
        'BI' => 'Đặt cọc (VI)',
        'BJ' => 'Đặt cọc (EN)',
        'BK' => 'Thanh toán (VI)',
        'BL' => 'Thanh toán (EN)',

        // Bài viết giới thiệu
        'BM' => 'Tiêu đề bài viết giới thiệu (VI)',
        'BN' => 'Tiêu đề bài viết giới thiệu (EN)',
        'BO' => 'Nội dung bài viết giới thiệu (VI)',
        'BP' => 'Nội dung bài viết giới thiệu (EN)',

        // Tiện ích
        'BQ' => 'Tiêu đề tiện ích (VI)',
        'BR' => 'Tiêu đề tiện ích (EN)',
        'BS' => 'Điện nước (VI)',
        'BT' => 'Điện nước (EN)',
        'BU' => 'Địa chỉ đăng ký kinh doanh (VI)',
        'BV' => 'Địa chỉ đăng ký kinh doanh (EN)',
        'BW' => 'Dịch vụ lễ tân (VI)',
        'BX' => 'Dịch vụ lễ tân (EN)',
        'BY' => 'Hỗ trợ IT (VI)',
        'BZ' => 'Hỗ trợ IT (EN)',
        'CA' => 'Hệ thống máy lạnh trung tâm (VI)',
        'CB' => 'Hệ thống máy lạnh trung tâm (EN)',
        'CC' => 'Dịch vụ vệ sinh (VI)',
        'CD' => 'Dịch vụ vệ sinh (EN)',
        'CE' => 'Điện thoại và dịch vụ xử lý cuộc gọi (VI)',
        'CF' => 'Điện thoại và dịch vụ xử lý cuộc gọi (EN)',
        'CG' => 'Khu vực pantry (VI)',
        'CH' => 'Khu vực pantry (EN)',
        'CI' => 'Phone booth (VI)',
        'CJ' => 'Phone booth (EN)',
        'CK' => 'In ấn photocopy (VI)',
        'CL' => 'In ấn photocopy (EN)',

        // Bài viết chi tiết
        'CM' => 'Tiêu đề bài viết chi tiết (VI)',
        'CN' => 'Tiêu đề bài viết chi tiết (EN)',
        'CO' => 'Nội dung bài viết chi tiết (VI)',
        'CP' => 'Nội dung bài viết chi tiết (EN)',

        // Vị trí
        'CQ' => 'Tiêu đề vị trí (VI)',
        'CR' => 'Tiêu đề vị trí (EN)',
        'CS' => 'Tọa độ (VI)',
        'CT' => 'Tọa độ (EN)',
        'CU' => 'Google map (VI)',
        'CV' => 'Google map (EN)',
        'CW' => 'Google street view (VI)',
        'CX' => 'Google street view (EN)',
    ];

    // Thêm tiêu đề cột vào hàng đầu tiên
    foreach ($columns as $col => $title) {
        $sheet->setCellValue($col . '1', $title);
        // Định dạng tiêu đề cột
        $sheet->getStyle($col . '1')->getFont()->setBold(true);
        $sheet->getStyle($col . '1')->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()->setARGB('FFD9EAD3');
    }

    // Truy vấn lấy dữ liệu từ bài viết
    $args = [
        'post_type' => 'office-rent-full',
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'orderby' => 'date',
        'order' => 'DESC',
    ];

    $query = new WP_Query($args);
    $row = 2; // Bắt đầu từ dòng 2 (sau tiêu đề)

    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            $post_id = get_the_ID();

            // Lấy thumbnail
            $thumbnail_id = get_post_thumbnail_id($post_id);
            $thumbnail_url = $thumbnail_id ? wp_get_attachment_url($thumbnail_id) : '';

            // Lấy gallery
            $gallery = get_field('gallery', $post_id);
            $gallery_urls = '';
            if (!empty($gallery)) {
                $urls = array();
                foreach ($gallery as $image) {
                    if (isset($image['url'])) {
                        $urls[] = $image['url'];
                    } else if (isset($image['ID'])) {
                        $image_url = wp_get_attachment_url($image['ID']);
                        if ($image_url) $urls[] = $image_url;
                    }
                }
                $gallery_urls = implode(', ', $urls);
            }

            // Lấy khoảng diện tích
            $area_ids = get_field('khoang_dien_tich', $post_id);
            $area_ranges = '';
            if (!empty($area_ids) && is_array($area_ids)) {
                $area_names = [];
                foreach ($area_ids as $id) {
                    $name = get_the_title($id);
                    // Thay thế ký tự đặc biệt
                    $name = str_replace('&#8211;', '-', $name);
                    $name = str_replace('&lt;', '<', $name);
                    $name = str_replace('&gt;', '>', $name);
                    $area_names[] = $name;
                }
                $area_ranges = implode(', ', $area_names);
            }

            // Lấy chứng chỉ
            $chung_chi_id = get_field('chung_chi', $post_id);
            $chung_chi = $chung_chi_id ? get_the_title($chung_chi_id) : '';

            // Lấy chủ đề
            $chu_de_1_id = get_field('chu_de_1', $post_id);
            $chu_de_2_id = get_field('chu_de_2', $post_id);
            $chu_de_3_id = get_field('chu_de_3', $post_id);

            $chu_de_1 = $chu_de_1_id ? get_the_title($chu_de_1_id) : '';
            $chu_de_2 = $chu_de_2_id ? get_the_title($chu_de_2_id) : '';
            $chu_de_3 = $chu_de_3_id ? get_the_title($chu_de_3_id) : '';

            // Lấy thông tin vị trí
            $district_id = get_post_meta($post_id, '_office_district', true);
            $district_name = $district_id ? get_the_title($district_id) : '';

            $ward_id = get_post_meta($post_id, '_office_ward', true);
            $ward_name = $ward_id ? get_the_title($ward_id) : '';

            $street_id = get_post_meta($post_id, '_office_street', true);
            $street_name = $street_id ? get_the_title($street_id) : '';

            $house_number = get_post_meta($post_id, '_office_house_number', true);

            // Ghép địa chỉ đầy đủ
            $full_address = $house_number;
            if (!empty($street_name)) $full_address .= ', ' . $street_name;
            if (!empty($ward_name)) $full_address .= ', ' . $ward_name;
            if (!empty($district_name)) $full_address .= ', ' . $district_name;

            // Lấy khu vực và thương hiệu
            $block_id = get_post_meta($post_id, '_office_block', true);
            $block_name = $block_id ? get_the_title($block_id) : '';

            $brand_id = get_post_meta($post_id, '_office_brand', true);
            $brand_name = $brand_id ? get_the_title($brand_id) : '';

            // Xử lý hạng tòa nhà
            $hang_toa_nha_id = get_field('dynamic_field_ts_toa_nha_tron_goi_hang-toa-nha', $post_id);
            $hang_toa_nha = '';
            if (!empty($hang_toa_nha_id)) {
                if (is_array($hang_toa_nha_id)) {
                    $hang_names = [];
                    foreach ($hang_toa_nha_id as $id) {
                        $hang_names[] = get_the_title($id);
                    }
                    $hang_toa_nha = implode(', ', $hang_names);
                } else {
                    $hang_toa_nha = get_the_title($hang_toa_nha_id);
                }
            }

            // Xử lý hướng tòa nhà
            $huong_toa_nha_ids = get_field('dynamic_field_ts_toa_nha_tron_goi_huong-toa-nha', $post_id);
            $huong_toa_nha = '';
            if (!empty($huong_toa_nha_ids) && is_array($huong_toa_nha_ids)) {
                $huong_names = [];
                foreach ($huong_toa_nha_ids as $id) {
                    $huong_names[] = get_the_title($id);
                }
                $huong_toa_nha = implode(', ', $huong_names);
            } elseif (!empty($huong_toa_nha_ids)) {
                $huong_toa_nha = get_the_title($huong_toa_nha_ids);
            }

            // Thêm dữ liệu vào excel - Basic info
            $sheet->setCellValue('A' . $row, $post_id);
            $sheet->setCellValue('B' . $row, get_the_title());
            $sheet->setCellValue('C' . $row, ''); // Tên văn phòng (EN) - để trống
            $sheet->setCellValue('D' . $row, get_permalink($post_id));
            $sheet->setCellValue('E' . $row, $thumbnail_url);
            $sheet->setCellValue('F' . $row, $gallery_urls);
            $sheet->setCellValue('G' . $row, get_field('ngay_gui', $post_id) ? get_field('ngay_gui', $post_id) : get_the_date('Y-m-d H:i:s'));
            $sheet->setCellValue('H' . $row, get_field('ngay_cap_nhat', $post_id) ? get_field('ngay_cap_nhat', $post_id) : get_the_modified_date('Y-m-d H:i:s'));
            $sheet->setCellValue('I' . $row, safe_for_excel(get_field('price_product', $post_id)));
            $sheet->setCellValue('J' . $row, safe_for_excel(get_field('price_range', $post_id)));
            $sheet->setCellValue('K' . $row, safe_for_excel(get_field('land_area', $post_id)));
            $sheet->setCellValue('L' . $row, safe_for_excel(get_field('construction_area', $post_id)));
            $sheet->setCellValue('M' . $row, safe_for_excel(get_field('price_management', $post_id)));
            $sheet->setCellValue('N' . $row, safe_for_excel(get_field('hotline', $post_id)));
            $sheet->setCellValue('O' . $row, html_entity_decode($area_ranges));
            $sheet->setCellValue('P' . $row, $chung_chi);
            $sheet->setCellValue('Q' . $row, $chu_de_1);
            $sheet->setCellValue('R' . $row, $chu_de_2);
            $sheet->setCellValue('S' . $row, $chu_de_3);
            $sheet->setCellValue('T' . $row, $full_address);
            $sheet->setCellValue('U' . $row, $full_address); // Địa chỉ (EN) - giống với VI
            $sheet->setCellValue('V' . $row, $district_name);
            $sheet->setCellValue('W' . $row, $ward_name);
            $sheet->setCellValue('X' . $row, $street_name);
            $sheet->setCellValue('Y' . $row, $block_name);
            $sheet->setCellValue('Z' . $row, $block_name); // Khu vực (EN) - giống với VI
            $sheet->setCellValue('AA' . $row, $brand_name);
            $sheet->setCellValue('AB' . $row, $brand_name); // Thương hiệu (EN) - giống với VI

            // Thông số tòa nhà
            $sheet->setCellValue('AC' . $row, safe_for_excel(get_field('title_parameter', $post_id)));
            $sheet->setCellValue('AD' . $row, ''); // Tiêu đề thông số tòa nhà (EN) - để trống
            $sheet->setCellValue('AE' . $row, safe_for_excel(get_field('dynamic_field_ts_toa_nha_tron_goi_ten-toa-nha', $post_id)));
            $sheet->setCellValue('AF' . $row, ''); // Tên tòa nhà (EN) - để trống
            $sheet->setCellValue('AG' . $row, safe_for_excel(get_field('dynamic_field_ts_toa_nha_tron_goi_quy-mo', $post_id)));
            $sheet->setCellValue('AH' . $row, ''); // Quy mô (EN) - để trống
            $sheet->setCellValue('AI' . $row, safe_for_excel(get_field('dynamic_field_ts_toa_nha_tron_goi_vi-tri', $post_id)));
            $sheet->setCellValue('AJ' . $row, ''); // Vị trí (EN) - để trống
            $sheet->setCellValue('AK' . $row, $hang_toa_nha);
            $sheet->setCellValue('AL' . $row, ''); // Hạng tòa nhà (EN) - để trống
            $sheet->setCellValue('AM' . $row, safe_for_excel(get_field('dynamic_field_ts_toa_nha_tron_goi_thang-may', $post_id)));
            $sheet->setCellValue('AN' . $row, ''); // Thang máy (EN) - để trống
            $sheet->setCellValue('AO' . $row, $huong_toa_nha);
            $sheet->setCellValue('AP' . $row, ''); // Hướng tòa nhà (EN) - để trống

            // Dịch vụ tòa nhà
            $sheet->setCellValue('AQ' . $row, safe_for_excel(get_field('title_info', $post_id)));
            $sheet->setCellValue('AR' . $row, ''); // Tiêu đề thông tin (EN) - để trống
            $sheet->setCellValue('AS' . $row, safe_for_excel(get_field('dynamic_field_dich_vu_tron_goi_tang', $post_id)));
            $sheet->setCellValue('AT' . $row, ''); // Tầng (EN) - để trống
            $sheet->setCellValue('AU' . $row, safe_for_excel(get_field('dynamic_field_dich_vu_tron_goi_loai-hinh', $post_id)));
            $sheet->setCellValue('AV' . $row, ''); // Loại hình (EN) - để trống
            $sheet->setCellValue('AW' . $row, safe_for_excel(get_field('dynamic_field_dich_vu_tron_goi_phong-lam-viec-rieng', $post_id)));
            $sheet->setCellValue('AX' . $row, ''); // Phòng làm việc riêng (EN) - để trống
            $sheet->setCellValue('AY' . $row, safe_for_excel(get_field('dynamic_field_dich_vu_tron_goi_so-cho-ngoi', $post_id)));
            $sheet->setCellValue('AZ' . $row, ''); // Số chỗ ngồi (EN) - để trống
            $sheet->setCellValue('BA' . $row, safe_for_excel(get_field('dynamic_field_dich_vu_tron_goi_gia-thue', $post_id)));
            $sheet->setCellValue('BB' . $row, ''); // Giá thuê (EN) - để trống
            $sheet->setCellValue('BC' . $row, safe_for_excel(get_field('dynamic_field_dich_vu_tron_goi_phi-gui-xe-may', $post_id)));
            $sheet->setCellValue('BD' . $row, ''); // Phí gửi xe máy (EN) - để trống
            $sheet->setCellValue('BE' . $row, safe_for_excel(get_field('dynamic_field_dich_vu_tron_goi_phi-gui-o-to', $post_id)));
            $sheet->setCellValue('BF' . $row, ''); // Phí gửi ô tô (EN) - để trống
            $sheet->setCellValue('BG' . $row, safe_for_excel(get_field('dynamic_field_dich_vu_tron_goi_thoi-gian-thue', $post_id)));
            $sheet->setCellValue('BH' . $row, ''); // Thời gian thuê (EN) - để trống
            $sheet->setCellValue('BI' . $row, safe_for_excel(get_field('dynamic_field_dich_vu_tron_goi_dat-coc', $post_id)));
            $sheet->setCellValue('BJ' . $row, ''); // Đặt cọc (EN) - để trống
            $sheet->setCellValue('BK' . $row, safe_for_excel(get_field('dynamic_field_dich_vu_tron_goi_thanh-toan', $post_id)));
            $sheet->setCellValue('BL' . $row, ''); // Thanh toán (EN) - để trống

            // Bài viết giới thiệu
            $sheet->setCellValue('BM' . $row, safe_for_excel(get_field('td_bai_viet_gioi_thieu', $post_id)));
            $sheet->setCellValue('BN' . $row, ''); // Tiêu đề bài viết giới thiệu (EN) - để trống
            $sheet->setCellValue('BO' . $row, safe_for_excel(get_field('nd_bai_viet_gioi_thieu', $post_id)));
            $sheet->setCellValue('BP' . $row, ''); // Nội dung bài viết giới thiệu (EN) - để trống

            // Tiện ích
            $sheet->setCellValue('BQ' . $row, safe_for_excel(get_field('title_utilities', $post_id)));
            $sheet->setCellValue('BR' . $row, ''); // Tiêu đề tiện ích (EN) - để trống
            $sheet->setCellValue('BS' . $row, safe_for_excel(get_field('dynamic_field_tien_ich_tron_goi_dien-nuoc', $post_id)));
            $sheet->setCellValue('BT' . $row, ''); // Điện nước (EN) - để trống
            $sheet->setCellValue('BU' . $row, safe_for_excel(get_field('dynamic_field_tien_ich_tron_goi_dia-chi-dang-ky-kinh-doanh', $post_id)));
            $sheet->setCellValue('BV' . $row, ''); // Địa chỉ đăng ký kinh doanh (EN) - để trống
            $sheet->setCellValue('BW' . $row, safe_for_excel(get_field('dynamic_field_tien_ich_tron_goi_dich-vu-le-tan', $post_id)));
            $sheet->setCellValue('BX' . $row, ''); // Dịch vụ lễ tân (EN) - để trống
            $sheet->setCellValue('BY' . $row, safe_for_excel(get_field('dynamic_field_tien_ich_tron_goi_ho-tro-it', $post_id)));
            $sheet->setCellValue('BZ' . $row, ''); // Hỗ trợ IT (EN) - để trống
            $sheet->setCellValue('CA' . $row, safe_for_excel(get_field('dynamic_field_tien_ich_tron_goi_he-thong-may-lanh-trung-tam', $post_id)));
            $sheet->setCellValue('CB' . $row, ''); // Hệ thống máy lạnh trung tâm (EN) - để trống
            $sheet->setCellValue('CC' . $row, safe_for_excel(get_field('dynamic_field_tien_ich_tron_goi_dich-vu-ve-sinh', $post_id)));
            $sheet->setCellValue('CD' . $row, ''); // Dịch vụ vệ sinh (EN) - để trống
            $sheet->setCellValue('CE' . $row, safe_for_excel(get_field('dynamic_field_tien_ich_tron_goi_dien-thoai-va-dich-vu-xu-ly-cuoc-goi', $post_id)));
            $sheet->setCellValue('CF' . $row, ''); // Điện thoại và dịch vụ xử lý cuộc gọi (EN) - để trống
            $sheet->setCellValue('CG' . $row, safe_for_excel(get_field('dynamic_field_tien_ich_tron_goi_khu-vuc-pantry', $post_id)));
            $sheet->setCellValue('CH' . $row, ''); // Khu vực pantry (EN) - để trống
            $sheet->setCellValue('CI' . $row, safe_for_excel(get_field('dynamic_field_tien_ich_tron_goi_phone-booth', $post_id)));
            $sheet->setCellValue('CJ' . $row, ''); // Phone booth (EN) - để trống
            $sheet->setCellValue('CK' . $row, safe_for_excel(get_field('dynamic_field_tien_ich_tron_goi_in-an-photocopy', $post_id)));
            $sheet->setCellValue('CL' . $row, ''); // In ấn photocopy (EN) - để trống


            // Bài viết chi tiết
            $sheet->setCellValue('CM' . $row, safe_for_excel(get_field('td_bai_viet_chi_tiet', $post_id)));
            $sheet->setCellValue('CN' . $row, ''); // Tiêu đề bài viết chi tiết (EN) - để trống
            $sheet->setCellValue('CO' . $row, safe_for_excel(get_field('nd_bai_viet_chi_tiet', $post_id)));
            $sheet->setCellValue('CP' . $row, ''); // Nội dung bài viết chi tiết (EN) - để trống

            // Vị trí
            $sheet->setCellValue('CQ' . $row, safe_for_excel(get_field('title_location', $post_id)));
            $sheet->setCellValue('CR' . $row, ''); // Tiêu đề vị trí (EN) - để trống
            $sheet->setCellValue('CS' . $row, safe_for_excel(get_field('toa_do', $post_id)));
            $sheet->setCellValue('CT' . $row, ''); // Tọa độ (EN) - để trống
            $sheet->setCellValue('CU' . $row, safe_for_excel(get_field('google_map', $post_id)));
            $sheet->setCellValue('CV' . $row, ''); // Google map (EN) - để trống
            $sheet->setCellValue('CW' . $row, safe_for_excel(get_field('google_street_view', $post_id)));
            $sheet->setCellValue('CX' . $row, ''); // Google street view (EN) - để trống

            $row++;
        }

        wp_reset_postdata();
    }

    // Auto-size các cột để fit nội dung
    foreach (range('A', 'Z') as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }

    // Auto-size các cột AA-CJ
    foreach (range('AA', 'CJ') as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }

    // Áp dụng filter và border cho toàn bộ bảng
    $lastRow = $row - 1;
    $range = 'A1:CJ' . $lastRow;

    $styleArray = [
        'borders' => [
            'allBorders' => [
                'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                'color' => ['argb' => 'FF000000'],
            ],
        ],
    ];
    $sheet->getStyle($range)->applyFromArray($styleArray);

    // Cập nhật phần định dạng nội dung - áp dụng riêng biệt cho từng phạm vi
    $sheet->getStyle('BA2:BB' . $lastRow)->getAlignment()->setWrapText(true);
    $sheet->getStyle('CA2:CB' . $lastRow)->getAlignment()->setWrapText(true);
    $sheet->getStyle('CG2:CJ' . $lastRow)->getAlignment()->setWrapText(true);

    // Tạo writer và output file
    $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
    $filename = 'office-full-data-export-' . date('Y-m-d') . '.xlsx';

    // Set headers để download file
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $filename . '"');
    header('Cache-Control: max-age=0');

    // Output file
    $writer->save('php://output');
    exit;
}


/**
 * Thêm link export vào trang danh sách office-rent posts
 */
function add_export_link_to_posts_list()
{
    global $current_screen;

    // Chỉ thêm vào trang danh sách office-rent
    if ($current_screen->post_type !== 'office-rent' || $current_screen->id !== 'edit-office-rent') {
        return;
    }

?>
    <div class="wrap" style="margin-top: 10px;">
        <a href="<?php echo admin_url('admin.php?page=excel-import'); ?>#export" class="button button-primary">
            <span class="dashicons dashicons-download" style="margin-top: 4px;"></span>
            Export Dữ Liệu Office
        </a>
    </div>
<?php
}
add_action('admin_notices', 'add_export_link_to_posts_list');







/**
 * Thêm menu quản lý import Excel cho Office Trọn Gói
 */
function add_excel_import_full_page()
{
    add_menu_page(
        'Import Excel Office Trọn Gói',
        'Import Excel Full',
        'manage_options',
        'excel-import-full',
        'render_excel_import_full_page',
        'dashicons-media-spreadsheet',
        31
    );
}
add_action('admin_menu', 'add_excel_import_full_page');

/**
 * Thêm menu quản lý import Excel cho Mặt Bằng Kinh Doanh
 */
function add_excel_import_business_page()
{
    add_menu_page(
        'Import Excel Mặt Bằng Kinh Doanh',
        'Import Business Space',
        'manage_options',
        'excel-import-business',
        'render_excel_import_business_page',
        'dashicons-upload',
        31
    );
}

add_action('admin_menu', 'add_excel_import_business_page');

/**
 * Hiển thị giao diện import Excel cho Office Trọn Gói
 */
function render_excel_import_full_page()
{
    // Xử lý xóa file nếu có
    if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['file'])) {
        handle_excel_file_delete_full($_GET['file']);
    }

    // Xử lý upload file nếu có
    if (isset($_POST['excel_import_full_submit']) && isset($_FILES['excel_file_full'])) {
        handle_excel_upload_full();
    }

    // Lấy danh sách file đã upload
    $uploaded_files = get_excel_files_list_full();

    // Hiển thị form import
?>
    <div class="wrap">
        <h1>Import Excel Data cho Office Trọn Gói</h1>

        <div class="card">
            <h2>Upload Excel File</h2>
            <form method="post" enctype="multipart/form-data">
                <table class="form-table">
                    <tr>
                        <th><label for="excel_file_full">Chọn file Excel</label></th>
                        <td>
                            <input type="file" name="excel_file_full" id="excel_file_full" accept=".xlsx, .xls">
                            <p class="description">Định dạng: .xlsx, .xls</p>
                        </td>
                    </tr>
                </table>

                <?php wp_nonce_field('excel_import_full_action', 'excel_import_full_nonce'); ?>
                <input type="submit" name="excel_import_full_submit" class="button button-primary" value="Upload và Xử lý">
            </form>
        </div>

        <div class="card" style="margin-top: 20px;">
            <h2>Export Dữ Liệu</h2>
            <p>Export dữ liệu từ bài viết ra file Excel</p>
            <form method="post" action="<?php echo admin_url('admin-post.php'); ?>">
                <input type="hidden" name="action" value="export_office_data_full">
                <?php wp_nonce_field('export_office_data_full_nonce', 'export_full_nonce'); ?>

                <p>
                    <label for="export_limit">Số lượng bài viết:</label>
                    <input type="number" id="export_limit" name="export_limit" value="100" min="1" max="1000" step="1">
                </p>

                <p>
                    <button type="submit" class="button button-primary">Export Dữ Liệu</button>
                </p>
            </form>
        </div>

        <?php if (!empty($uploaded_files)) : ?>
            <div class="card-full" style="margin-top: 20px;">
                <h2>File đã upload</h2>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>Tên file</th>
                            <th>Ngày upload</th>
                            <th>Số dòng dữ liệu</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($uploaded_files as $file) : ?>
                            <tr>
                                <td><?php echo esc_html($file['name']); ?></td>
                                <td><?php echo esc_html($file['date']); ?></td>
                                <td><?php echo get_excel_total_rows($file['full_path']); ?></td>
                                <td>
                                    <?php
                                    $import_status = get_option('excel_import_status_full_' . md5($file['full_path']), 'Chưa import');
                                    echo '<span class="status-' . sanitize_html_class($import_status) . '">' . esc_html($import_status) . '</span>';
                                    ?>
                                </td>
                                <td>
                                    <a href="<?php echo admin_url('admin.php?page=excel-import-full&action=view&file=' . urlencode($file['full_path'])); ?>" class="button button-small button-small-full">Xem dữ liệu</a>
                                    <a href="<?php echo admin_url('admin.php?page=excel-import-full&action=delete&file=' . urlencode($file['full_path'])); ?>"
                                       class="button button-small button-link-delete"
                                       onclick="return confirm('Bạn có chắc chắn muốn xóa file này?');">Xóa</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <?php
        // Hiển thị preview nếu được yêu cầu
        if (isset($_GET['action']) && $_GET['action'] == 'view' && isset($_GET['file'])) {
            $file_path = sanitize_text_field(urldecode($_GET['file']));

            // Đọc dữ liệu từ file và hiển thị trực tiếp, không cần JavaScript
            $excel_data = read_excel_file_paged($file_path, 0, 2); // Đọc 2 dòng đầu tiên

            if ($excel_data && !isset($excel_data['error'])) {
        ?>
                <div class="card-full" style="margin-top: 20px;">
                    <h2>Xem trước dữ liệu Excel</h2>

                    <div style="margin-bottom: 15px;">
                        <p>Tổng số dòng dữ liệu: <strong><?php echo $excel_data['total_rows']; ?></strong></p>
                        <p>Hiển thị dòng thứ hai</p>
                    </div>

                    <div>
                        <ul class="excel-data-list">
                            <?php
                            // Bỏ qua dòng tiêu đề, lấy dòng thứ hai dữ liệu
                            $start_index = 2;
                            if (isset($excel_data['data'][$start_index])) {
                                $row = $excel_data['data'][$start_index];
                                // Hiển thị từng cặp tiêu đề/nội dung
                                for ($i = 0; $i < count($row); $i++) {
                                    $header = $excel_data['headers'][$i] ?: 'Không có tiêu đề';
                                    $cell = is_null($row[$i]) ? '' : $row[$i];
                            ?>
                                    <li>
                                        <strong><?php echo esc_html($header); ?>:</strong>
                                        <span><?php echo esc_html($cell); ?></span>
                                    </li>
                            <?php
                                }
                            } else {
                                echo '<li>Không có dữ liệu hoặc chỉ có một dòng dữ liệu</li>';
                            }
                            ?>
                        </ul>
                    </div>
                </div>

                <style>
                    .excel-data-list {
                        list-style: none;
                        padding: 0;
                        margin: 0;
                    }

                    .excel-data-list li {
                        display: flex;
                        padding: 8px 0;
                        border-bottom: 1px solid #eee;
                    }

                    .excel-data-list li strong {
                        width: 200px;
                        margin-right: 15px;
                    }

                    .excel-data-list li span {
                        flex: 1;
                    }
                </style>
            <?php
            } else {
            ?>
                <div class="notice notice-error inline">
                    <p>Không thể đọc dữ liệu từ file Excel. <?php echo isset($excel_data['message']) ? esc_html($excel_data['message']) : ''; ?></p>
                </div>
        <?php
            }
        }
        ?>

        <!-- Phần import tự động - Chỉ có HTML, script được thêm qua extension -->
        <div id="global-import-results" class="card-full" style="margin-top: 20px; display: none;">
            <h2>Quá trình Import (tự động)</h2>
            <div class="results-content"></div>
            <div class="import-log" style="max-height:300px;overflow-y:auto;margin-top:15px;border:1px solid #eee;padding:10px;"></div>
        </div>
    </div>
<?php
}

/**
 * Xử lý upload file Excel cho Office Trọn Gói
 */
function handle_excel_upload_full()
{
    // Kiểm tra nonce
    if (!isset($_POST['excel_import_full_nonce']) || !wp_verify_nonce($_POST['excel_import_full_nonce'], 'excel_import_full_action')) {
        wp_die('Lỗi bảo mật. Vui lòng thử lại.');
    }

    // Kiểm tra file
    if (!isset($_FILES['excel_file_full']) || $_FILES['excel_file_full']['error'] !== UPLOAD_ERR_OK) {
        add_settings_error('excel_import_full', 'file_upload', 'Upload thất bại. Lỗi: ' . $_FILES['excel_file_full']['error'], 'error');
        return;
    }

    // Chuẩn bị để upload file
    require_once(ABSPATH . 'wp-admin/includes/file.php');
    require_once(ABSPATH . 'wp-admin/includes/media.php');
    require_once(ABSPATH . 'wp-admin/includes/image.php');

    // Thêm filter để chuyển đổi thư mục upload
    add_filter('upload_dir', 'excel_upload_dir_filter');

    // Sử dụng wp_handle_upload để upload file
    $upload = wp_handle_upload($_FILES['excel_file_full'], array('test_form' => false));

    // Loại bỏ filter sau khi upload xong
    remove_filter('upload_dir', 'excel_upload_dir_filter');

    if (isset($upload['error'])) {
        // Nếu có lỗi
        add_settings_error('excel_import_full', 'file_upload', 'Lỗi upload: ' . $upload['error'], 'error');
        error_log('Excel import upload error: ' . $upload['error']);
        return;
    }

    // Nếu upload thành công, $upload['file'] sẽ chứa đường dẫn đầy đủ đến file
    $file_path = $upload['file'];

    // Kiểm tra đọc file để xác nhận tính hợp lệ
    try {
        // Sử dụng trực tiếp thư viện PhpSpreadsheet đã cài đặt
        require_once(get_template_directory() . '/inc/lib/vendor/autoload.php');
        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($file_path);
        $spreadsheet = $reader->load($file_path);

        // Lấy số dòng dữ liệu
        $worksheet = $spreadsheet->getActiveSheet();
        $rowCount = $worksheet->getHighestRow() - 1; // Trừ đi dòng tiêu đề

        // Lưu thông tin file vào bảng options để quản lý
        $uploaded_files = get_option('excel_imported_files_full', array());
        $file_info = array(
            'name' => basename($file_path),
            'date' => current_time('mysql'),
            'full_path' => $file_path
        );
        $uploaded_files[] = $file_info;
        update_option('excel_imported_files_full', $uploaded_files);

        add_settings_error('excel_import_full', 'file_upload', 'File Excel đã upload thành công. Tổng số dòng dữ liệu: ' . $rowCount, 'success');
    } catch (Exception $e) {
        // Xóa file nếu có lỗi
        @unlink($file_path);
        add_settings_error('excel_import_full', 'file_upload', 'Lỗi xử lý file Excel: ' . $e->getMessage(), 'error');
        error_log('Excel import processing error: ' . $e->getMessage());
    }
}

/**
 * Xử lý xóa file Excel đã upload cho Office Trọn Gói
 */
function handle_excel_file_delete_full($file_path)
{
    $file_path = urldecode($file_path);
    $uploaded_files = get_option('excel_imported_files_full', array());

    // Tìm và xóa file khỏi danh sách
    $updated_files = array();
    $file_deleted = false;

    foreach ($uploaded_files as $file) {
        if ($file['full_path'] !== $file_path) {
            $updated_files[] = $file;
        } else {
            // Xóa file vật lý
            if (file_exists($file_path)) {
                @unlink($file_path);
            }
            $file_deleted = true;
        }
    }

    if ($file_deleted) {
        update_option('excel_imported_files_full', $updated_files);
        add_settings_error('excel_import_full', 'file_delete', 'File đã được xóa thành công.', 'success');
    } else {
        add_settings_error('excel_import_full', 'file_delete', 'Không tìm thấy file để xóa.', 'error');
    }
}

/**
 * Lấy danh sách file Excel đã upload cho Office Trọn Gói
 */
function get_excel_files_list_full()
{
    $files = get_option('excel_imported_files_full', array());

    // Lọc các file không tồn tại
    $filtered_files = array();
    foreach ($files as $file) {
        if (file_exists($file['full_path'])) {
            $filtered_files[] = $file;
        }
    }

    // Cập nhật lại option nếu có file bị xóa
    if (count($filtered_files) !== count($files)) {
        update_option('excel_imported_files_full', $filtered_files);
    }

    return $filtered_files;
}

/**
 * AJAX endpoint để import từng dòng dữ liệu và chạy tự động cho Office Trọn Gói
 */
add_action('wp_ajax_import_excel_full_batch', 'ajax_import_excel_full_batch');

function ajax_import_excel_full_batch()
{
    // Kiểm tra nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'excel_import_full_ajax_nonce')) {
        wp_send_json_error(array('message' => 'Lỗi bảo mật! Vui lòng tải lại trang.'));
    }

    // Lấy đường dẫn file Excel
    $file_path = isset($_POST['file_path']) ? sanitize_text_field($_POST['file_path']) : '';

    if (empty($file_path) || !file_exists($file_path)) {
        wp_send_json_error(array('message' => 'Không tìm thấy file Excel.'));
    }

    // Lấy vị trí bắt đầu import
    $start_row = isset($_POST['start_row']) ? intval($_POST['start_row']) : 1;
    $batch_size = 1; // Chỉ import 1 dòng mỗi lần

    // Đọc dữ liệu Excel
    $data = read_excel_file($file_path);

    if (!$data) {
        wp_send_json_error(array('message' => 'Không thể đọc dữ liệu từ file Excel.'));
    }

    // Lấy tiêu đề và dữ liệu
    $headers = $data[0];
    $data = array_slice($data, 1);
    $total_rows = count($data);

    if ($start_row > $total_rows) {
        wp_send_json_error(array('message' => 'Vị trí bắt đầu vượt quá tổng số dòng dữ liệu.'));
    }

    // Lấy dữ liệu cần import (chỉ 1 dòng)
    $row_index = $start_row;
    $row = $data[$start_row - 1];

    // Biến theo dõi kết quả
    $imported_posts = array();
    $errors = array();
    $success = false;

    // Bỏ qua dòng trống
    if (!empty(array_filter($row))) {
        // Kết hợp header và dữ liệu
        $row_data = array();
        foreach ($headers as $i => $header) {
            if (isset($row[$i])) {
                $row_data[$header] = $row[$i];
            }
        }

        // Chuyển đổi định dạng ngày từ Excel sang MySQL
        $date_fields = array(
            'date_created' => current_time('mysql'),
            'date_modified' => current_time('mysql')
        );

        // Tìm và chuyển đổi giá trị ngày
        foreach ($row_data as $header => $value) {
            $header_lower = strtolower(trim($header));

            if (strpos($header_lower, 'ngày đăng') !== false || strpos($header_lower, 'ngay dang') !== false) {
                if (is_numeric($value)) {
                    $date_fields['date_created'] = excel_date_to_mysql_date($value);
                    $row_data['excel_date_created'] = $value;
                }
            } else if (strpos($header_lower, 'ngày cập nhật') !== false || strpos($header_lower, 'ngay cap nhat') !== false) {
                if (is_numeric($value)) {
                    $date_fields['date_modified'] = excel_date_to_mysql_date($value);
                    $row_data['excel_date_modified'] = $value;
                }
            }
        }

        // Tạo post mới
        try {
            $post_id = create_office_full_post_from_data($row_data, $date_fields);

            if ($post_id && !is_wp_error($post_id)) {
                $success = true;
                $imported_posts[] = array(
                    'id' => $post_id,
                    'title' => get_the_title($post_id),
                    'row' => $row_index,
                    'edit_link' => get_edit_post_link($post_id, '')
                );
            } else {
                $error_message = is_wp_error($post_id) ? $post_id->get_error_message() : 'Lỗi không xác định khi tạo post.';
                $errors[] = "Dòng #{$row_index}: {$error_message}";
            }
        } catch (Exception $e) {
            $errors[] = "Dòng #{$row_index}: " . $e->getMessage();
        }
    }

    // Kiểm tra còn dữ liệu không
    $next_row = $start_row + 1;
    $has_more = $next_row <= $total_rows;
    $progress_percent = round(($start_row / $total_rows) * 100);

    // Tạo HTML kết quả
    ob_start();
?>
    <div class="import-progress">
        <div class="progress-bar">
            <div class="progress-bar-inner" style="width: <?php echo $progress_percent; ?>%"></div>
        </div>
        <p>Đang xử lý: <?php echo $start_row; ?> / <?php echo $total_rows; ?> dòng (<?php echo $progress_percent; ?>%)</p>
    </div>

    <?php if ($success) : ?>
        <div class="notice notice-success inline">
            <p>Đã import thành công dòng #<?php echo $row_index; ?>: <?php echo esc_html($imported_posts[0]['title']); ?></p>
            <p>Link bài viết: <a href="<?php echo esc_url($imported_posts[0]['edit_link']); ?>" target="_blank">Xem/Sửa</a></p>
        </div>
    <?php elseif (!empty($errors)) : ?>
        <div class="notice notice-error inline">
            <p><?php echo esc_html($errors[0]); ?></p>
        </div>
    <?php endif; ?>

    <?php if ($has_more) : ?>
        <div style="display:none;">
            <button type="button" id="auto-continue-import-full" class="button continue-batch-import-full" data-file="<?php echo esc_attr($file_path); ?>" data-start="<?php echo $next_row; ?>">
                Tiếp tục
            </button>
        </div>
        <script>
            // Tự động import dòng tiếp theo sau 1200ms (thời gian nghỉ lâu hơn để giảm tải)
            jQuery(document).ready(function($) {
                setTimeout(function() {
                    $('#auto-continue-import-full').trigger('click');
                }, 1200);
            });
        </script>
    <?php else : ?>
        <div class="notice notice-success inline">
            <p><strong>Đã hoàn thành import toàn bộ dữ liệu!</strong></p>
        </div>
        <button type="button" class="button close-import-result">Đóng kết quả</button>
    <?php endif; ?>
<?php
    $result_html = ob_get_clean();

    wp_send_json_success(array(
        'html' => $result_html,
        'has_more' => $has_more,
        'next_row' => $next_row,
        'current_row' => $start_row,
        'total_rows' => $total_rows,
        'progress' => $progress_percent
    ));
}



/**
 * Tạo post Office Trọn Gói mới với dữ liệu từ Excel
 *
 * @param array $data Dữ liệu từ Excel
 * @param array $date_fields Ngày tháng đã chuyển đổi
 * @return int|WP_Error ID của post mới hoặc lỗi
 */
function create_office_full_post_from_data($data, $date_fields)
{
    // Thêm ở đầu hàm nếu bạn muốn kiểm tra theo ID
    $existing_post_id = null;
    $lang = 'vi';
    if (isset($data['ID']) && !empty($data['ID']) && is_numeric($data['ID'])) {
        $existing_post_id = check_if_post_exists_by_id($data['ID'], 'office-rent-full');
        $post_title = $data['Tên văn phòng (VI)'];
    }

    // Nếu không tìm thấy theo ID, kiểm tra theo slug từ đường dẫn VI hoặc URL web cũ
    if (!$existing_post_id) {
        // Ưu tiên kiểm tra theo đường dẫn VI trước
        if (isset($data['Đường dẫn (VI)']) && !empty($data['Đường dẫn (VI)'])) {
            $vi_slug = trim($data['Đường dẫn (VI)'], '/');
            if (!empty($vi_slug)) {
                $existing_post_id = check_if_post_exists_by_direct_slug($vi_slug, 'office-rent-full');
            }
        }

        // Nếu vẫn không tìm thấy, kiểm tra theo URL web cũ
        if (!$existing_post_id && isset($data['URL web cũ']) && !empty($data['URL web cũ'])) {
            $existing_post_id = check_if_post_exists_by_slug($data['URL web cũ'], 'office-rent-full');
        }
    }

    // Nếu vẫn không tìm thấy theo slug, kiểm tra theo tiêu đề (chỉ check theo tên tiếng Việt)
    if (!$existing_post_id) {
        // Tìm tiêu đề từ dữ liệu - chỉ dùng tên tiếng Việt
        $post_title = '';
        if (isset($data['Tên văn phòng (VI)'])) {
            $post_title = $data['Tên văn phòng (VI)'];
        } else {
            // Tìm trường tiêu đề thích hợp nếu không có trường cụ thể
            foreach ($data as $key => $value) {
                if (stripos($key, 'tên') !== false || stripos($key, 'tiêu đề') !== false) {
                    $post_title = $value;
                    break;
                }
            }

            // Nếu vẫn không tìm thấy, dùng giá trị mặc định
            if (empty($post_title)) {
                $post_title = 'Văn phòng cho thuê ' . current_time('Y-m-d');
            }
        }

        // Kiểm tra xem bài viết đã tồn tại chưa - chỉ check theo tên tiếng Việt
        $existing_post_id = check_if_post_exists($post_title, 'office-rent-full');
    }

    $is_update = $existing_post_id ? true : false;
    $post_id = $existing_post_id; // Lưu lại ID cho việc xử lý sau này

    // Đảm bảo có post_title cho việc tạo post mới
    if (empty($post_title)) {
        if (isset($data['Tên văn phòng (VI)'])) {
            $post_title = $data['Tên văn phòng (VI)'];
        } else {
            $post_title = 'Văn phòng cho thuê ' . current_time('Y-m-d');
        }
    }

    // Tìm nội dung chi tiết
    $post_content = '';
    if (isset($data['Bài viết chi tiết (VI)'])) {
        $post_content = $data['Bài viết chi tiết (VI)'];
    }

    // Tìm mô tả ngắn
    $post_excerpt = '';
    if (isset($data['Bài viết giới thiệu (VI)'])) {
        $post_excerpt = $data['Bài viết giới thiệu (VI)'];
    }

    // Chuẩn bị dữ liệu post
    $post_data = array(
        'post_title'    => $post_title,
        'post_content'  => $post_content,
        'post_excerpt'  => $post_excerpt,
        'post_status'   => 'publish',
        'post_type'     => 'office-rent-full',
        'post_date'     => $date_fields['date_created'],
        'post_modified' => $date_fields['date_modified'],
    );

    // Xử lý slug với ưu tiên: Đường dẫn (VI) > URL web cũ - chỉ set khi tạo mới
    if (!$existing_post_id) {
        $slug = get_slug_from_excel_data($data, 'vi');
        if ($slug) {
            $post_data['post_name'] = $slug;
        }
    }

    // Tạo post mới hoặc cập nhật post đã tồn tại
    if ($existing_post_id) {
        $post_data['ID'] = $existing_post_id;
        $post_id = wp_update_post($post_data);
        $is_update = true;
    } else {
        $post_id = wp_insert_post($post_data);
    }

    if (is_wp_error($post_id)) {
        return $post_id;
    }

    // Set a flag to indicate this is an update
    if ($is_update) {
        update_post_meta($post_id, '_is_updated_from_excel', '1');
    }

    // Cập nhật meta data
    foreach ($data as $key => $value) {
        if (!empty($value)) {
            // Chuyển đổi key thành meta_key
            $meta_key = sanitize_key(str_replace(' ', '_', strtolower($key)));

            // Lưu meta data
            update_post_meta($post_id, $meta_key, $value);
        }
    }

    // Xử lý Ngày đăng
    if (isset($data['Ngày đăng']) && !empty($data['Ngày đăng'])) {
        $date_value = $data['Ngày đăng'];
        if (is_numeric($date_value)) {
            update_field('ngay_gui', $date_fields['date_created'], $post_id);
        }
    }

    // Xử lý Ngày cập nhật
    if (isset($data['Ngày cập nhật']) && !empty($data['Ngày cập nhật'])) {
        $date_value = $data['Ngày cập nhật'];
        if (is_numeric($date_value)) {
            update_field('ngay_cap_nhat', $date_fields['date_modified'], $post_id);
        }
    }

    // Xử lý Hotline
    if (isset($data['Hotline']) && !empty($data['Hotline'])) {
        update_field('hotline', $data['Hotline'], $post_id);
        // Lưu thêm vào post meta để đảm bảo
        update_post_meta($post_id, 'hotline', $data['Hotline']);
    }

    // Xử lý show_or_off theo cột "Hiển thị"
    if (isset($data['Hiển thị']) && !empty($data['Hiển thị'])) {
        $show_value = strtolower(trim($data['Hiển thị']));
        // Chuyển đổi giá trị text thành boolean
        $show_or_off = ($show_value === 'hiện' || $show_value === 'hiển thị' || $show_value === 'true' || $show_value === '1') ? true : false;
        update_field('show_or_off', $show_or_off, $post_id);
    }

    // 1. Xử lý thành phố (mặc định là Tp. Hồ Chí Minh)
    $city_id = find_or_create_location_term('Tp. Hồ Chí Minh', 'city', $lang);
    if ($city_id) {
        update_post_meta($post_id, '_office_city', $city_id);
    }

    // 2. Xử lý quận huyện
    if (isset($data['Địa chỉ quận']) && !empty($data['Địa chỉ quận'])) {
        $district_id = find_or_create_location_term($data['Địa chỉ quận'], 'district', $lang);
        if ($district_id) {
            update_post_meta($post_id, '_office_district', $district_id);
        }
    }

    // 3. Xử lý phường xã
    if (isset($data['Địa chỉ phường']) && !empty($data['Địa chỉ phường'])) {
        $ward_id = find_or_create_location_term($data['Địa chỉ phường'], 'ward', $lang);
        if ($ward_id) {
            update_post_meta($post_id, '_office_ward', $ward_id);
        }
    }

    // 4. Xử lý đường phố
    if (isset($data['Địa chỉ đường']) && !empty($data['Địa chỉ đường'])) {
        $street_id = find_or_create_location_term($data['Địa chỉ đường'], 'street', $lang);
        if ($street_id) {
            update_post_meta($post_id, '_office_street', $street_id);
        }
    }

    // 5. Xử lý số nhà
    if (isset($data['Địa chỉ (VI)']) && !empty($data['Địa chỉ (VI)'])) {
        update_post_meta($post_id, '_office_house_number', sanitize_text_field($data['Địa chỉ (VI)']));
    }

    // 6. Xử lý khu vực (block)
    if (isset($data['Khu vực']) && !empty($data['Khu vực'])) {
        $block_id = find_or_create_location_term($data['Khu vực'], 'block', $lang);
        if ($block_id) {
            update_post_meta($post_id, '_office_block', $block_id);
        }
    } elseif (isset($data['Khu vực (VI)']) && !empty($data['Khu vực (VI)'])) {
        $block_id = find_or_create_location_term($data['Khu vực (VI)'], 'block', $lang);
        if ($block_id) {
            update_post_meta($post_id, '_office_block', $block_id);
        }
    }

    // 7. Xử lý thương hiệu (brand)
    if (isset($data['Thương hiệu']) && !empty($data['Thương hiệu'])) {
        $brand_id = find_or_create_location_term($data['Thương hiệu'], 'brand', $lang);
        if ($brand_id) {
            update_post_meta($post_id, '_office_brand', $brand_id);
        }
    } elseif (isset($data['Thương hiệu (VI)']) && !empty($data['Thương hiệu (VI)'])) {
        $brand_id = find_or_create_location_term($data['Thương hiệu (VI)'], 'brand');
        if ($brand_id) {
            update_post_meta($post_id, '_office_brand', $brand_id);
        }
    }

    // 8. Xử lý khoảng diện tích
    if (isset($data['Khoảng diện tích']) && !empty($data['Khoảng diện tích'])) {
        $area_ids = process_area_ranges($data['Khoảng diện tích'], $lang);
        if (!empty($area_ids)) {
            update_post_meta($post_id, 'khoang_dien_tich', $area_ids);
        }
    }

    // Lưu thumbnail từ cột "Ảnh đại diện"
    if (isset($data['Ảnh đại diện']) && !empty($data['Ảnh đại diện'])) {
        // Đảm bảo URL hợp lệ trước khi xử lý
        $image_url = trim($data['Ảnh đại diện']);
        if (filter_var($image_url, FILTER_VALIDATE_URL)) {
            $thumbnail_id = import_remote_image($image_url, $post_id);
            if ($thumbnail_id) {
                set_post_thumbnail($post_id, $thumbnail_id);
            }
        }
    }

    // Xử lý thư viện ảnh từ cột "Thư viện ảnh"
    if (isset($data['Thư viện ảnh']) && !empty($data['Thư viện ảnh'])) {
        // Đảm bảo có các URL hợp lệ và được phân tách đúng
        $urls_string = trim($data['Thư viện ảnh']);
        if (!empty($urls_string)) {
            $gallery_ids = import_gallery_images($urls_string, $post_id);
            if (!empty($gallery_ids)) {
                // Lưu dưới dạng mảng ID đúng định dạng ACF
                update_field('gallery', $gallery_ids, $post_id);

                // Log để kiểm tra
                error_log('Đã import gallery cho post ' . $post_id . ': ' . print_r($gallery_ids, true));
            }
        }
    }


    // Xử lý các trường vị trí
    if (isset($data['Tọa độ (VI)']) && !empty($data['Tọa độ (VI)'])) {
        update_field('toa_do', $data['Tọa độ (VI)'], $post_id);
    }

    if (isset($data['Map Street View 360']) && !empty($data['Map Street View 360'])) {
        update_field('google_street_view', $data['Map Street View 360'], $post_id);
    }

    if (isset($data['Giá tiền']) && !empty($data['Giá tiền'])) {
        update_field('price_product', $data['Giá tiền'], $post_id);
    }

    if (isset($data['Khoảng giá']) && !empty($data['Khoảng giá'])) {
        update_field('price_range', $data['Khoảng giá'], $post_id);
    }

    // Xử lý các trường chủ đề
    if (isset($data['Chủ đề (VI)']) && !empty($data['Chủ đề (VI)'])) {
        $topic1_page = get_page_by_title($data['Chủ đề (VI)'], OBJECT, 'page');
        if ($topic1_page) {
            update_field('chu_de_1', $topic1_page->ID, $post_id);
        }
    }

    if (isset($data['Chủ đề con 1 (VI)']) && !empty($data['Chủ đề con 1 (VI)'])) {
        $topic2_page = get_page_by_title($data['Chủ đề con 1 (VI)'], OBJECT, 'page');
        if ($topic2_page) {
            update_field('chu_de_2', $topic2_page->ID, $post_id);
        }
    }

    if (isset($data['Chủ đề con 2 (VI)']) && !empty($data['Chủ đề con 2 (VI)'])) {
        $topic3_page = get_page_by_title($data['Chủ đề con 2 (VI)'], OBJECT, 'page');
        if ($topic3_page) {
            update_field('chu_de_3', $topic3_page->ID, $post_id);
        }
    }

    // Xử lý các trường thông số tòa nhà trọn gói (Office Full)
    update_field('title_parameter', $data['Tiêu đề thông số tòa nhà (VI)'], $post_id);
    // Ánh xạ các trường Excel vào các trường ACF dynamic_field cho các thông số tòa nhà
    $excel_to_acf_mapping = array(
        'Tên tòa nhà (VI)' => 'dynamic_field_ts_toa_nha_tron_goi_ten-toa-nha',
        'URL tòa nhà (VI)' => 'dynamic_field_ts_toa_nha_tron_goi_url-toa-nha',
        'Quy mô (VI)' => 'dynamic_field_ts_toa_nha_tron_goi_quy-mo',
        'Vị trí (VI)' => 'dynamic_field_ts_toa_nha_tron_goi_vi-tri',
        'Hạng tòa nhà (VI)' => 'dynamic_field_ts_toa_nha_tron_goi_hang-toa-nha',
        'Thang máy (VI)' => 'dynamic_field_ts_toa_nha_tron_goi_thang-may',
        'Hướng tòa nhà (VI)' => 'dynamic_field_ts_toa_nha_tron_goi_huong-toa-nha',
    );

    foreach ($excel_to_acf_mapping as $excel_field => $acf_field) {
        if (isset($data[$excel_field]) && !empty($data[$excel_field])) {
            update_field($acf_field, $data[$excel_field], $post_id);
        }
    }

    // Xử lý hạng tòa nhà
    if (isset($data['Hạng tòa nhà']) && !empty($data['Hạng tòa nhà'])) {
        $class_id = find_or_create_location_term($data['Hạng tòa nhà'], 'building-class', $lang);
        if ($class_id) {
            update_field('dynamic_field_ts_toa_nha_tron_goi_hang-toa-nha', $class_id, $post_id);
        }
    } elseif (isset($data['Hạng tòa nhà (VI)']) && !empty($data['Hạng tòa nhà (VI)'])) {
        $class_id = find_or_create_location_term($data['Hạng tòa nhà (VI)'], 'building-class', $lang);
        if ($class_id) {
            update_field('dynamic_field_ts_toa_nha_tron_goi_hang-toa-nha', $class_id, $post_id);
        }
    } elseif (isset($data['Xếp hạng (VI)']) && !empty($data['Xếp hạng (VI)'])) {
        $class_id = find_or_create_location_term($data['Xếp hạng (VI)'], 'building-class', $lang);
        if ($class_id) {
            update_field('dynamic_field_ts_toa_nha_tron_goi_hang-toa-nha', $class_id, $post_id);
        }
    }

    // Xử lý hướng tòa nhà
    if (isset($data['Hướng tòa nhà']) && !empty($data['Hướng tòa nhà'])) {
        $orientations = array_map('trim', explode(',', $data['Hướng tòa nhà']));
        $orientation_ids = array();

        foreach ($orientations as $orientation) {
            $orientation_id = find_or_create_location_term($orientation, 'building-orientation', $lang);
            if ($orientation_id) {
                $orientation_ids[] = $orientation_id;
            }
        }

        if (!empty($orientation_ids)) {
            update_field('dynamic_field_ts_toa_nha_tron_goi_huong-toa-nha', $orientation_ids, $post_id);
        }
    } elseif (isset($data['Hướng tòa nhà (VI)']) && !empty($data['Hướng tòa nhà (VI)'])) {
        $orientations = array_map('trim', explode(',', $data['Hướng tòa nhà (VI)']));
        $orientation_ids = array();

        foreach ($orientations as $orientation) {
            $orientation_id = find_or_create_location_term($orientation, 'building-orientation', $lang);
            if ($orientation_id) {
                $orientation_ids[] = $orientation_id;
            }
        }

        if (!empty($orientation_ids)) {
            update_field('dynamic_field_ts_toa_nha_tron_goi_huong-toa-nha', $orientation_ids, $post_id);
        }
    }


    // Xử lý các trường thông tin dịch vụ trọn gói
    update_field('title_info', $data['Tiêu đề thông tin (VI)'], $post_id);
    // Ánh xạ các trường Excel vào các trường ACF dynamic_field cho các thông số tòa nhà
    $excel_to_acf_info_mapping = array(
        'Tầng (VI)' => 'dynamic_field_dich_vu_tron_goi_tang',
        'Loại hình (VI)' => 'dynamic_field_dich_vu_tron_goi_loai-hinh',
        'Phòng làm việc riêng (VI)' => 'dynamic_field_dich_vu_tron_goi_phong-lam-viec-rieng',
        'Số chỗ ngồi (VI)' => 'dynamic_field_dich_vu_tron_goi_so-cho-ngoi',
        'Giá thuê (VI)' => 'dynamic_field_dich_vu_tron_goi_gia-thue',
        'Phí gửi xe máy (VI)' => 'dynamic_field_dich_vu_tron_goi_phi-gui-xe-may',
        'Phí gửi ô tô (VI)' => 'dynamic_field_dich_vu_tron_goi_phi-gui-o-to',
        'Thời gian thuê (VI)' => 'dynamic_field_dich_vu_tron_goi_thoi-gian-thue',
        'Đặt cọc (VI)' => 'dynamic_field_dich_vu_tron_goi_dat-coc',
        'Thanh toán (VI)' => 'dynamic_field_dich_vu_tron_goi_thanh-toan',
    );

    foreach ($excel_to_acf_info_mapping as $excel_field => $acf_field) {
        if (isset($data[$excel_field]) && !empty($data[$excel_field])) {
            update_field($acf_field, $data[$excel_field], $post_id);
        }
    }


    // Xử lý các trường nội dung bài viết
    if (isset($data['Bài viết giới thiệu (VI)']) && !empty($data['Bài viết giới thiệu (VI)'])) {
        update_field('td_bai_viet_gioi_thieu', 'Giới thiệu văn phòng ' . $post_title, $post_id);
        update_field('nd_bai_viet_gioi_thieu', $data['Bài viết giới thiệu (VI)'], $post_id);
    } elseif (isset($data['Nội dung bài viết giới thiệu (VI)']) && !empty($data['Nội dung bài viết giới thiệu (VI)'])) {
        update_field('td_bai_viet_gioi_thieu', $data['Tiêu đề bài viết giới thiệu (VI)'], $post_id);
        update_field('nd_bai_viet_gioi_thieu', $data['Nội dung bài viết giới thiệu (VI)'], $post_id);
    }


    // Xử lý các trường tiện ích trọn gói
    update_field('title_utilities', $data['Tiêu đề tiện ích (VI)'], $post_id);

    $excel_data_acf_utilities_mapping = array(
        'Điện nước (VI)' => 'dynamic_field_tien_ich_tron_goi_dien-nuoc',
        'Địa chỉ đăng ký kinh doanh (VI)' => 'dynamic_field_tien_ich_tron_goi_dia-chi-dang-ky-kinh-doanh',
        'Dịch vụ lễ tân (VI)' => 'dynamic_field_tien_ich_tron_goi_dich-vu-le-tan',
        'Hỗ trợ IT/internet (VI)' => 'dynamic_field_tien_ich_tron_goi_ho-tro-it',
        'Hệ thống máy lạnh trung tâm (VI)' => 'dynamic_field_tien_ich_tron_goi_he-thong-may-lanh-trung-tam',
        'Dịch vụ vệ sinh (VI)' => 'dynamic_field_tien_ich_tron_goi_dich-vu-ve-sinh',
        'Điện thoại và dịch vụ xử lý cuộc gọi (VI)' => 'dynamic_field_tien_ich_tron_goi_dien-thoai-va-dich-vu-xu-ly-cuoc-goi',
        'Khu vực pantry (VI)' => 'dynamic_field_tien_ich_tron_goi_khu-vuc-pantry',
        'Phone booth (VI)' => 'dynamic_field_tien_ich_tron_goi_phone-booth',
        'In ấn photocopy (VI)' => 'dynamic_field_tien_ich_tron_goi_in-an-photocopy',
        'Khu vực thư giãn (VI)' => 'dynamic_field_tien_ich_tron_goi_khu-vuc-thu-gian',
        'Tiện ích văn phòng (VI)' => 'dynamic_field_tien_ich_tron_goi_tien-ich-van-phong',
        'Tiện ích đặc biệt (VI)' => 'dynamic_field_tien_ich_tron_goi_tien-ich-dac-biet',
    );

    foreach ($excel_data_acf_utilities_mapping as $excel_field => $acf_field) {
        if (isset($data[$excel_field]) && !empty($data[$excel_field])) {
            update_field($acf_field, $data[$excel_field], $post_id);
        }
    }


    if (isset($data['Bài viết chi tiết (VI)']) && !empty($data['Bài viết chi tiết (VI)'])) {
        update_field('td_bai_viet_chi_tiet', 'Giới thiệu văn phòng chi tiết ' . $post_title, $post_id);
        update_field('nd_bai_viet_chi_tiet', $data['Bài viết chi tiết (VI)'], $post_id);
    } elseif (isset($data['Nội dung bài viết chi tiết (VI)']) && !empty($data['Nội dung bài viết chi tiết (VI)'])) {
        update_field('td_bai_viet_chi_tiet', $data['Tiêu đề bài viết chi tiết (VI)'], $post_id);
        update_field('nd_bai_viet_chi_tiet', $data['Nội dung bài viết chi tiết (VI)'], $post_id);
    }


    // Xử lý các trường vị trí
    update_field('title_location', 'Vị trí tòa nhà', $post_id);

    if (isset($data['Tọa độ (VI)']) && !empty($data['Tọa độ (VI)'])) {
        update_field('toa_do', $data['Tọa độ (VI)'], $post_id);
    }

    if (isset($data['Google map (VI)']) && !empty($data['Google map (VI)'])) {
        update_field('google_map', $data['Google map (VI)'], $post_id);
    }

    if (isset($data['Google Street View 360 (VI)']) && !empty($data['Google Street View 360 (VI)'])) {
        update_field('google_street_view', $data['Google Street View 360 (VI)'], $post_id);
    }

    // Thêm link export vào trang danh sách
    add_action('admin_notices', 'add_export_link_to_posts_list_full');

    if ($post_id && !is_wp_error($post_id) && isset($data['Tên văn phòng (EN)']) && !empty($data['Tên văn phòng (EN)'])) {
        // Kiểm tra xem đã có bản dịch tiếng Anh chưa
        $lang = 'en';
        $english_post_id = get_post_meta($post_id, '_office_english_version', true);

        // Thêm debug
        error_log("Processing English version for Vietnamese post $post_id. Found linked English post ID: " . ($english_post_id ? $english_post_id : 'none'));

        // QUAN TRỌNG: Kiểm tra kỹ xem bản tiếng Anh có tồn tại thực sự không
        $english_post_exists = false;
        if ($english_post_id) {
            $english_post = get_post($english_post_id);
            if ($english_post && $english_post->post_type == 'office-rent-full' && $english_post->post_status != 'trash') {
                $english_post_exists = true;
                error_log("Confirmed English post $english_post_id exists and is valid");
            } else {
                error_log("English post $english_post_id doesn't exist or is invalid");
            }
        }

        if ($english_post_exists) {
            // Cập nhật post hiện có
            $english_post_data = array(
                'ID'            => $english_post_id,
                'post_title'    => $data['Tên văn phòng (EN)'],
                'post_status'   => 'publish',
                'post_date'     => $date_fields['date_created'],
                'post_modified' => $date_fields['date_modified']
            );
            $english_post_id = wp_update_post($english_post_data);
            error_log("Updated existing English post: $english_post_id");
        } else {
            // Tạo post mới
            $english_post_data = array(
                'post_title'    => $data['Tên văn phòng (EN)'],
                'post_content'  => '',
                'post_excerpt'  => '',
                'post_status'   => 'publish',
                'post_type'     => 'office-rent-full',
                'post_date'     => $date_fields['date_created'],
                'post_modified' => $date_fields['date_modified']
            );
            $english_post_id = wp_insert_post($english_post_data);
            error_log("Created new English post: $english_post_id");

            // Lưu mối quan hệ hai chiều
            if (!is_wp_error($english_post_id)) {
                update_post_meta($post_id, '_office_english_version', $english_post_id);
                update_post_meta($english_post_id, '_office_vietnamese_version', $post_id);
                error_log("Linked posts: VI=$post_id <-> EN=$english_post_id");
            }
        }

        // Liên kết ngôn ngữ nếu có plugin WPML và nếu english_post_id hợp lệ
        if (!is_wp_error($english_post_id) && function_exists('icl_object_id')) {
            global $sitepress;

            // Đảm bảo bản tiếng Việt được set đúng ngôn ngữ
            $wpml_element_type = apply_filters('wpml_element_type', 'office-rent-full');

            // Set ngôn ngữ cho bản tiếng Việt
            $vi_set_language_args = array(
                'element_id'    => $post_id,
                'element_type'  => $wpml_element_type,
                'trid'          => false,
                'language_code' => 'vi'
            );
            do_action('wpml_set_element_language_details', $vi_set_language_args);

            // Lấy trid của bản tiếng Việt
            $trid = apply_filters('wpml_element_trid', null, $post_id, $wpml_element_type);

            if ($trid) {
                // Set ngôn ngữ cho bản tiếng Anh và liên kết với bản tiếng Việt
                $en_set_language_args = array(
                    'element_id'    => $english_post_id,
                    'element_type'  => $wpml_element_type,
                    'trid'          => $trid,
                    'language_code' => 'en',
                    'source_language_code' => 'vi'
                );
                do_action('wpml_set_element_language_details', $en_set_language_args);

                error_log("WPML: Linked VI post $post_id with EN post $english_post_id using trid $trid");
            } else {
                error_log("WPML: Could not get trid for post $post_id");
            }
        }

        if ($english_post_id && !is_wp_error($english_post_id)) {
            // Đánh dấu cập nhật từ Excel
            update_post_meta($english_post_id, '_is_updated_from_excel', '1');

            // Lưu lại mối quan hệ nếu chưa được lưu
            if (!get_post_meta($english_post_id, '_office_vietnamese_version', true)) {
                update_post_meta($english_post_id, '_office_vietnamese_version', $post_id);
            }
            if (!get_post_meta($post_id, '_office_english_version', true)) {
                update_post_meta($post_id, '_office_english_version', $english_post_id);
            }

            // Copy thumbnail từ bản tiếng Việt - đảm bảo đúng cách
            $thumbnail_id = get_post_thumbnail_id($post_id);
            if ($thumbnail_id) {
                set_post_thumbnail($english_post_id, $thumbnail_id);
            }

            // Copy gallery từ bản tiếng Việt - kiểm tra xem gallery được lưu đúng format
            $gallery = get_field('gallery', $post_id);
            if (!empty($gallery) && is_array($gallery)) {
                update_field('gallery', $gallery, $english_post_id);
                // Debug log
                error_log('Copied gallery to English post ' . $english_post_id . ': ' . print_r($gallery, true));
            }

            // Cập nhật meta data
            foreach ($data as $key => $value) {
                if (!empty($value) && strpos($key, '(EN)') !== false) {
                    // Chuyển đổi key thành meta_key
                    $meta_key = sanitize_key(str_replace(' ', '_', strtolower($key)));
                    // Lưu meta data
                    update_post_meta($english_post_id, $meta_key, $value);
                }
            }

            // Hotline (giữ nguyên như bản tiếng Việt)
            if (isset($data['Hot Line (EN)']) && !empty($data['Hot Line (EN)'])) {
                update_field('hotline', $data['Hot Line (EN)'], $english_post_id);
                update_post_meta($english_post_id, 'hotline', $data['Hot Line (EN)']);
            } elseif (isset($data['Hotline']) && !empty($data['Hotline'])) {
                update_field('hotline', $data['Hotline'], $english_post_id);
                update_post_meta($english_post_id, 'hotline', $data['Hotline']);
            }

            // 1. Xử lý thành phố (mặc định là Tp. Hồ Chí Minh)
            $city_id = find_or_create_location_term('Ho Chi Minh City', 'city', $lang);
            if ($city_id) {
                update_post_meta($english_post_id, '_office_city', $city_id);
            }

            // 2. Xử lý quận huyện
            if (isset($data['Địa chỉ quận (EN)']) && !empty($data['Địa chỉ quận (EN)'])) {
                $district_id = find_or_create_location_term($data['Địa chỉ quận (EN)'], 'district', $lang);
                if ($district_id) {
                    update_post_meta($english_post_id, '_office_district', $district_id);
                }
            }

            // 3. Xử lý phường xã
            if (isset($data['Địa chỉ phường (EN)']) && !empty($data['Địa chỉ phường (EN)'])) {
                $ward_id = find_or_create_location_term($data['Địa chỉ phường (EN)'], 'ward', $lang);
                if ($ward_id) {
                    update_post_meta($english_post_id, '_office_ward', $ward_id);
                }
            }

            // 4. Xử lý đường phố
            if (isset($data['Địa chỉ đường (EN)']) && !empty($data['Địa chỉ đường (EN)'])) {
                $street_id = find_or_create_location_term($data['Địa chỉ đường (EN)'], 'street', $lang);
                if ($street_id) {
                    update_post_meta($english_post_id, '_office_street', $street_id);
                }
            }

            // 5. Xử lý số nhà
            if (isset($data['Địa chỉ (EN)']) && !empty($data['Địa chỉ (EN)'])) {
                update_post_meta($english_post_id, '_office_house_number', sanitize_text_field($data['Địa chỉ (EN)']));
            }

            // 6. Xử lý khu vực (block)
            if (isset($data['Khu vực (EN)']) && !empty($data['Khu vực (EN)'])) {
                $block_id = find_or_create_location_term($data['Khu vực (EN)'], 'block', $lang);
                if ($block_id) {
                    update_post_meta($english_post_id, '_office_block', $block_id);
                }
            } else {
                // Copy từ bản tiếng Việt
                $block_id = get_post_meta($post_id, '_office_block', true);
                if ($block_id) {
                    update_post_meta($english_post_id, '_office_block', $block_id);
                }
            }

            // 7. Xử lý thương hiệu (brand)
            if (isset($data['Thương hiệu (EN)']) && !empty($data['Thương hiệu (EN)'])) {
                $brand_id = find_or_create_location_term($data['Thương hiệu (EN)'], 'brand', $lang);
                if ($brand_id) {
                    update_post_meta($english_post_id, '_office_brand', $brand_id);
                }
            } else {
                // Copy từ bản tiếng Việt
                $brand_id = get_post_meta($post_id, '_office_brand', true);
                if ($brand_id) {
                    update_post_meta($english_post_id, '_office_brand', $brand_id);
                }
            }

            // 8. Xử lý khoảng diện tích (copy từ bản tiếng Việt)
            $area_ids = get_post_meta($post_id, 'khoang_dien_tich', true);
            if (!empty($area_ids)) {
                update_post_meta($english_post_id, 'khoang_dien_tich', $area_ids);
            }



            // Xử lý các trường vị trí
            if (isset($data['Tọa độ (EN)']) && !empty($data['Tọa độ (EN)'])) {
                update_field('toa_do', $data['Tọa độ (EN)'], $english_post_id);
            }

            if (isset($data['Map Street View 360']) && !empty($data['Map Street View 360'])) {
                update_field('google_street_view', $data['Map Street View 360'], $english_post_id);
            }

            if (isset($data['Giá tiền']) && !empty($data['Giá tiền'])) {
                update_field('price_product', $data['Giá tiền'], $english_post_id);
            }

            if (isset($data['Khoảng giá']) && !empty($data['Khoảng giá'])) {
                update_field('price_range', $data['Khoảng giá'], $english_post_id);
            }

            // Xử lý các trường chủ đề
            if (isset($data['Chủ đề (VI)']) && !empty($data['Chủ đề (VI)'])) {
                $topic1_page = get_page_by_title($data['Chủ đề (VI)'], OBJECT, 'page');
                if ($topic1_page) {
                    update_field('chu_de_1', $topic1_page->ID, $english_post_id);
                }
            }

            if (isset($data['Chủ đề con 1 (VI)']) && !empty($data['Chủ đề con 1 (VI)'])) {
                $topic2_page = get_page_by_title($data['Chủ đề con 1 (VI)'], OBJECT, 'page');
                if ($topic2_page) {
                    update_field('chu_de_2', $topic2_page->ID, $english_post_id);
                }
            }

            if (isset($data['Chủ đề con 2 (VI)']) && !empty($data['Chủ đề con 2 (VI)'])) {
                $topic3_page = get_page_by_title($data['Chủ đề con 2 (VI)'], OBJECT, 'page');
                if ($topic3_page) {
                    update_field('chu_de_3', $topic3_page->ID, $english_post_id);
                }
            }

            // Xử lý các trường thông số tòa nhà trọn gói (Office Full)
            update_field('title_parameter', $data['Tiêu đề thông số tòa nhà (EN)'], $english_post_id);
            // Ánh xạ các trường Excel vào các trường ACF dynamic_field cho các thông số tòa nhà
            $excel_to_acf_mapping = array(
                'Tên tòa nhà (EN)' => 'dynamic_field_ts_toa_nha_tron_goi_ten-toa-nha',
                'URL tòa nhà (EN)' => 'dynamic_field_ts_toa_nha_tron_goi_url-toa-nha',
                'Quy mô (EN)' => 'dynamic_field_ts_toa_nha_tron_goi_quy-mo',
                'Vị trí (EN)' => 'dynamic_field_ts_toa_nha_tron_goi_vi-tri',
                'Hạng tòa nhà (EN)' => 'dynamic_field_ts_toa_nha_tron_goi_hang-toa-nha',
                'Thang máy (EN)' => 'dynamic_field_ts_toa_nha_tron_goi_thang-may',
                'Hướng tòa nhà (EN)' => 'dynamic_field_ts_toa_nha_tron_goi_huong-toa-nha',
            );

            foreach ($excel_to_acf_mapping as $excel_field => $acf_field) {
                if (isset($data[$excel_field]) && !empty($data[$excel_field])) {
                    update_field($acf_field, $data[$excel_field], $english_post_id);
                }
            }

            // Xử lý hạng tòa nhà
            if (isset($data['Hạng tòa nhà']) && !empty($data['Hạng tòa nhà'])) {
                $class_id = find_or_create_location_term($data['Hạng tòa nhà'], 'building-class', $lang);
                if ($class_id) {
                    update_field('dynamic_field_ts_toa_nha_tron_goi_hang-toa-nha', $class_id, $english_post_id);
                }
            } elseif (isset($data['Hạng tòa nhà (EN)']) && !empty($data['Hạng tòa nhà (EN)'])) {
                $class_id = find_or_create_location_term($data['Hạng tòa nhà (EN)'], 'building-class', $lang);
                if ($class_id) {
                    update_field('dynamic_field_ts_toa_nha_tron_goi_hang-toa-nha', $class_id, $english_post_id);
                }
            } elseif (isset($data['Xếp hạng (EN)']) && !empty($data['Xếp hạng (EN)'])) {
                $class_id = find_or_create_location_term($data['Xếp hạng (EN)'], 'building-class', $lang);
                if ($class_id) {
                    update_field('dynamic_field_ts_toa_nha_tron_goi_hang-toa-nha', $class_id, $english_post_id);
                }
            }

            // Xử lý hướng tòa nhà
            if (isset($data['Hướng tòa nhà']) && !empty($data['Hướng tòa nhà'])) {
                $orientations = array_map('trim', explode(',', $data['Hướng tòa nhà']));
                $orientation_ids = array();

                foreach ($orientations as $orientation) {
                    $orientation_id = find_or_create_location_term($orientation, 'building-orientation', $lang);
                    if ($orientation_id) {
                        $orientation_ids[] = $orientation_id;
                    }
                }

                if (!empty($orientation_ids)) {
                    update_field('dynamic_field_ts_toa_nha_tron_goi_huong-toa-nha', $orientation_ids, $english_post_id);
                }
            } elseif (isset($data['Hướng tòa nhà (EN)']) && !empty($data['Hướng tòa nhà (EN)'])) {
                $orientations = array_map('trim', explode(',', $data['Hướng tòa nhà (EN)']));
                $orientation_ids = array();

                foreach ($orientations as $orientation) {
                    $orientation_id = find_or_create_location_term($orientation, 'building-orientation', $lang);
                    if ($orientation_id) {
                        $orientation_ids[] = $orientation_id;
                    }
                }

                if (!empty($orientation_ids)) {
                    update_field('dynamic_field_ts_toa_nha_tron_goi_huong-toa-nha', $orientation_ids, $english_post_id);
                }
            }


            // Xử lý các trường thông tin dịch vụ trọn gói
            update_field('title_info', $data['Tiêu đề thông tin (EN)'], $english_post_id);
            // Ánh xạ các trường Excel vào các trường ACF dynamic_field cho các thông số tòa nhà
            $excel_to_acf_info_mapping = array(
                'Tầng (EN)' => 'dynamic_field_dich_vu_tron_goi_tang',
                'Loại hình (EN)' => 'dynamic_field_dich_vu_tron_goi_loai-hinh',
                'Phòng làm việc riêng (EN)' => 'dynamic_field_dich_vu_tron_goi_phong-lam-viec-rieng',
                'Số chỗ ngồi (EN)' => 'dynamic_field_dich_vu_tron_goi_so-cho-ngoi',
                'Giá thuê (EN)' => 'dynamic_field_dich_vu_tron_goi_gia-thue',
                'Phí gửi xe máy (EN)' => 'dynamic_field_dich_vu_tron_goi_phi-gui-xe-may',
                'Phí gửi ô tô (EN)' => 'dynamic_field_dich_vu_tron_goi_phi-gui-o-to',
                'Thời gian thuê (EN)' => 'dynamic_field_dich_vu_tron_goi_thoi-gian-thue',
                'Đặt cọc (EN)' => 'dynamic_field_dich_vu_tron_goi_dat-coc',
                'Thanh toán (EN)' => 'dynamic_field_dich_vu_tron_goi_thanh-toan',
            );

            foreach ($excel_to_acf_info_mapping as $excel_field => $acf_field) {
                if (isset($data[$excel_field]) && !empty($data[$excel_field])) {
                    update_field($acf_field, $data[$excel_field], $english_post_id);
                }
            }


            // Xử lý các trường nội dung bài viết
            if (isset($data['Bài viết giới thiệu (EN)']) && !empty($data['Bài viết giới thiệu (EN)'])) {
                update_field('td_bai_viet_gioi_thieu', 'Giới thiệu văn phòng ' . $post_title, $english_post_id);
                update_field('nd_bai_viet_gioi_thieu', $data['Bài viết giới thiệu (EN)'], $english_post_id);
            } elseif (isset($data['Nội dung bài viết giới thiệu (EN)']) && !empty($data['Nội dung bài viết giới thiệu (EN)'])) {
                update_field('td_bai_viet_gioi_thieu', $data['Tiêu đề bài viết giới thiệu (EN)'], $english_post_id);
                update_field('nd_bai_viet_gioi_thieu', $data['Nội dung bài viết giới thiệu (EN)'], $english_post_id);
            }


            // Xử lý các trường tiện ích trọn gói
            update_field('title_utilities', $data['Tiêu đề tiện ích (EN)'], $english_post_id);

            $excel_data_acf_utilities_mapping = array(
                'Điện nước (EN)' => 'dynamic_field_tien_ich_tron_goi_dien-nuoc',
                'Địa chỉ đăng ký kinh doanh (EN)' => 'dynamic_field_tien_ich_tron_goi_dia-chi-dang-ky-kinh-doanh',
                'Dịch vụ lễ tân (EN)' => 'dynamic_field_tien_ich_tron_goi_dich-vu-le-tan',
                'Hỗ trợ IT/internet (EN)' => 'dynamic_field_tien_ich_tron_goi_ho-tro-it',
                'Hệ thống máy lạnh trung tâm (EN)' => 'dynamic_field_tien_ich_tron_goi_he-thong-may-lanh-trung-tam',
                'Dịch vụ vệ sinh (EN)' => 'dynamic_field_tien_ich_tron_goi_dich-vu-ve-sinh',
                'Điện thoại và dịch vụ xử lý cuộc gọi (EN)' => 'dynamic_field_tien_ich_tron_goi_dien-thoai-va-dich-vu-xu-ly-cuoc-goi',
                'Khu vực pantry (EN)' => 'dynamic_field_tien_ich_tron_goi_khu-vuc-pantry',
                'Phone booth (EN)' => 'dynamic_field_tien_ich_tron_goi_phone-booth',
                'In ấn photocopy (EN)' => 'dynamic_field_tien_ich_tron_goi_in-an-photocopy',
                'Khu vực thư giãn (EN)' => 'dynamic_field_tien_ich_tron_goi_khu-vuc-thu-gian',
                'Tiện ích văn phòng (EN)' => 'dynamic_field_tien_ich_tron_goi_tien-ich-van-phong',
                'Tiện ích đặc biệt (EN)' => 'dynamic_field_tien_ich_tron_goi_tien-ich-dac-biet',
            );

            foreach ($excel_data_acf_utilities_mapping as $excel_field => $acf_field) {
                if (isset($data[$excel_field]) && !empty($data[$excel_field])) {
                    update_field($acf_field, $data[$excel_field], $english_post_id);
                }
            }


            if (isset($data['Bài viết chi tiết (EN)']) && !empty($data['Bài viết chi tiết (EN)'])) {
                update_field('td_bai_viet_chi_tiet', 'Giới thiệu văn phòng chi tiết ' . $post_title, $english_post_id);
                update_field('nd_bai_viet_chi_tiet', $data['Bài viết chi tiết (EN)'], $english_post_id);
            } elseif (isset($data['Nội dung bài viết chi tiết (EN)']) && !empty($data['Nội dung bài viết chi tiết (EN)'])) {
                update_field('td_bai_viet_chi_tiet', $data['Tiêu đề bài viết chi tiết (EN)'], $english_post_id);
                update_field('nd_bai_viet_chi_tiet', $data['Nội dung bài viết chi tiết (EN)'], $english_post_id);
            }


            // Xử lý các trường vị trí
            update_field('title_location', 'Vị trí tòa nhà', $english_post_id);

            if (isset($data['Tọa độ (EN)']) && !empty($data['Tọa độ (EN)'])) {
                update_field('toa_do', $data['Tọa độ (EN)'], $english_post_id);
            }

            // Sử dụng cùng giá trị Google map (VI) cho cả bản tiếng Anh
            if (isset($data['Google map (VI)']) && !empty($data['Google map (VI)'])) {
                update_field('google_map', $data['Google map (VI)'], $english_post_id);
            }

            if (isset($data['Google Street View 360 (EN)']) && !empty($data['Google Street View 360 (EN)'])) {
                update_field('google_street_view', $data['Google Street View 360 (EN)'], $english_post_id);
            }
        }
    }

    return $post_id;
}

/**
 * Thêm link export vào trang danh sách office-rent-full posts
 */
function add_export_link_to_posts_list_full()
{
    global $current_screen;

    // Chỉ thêm vào trang danh sách office-rent-full
    if ($current_screen->post_type !== 'office-rent-full' || $current_screen->id !== 'edit-office-rent-full') {
        return;
    }

?>
    <div class="wrap" style="margin-top: 10px;">
        <a href="<?php echo admin_url('admin.php?page=excel-import-full'); ?>#export" class="button button-primary">
            <span class="dashicons dashicons-download" style="margin-top: 4px;"></span>
            Export Dữ Liệu Office Trọn Gói
        </a>
    </div>
    <?php
}
add_action('admin_notices', 'add_export_link_to_posts_list_full');

/**
 * Thêm extensions cho phần import Excel của Office Full
 */

add_action('admin_init', 'render_excel_import_full_page_extensions');
function render_excel_import_full_page_extensions()
{
    add_action('admin_footer', function () {
        if($_GET['page'] == 'excel-import-full'){
    ?>
        <script>
            jQuery(document).ready(function($) {
                // Chỉ thêm nút cho các button chưa có nút Import Tự Động bên cạnh
                $('.button-small').each(function() {
                    if ($(this).text() === 'Xem dữ liệu' && $(this).next('.batch-import-full-button').length === 0) {
                        var filePath = $(this).attr('href').split('file=')[1];
                        if (filePath) {
                            filePath = decodeURIComponent(filePath);
                            $(this).after('&nbsp;<button type="button" class="button button-primary batch-import-full-button" data-file="' + filePath + '">Import Tự Động</button>');
                        }
                    }
                });

                // Xử lý click bắt đầu import
                $(document).on('click', '.batch-import-full-button', function(e) {
                    e.preventDefault();
                    var button = $(this);
                    var filePath = button.data('file');

                    // Xác nhận trước khi bắt đầu
                    if (!confirm('Bắt đầu import tự động từng dòng? Quá trình sẽ tiếp tục cho đến khi hoàn tất.')) {
                        return;
                    }

                    button.prop('disabled', true).text('Đang import...');

                    // Reset log
                    $('#global-import-results .import-log').empty();

                    // Cuộn lên đầu trang
                    $('html, body').animate({
                        scrollTop: 0
                    }, 300);

                    // Hiển thị loading
                    $('#global-import-results').slideDown();
                    $('#global-import-results .results-content').html(
                        '<div class="import-loading">' +
                        '<p>Đang bắt đầu import dữ liệu...</p>' +
                        '<div class="spinner is-active" style="float:none; display:inline-block; visibility:visible;"></div>' +
                        '</div>'
                    );

                    // Bắt đầu import dòng đầu tiên - gọi đến hàm import dành cho office-rent-full
                    startImportFull(filePath, 1, button);
                });

                // Xử lý click tiếp tục import (tự động)
                $(document).on('click', '.continue-batch-import-full', function(e) {
                    e.preventDefault();
                    var button = $(this);
                    var filePath = button.data('file');
                    var nextRow = button.data('start');

                    startImportFull(filePath, nextRow, null);
                });

                // Hàm bắt đầu import
                function startImportFull(filePath, startRow, originalButton) {
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'import_excel_full_batch',
                            nonce: '<?php echo wp_create_nonce('excel_import_full_ajax_nonce'); ?>',
                            file_path: filePath,
                            start_row: startRow
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.success) {
                                // Cập nhật kết quả
                                $('#global-import-results .results-content').html(response.data.html);

                                // Thêm vào log
                                var logMessage = 'Dòng #' + response.data.current_row + ': ';

                                if ($('#global-import-results .notice-success').length) {
                                    logMessage += 'Import thành công';
                                } else if ($('#global-import-results .notice-error').length) {
                                    logMessage += $('#global-import-results .notice-error p').text();
                                } else {
                                    logMessage += 'Đã xử lý';
                                }

                                $('#global-import-results .import-log').prepend('<p>' + logMessage + '</p>');

                                // Nếu đã hoàn thành và có nút gốc, bật lại nút
                                if (!response.data.has_more && originalButton) {
                                    originalButton.prop('disabled', false).text('Import Tự Động');
                                }
                            } else {
                                // Xử lý lỗi
                                $('#global-import-results .results-content').html(
                                    '<div class="notice notice-error"><p>' + response.data.message + '</p></div>'
                                );

                                // Bật lại nút gốc nếu có
                                if (originalButton) {
                                    originalButton.prop('disabled', false).text('Import Tự Động');
                                }
                            }
                        },
                        error: function(xhr, status, error) {
                            // Xử lý lỗi AJAX
                            $('#global-import-results .results-content').html(
                                '<div class="notice notice-error"><p>Lỗi AJAX: ' + error + '</p></div>'
                            );

                            // Bật lại nút gốc nếu có
                            if (originalButton) {
                                originalButton.prop('disabled', false).text('Import Tự Động');
                            }
                        }
                    });
                }

                // Xử lý nút đóng kết quả
                $(document).on('click', '.close-import-result', function() {
                    $('#global-import-results').slideUp(function() {
                        $('.batch-import-full-button').prop('disabled', false).text('Import Tự Động');
                    });
                });
            });
        </script>
        <style>
            .progress-bar {
                height: 20px;
                background-color: #f0f0f0;
                border-radius: 4px;
                margin: 10px 0;
                overflow: hidden;
            }

            .progress-bar-inner {
                height: 100%;
                background-color: #0073aa;
                width: 0%;
                transition: width 0.3s;
            }

            .import-progress {
                margin-bottom: 15px;
            }

            .import-log {
                font-family: monospace;
                font-size: 12px;
            }

            .import-log p {
                margin: 3px 0;
                padding: 3px 0;
                border-bottom: 1px dotted #eee;
            }
        </style>
    <?php
        }
    });

}

/**
 * Hiển thị giao diện import Excel cho Mặt Bằng Kinh Doanh
 */
function render_excel_import_business_page()
{
    // Xử lý xóa file nếu có
    if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['file'])) {
        handle_excel_file_delete_business($_GET['file']);
    }

    // Xử lý upload file nếu có
    if (isset($_POST['excel_import_business_submit']) && isset($_FILES['excel_file_business'])) {
        handle_excel_upload_business();
    }

    // Lấy danh sách file đã upload
    $uploaded_files = get_excel_files_list_business();

    ?>
    <div class="wrap">
        <h1>Import Excel - Mặt Bằng Kinh Doanh</h1>

        <?php settings_errors('excel_import_business'); ?>

        <div class="card">
            <h2>Upload file Excel</h2>
            <form method="post" enctype="multipart/form-data">
                <table class="form-table">
                    <tr>
                        <th scope="row">Chọn file Excel</th>
                        <td>
                            <input type="file" name="excel_file_business" accept=".xlsx,.xls" required>
                            <p class="description">Chỉ chấp nhận file .xlsx hoặc .xls</p>
                        </td>
                    </tr>
                </table>
                <?php submit_button('Upload và Import', 'primary', 'excel_import_business_submit'); ?>
            </form>
        </div>

        <?php if (!empty($uploaded_files)) : ?>
            <div class="card-full" style="margin-top: 20px;">
                <h2>File đã upload</h2>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>Tên file</th>
                            <th>Ngày upload</th>
                            <th>Số dòng dữ liệu</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($uploaded_files as $file) : ?>
                            <tr>
                                <td><?php echo esc_html($file['name']); ?></td>
                                <td><?php echo esc_html($file['date']); ?></td>
                                <td><?php echo get_excel_total_rows($file['full_path']); ?></td>
                                <td>
                                    <?php
                                    $import_status = get_option('excel_import_status_business_' . md5($file['full_path']), 'Chưa import');
                                    echo '<span class="status-' . sanitize_html_class($import_status) . '">' . esc_html($import_status) . '</span>';
                                    ?>
                                </td>
                                <td>
                                    <a href="<?php echo admin_url('admin.php?page=excel-import-business&action=view&file=' . urlencode($file['full_path'])); ?>" class="button button-small">Xem dữ liệu</a>
                                    <a href="<?php echo admin_url('admin.php?page=excel-import-business&action=delete&file=' . urlencode($file['full_path'])); ?>"
                                       class="button button-small button-link-delete"
                                       onclick="return confirm('Bạn có chắc chắn muốn xóa file này?');">Xóa</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <?php
        // Hiển thị dữ liệu nếu có action view
        if (isset($_GET['action']) && $_GET['action'] === 'view' && isset($_GET['file'])) {
            $file_path = urldecode($_GET['file']);
            if (file_exists($file_path)) {
                display_excel_data_business($file_path);
            }
        }
        ?>
    </div>
    <?php
}

/**
 * Xử lý xóa file Excel đã upload cho Mặt Bằng Kinh Doanh
 */
function handle_excel_file_delete_business($file_path)
{
    $file_path = urldecode($file_path);
    $uploaded_files = get_option('excel_imported_files_business', array());

    // Tìm và xóa file khỏi danh sách
    $updated_files = array();
    $file_deleted = false;

    foreach ($uploaded_files as $file) {
        if ($file['full_path'] !== $file_path) {
            $updated_files[] = $file;
        } else {
            // Xóa file vật lý
            if (file_exists($file_path)) {
                @unlink($file_path);
            }
            $file_deleted = true;
        }
    }

    if ($file_deleted) {
        update_option('excel_imported_files_business', $updated_files);
        add_settings_error('excel_import_business', 'file_delete', 'File đã được xóa thành công.', 'success');
    } else {
        add_settings_error('excel_import_business', 'file_delete', 'Không tìm thấy file để xóa.', 'error');
    }
}

/**
 * Lấy danh sách file Excel đã upload cho Mặt Bằng Kinh Doanh
 */
function get_excel_files_list_business()
{
    $files = get_option('excel_imported_files_business', array());

    // Lọc các file không tồn tại
    $filtered_files = array();
    foreach ($files as $file) {
        if (file_exists($file['full_path'])) {
            $filtered_files[] = $file;
        }
    }

    // Cập nhật lại option nếu có file bị xóa
    if (count($filtered_files) !== count($files)) {
        update_option('excel_imported_files_business', $filtered_files);
    }

    return $filtered_files;
}

/**
 * Xử lý upload file Excel cho Mặt Bằng Kinh Doanh
 */
function handle_excel_upload_business()
{
    if (!isset($_FILES['excel_file_business']) || $_FILES['excel_file_business']['error'] !== UPLOAD_ERR_OK) {
        add_settings_error('excel_import_business', 'upload_error', 'Có lỗi xảy ra khi upload file.', 'error');
        return;
    }

    $file = $_FILES['excel_file_business'];
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    // Kiểm tra định dạng file
    if (!in_array($file_extension, ['xlsx', 'xls'])) {
        add_settings_error('excel_import_business', 'invalid_format', 'Chỉ chấp nhận file Excel (.xlsx, .xls).', 'error');
        return;
    }

    // Tạo thư mục lưu trữ
    $upload_dir = get_excel_upload_dir();
    if (!file_exists($upload_dir)) {
        wp_mkdir_p($upload_dir);
    }

    // Tạo tên file unique
    $filename = 'business_' . time() . '_' . sanitize_file_name($file['name']);
    $file_path = $upload_dir . '/' . $filename;

    // Di chuyển file
    if (move_uploaded_file($file['tmp_name'], $file_path)) {
        // Lưu thông tin file
        $files = get_option('excel_imported_files_business', array());
        $files[] = array(
            'name' => $file['name'],
            'full_path' => $file_path,
            'date' => current_time('Y-m-d H:i:s')
        );
        update_option('excel_imported_files_business', $files);

        add_settings_error('excel_import_business', 'upload_success', 'Upload file thành công!', 'success');
    } else {
        add_settings_error('excel_import_business', 'upload_failed', 'Không thể lưu file.', 'error');
    }
}

/**
 * Hiển thị dữ liệu Excel cho Mặt Bằng Kinh Doanh
 */
function display_excel_data_business($file_path)
{
    require_once get_template_directory() . '/vendor/autoload.php';

    try {
        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xlsx');
        $reader->setReadDataOnly(true);
        $spreadsheet = $reader->load($file_path);
        $worksheet = $spreadsheet->getActiveSheet();
        $data = $worksheet->toArray();

        if (empty($data)) {
            echo '<div class="notice notice-error"><p>File Excel không có dữ liệu.</p></div>';
            return;
        }

        // Lấy header từ dòng đầu tiên
        $headers = array_shift($data);
        $total_rows = count($data);

        ?>
        <div class="card" style="margin-top: 20px;">
            <h2>Dữ liệu Excel - Mặt Bằng Kinh Doanh (<?php echo $total_rows; ?> dòng)</h2>

            <div style="margin-bottom: 15px;">
                <button type="button" class="button button-primary" onclick="startBusinessImport('<?php echo esc_js($file_path); ?>')">
                    Bắt đầu Import
                </button>
                <button type="button" class="button" onclick="location.reload()">Làm mới</button>
            </div>

            <div class="table-responsive" style="max-height: 400px; overflow: auto;">
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <?php foreach ($headers as $header): ?>
                                <th style="min-width: 150px;"><?php echo esc_html($header); ?></th>
                            <?php endforeach; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $display_rows = array_slice($data, 0, 10); // Chỉ hiển thị 10 dòng đầu
                        foreach ($display_rows as $row): ?>
                            <tr>
                                <?php foreach ($row as $cell): ?>
                                    <td style="min-width: 150px; max-width: 200px; word-wrap: break-word;">
                                        <?php echo esc_html(substr($cell, 0, 100)) . (strlen($cell) > 100 ? '...' : ''); ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>
                        <?php endforeach; ?>

                        <?php if ($total_rows > 10): ?>
                            <tr>
                                <td colspan="<?php echo count($headers); ?>" style="text-align: center; font-style: italic;">
                                    ... và <?php echo ($total_rows - 10); ?> dòng khác
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <script>
        function startBusinessImport(filePath) {
            if (!confirm('Bạn có chắc chắn muốn import dữ liệu này?')) {
                return;
            }

            // Hiển thị progress
            document.getElementById('business-import-results').style.display = 'block';

            // Bắt đầu import từng dòng
            importBusinessRow(filePath, 1, <?php echo $total_rows; ?>);
        }

        function importBusinessRow(filePath, currentRow, totalRows) {
            if (currentRow > totalRows) {
                document.querySelector('#business-import-results .progress-text').textContent = 'Hoàn thành!';
                return;
            }

            const formData = new FormData();
            formData.append('action', 'import_business_row');
            formData.append('file_path', filePath);
            formData.append('row_number', currentRow);
            formData.append('nonce', '<?php echo wp_create_nonce('import_business_row'); ?>');

            fetch(ajaxurl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update progress
                    const progress = Math.round((currentRow / totalRows) * 100);
                    document.querySelector('#business-import-results .progress-fill').style.width = progress + '%';
                    document.querySelector('#business-import-results .progress-text').textContent =
                        `Đang xử lý: ${currentRow}/${totalRows} dòng (${progress}%)`;

                    // Add log
                    const logContainer = document.querySelector('#business-import-results .import-log');
                    const logEntry = document.createElement('div');
                    logEntry.className = 'log-entry log-' + (data.data.success ? 'success' : 'error');
                    logEntry.textContent = `[${new Date().toLocaleTimeString()}] Dòng #${currentRow}: ${data.data.message}`;
                    logContainer.appendChild(logEntry);
                    logContainer.scrollTop = logContainer.scrollHeight;

                    // Continue with next row
                    setTimeout(() => importBusinessRow(filePath, currentRow + 1, totalRows), 100);
                } else {
                    console.error('Import failed:', data);
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }
        </script>

        <?php
        // Add import results container
        ?>
        <div id="business-import-results" style="display:none; margin-top: 20px; margin-bottom: 20px; padding: 15px; background-color: #fff; border: 1px solid #ddd; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <h3 style="margin-top: 0;">Tiến trình Import</h3>
            <div class="progress-bar" style="width: 100%; background-color: #f1f1f1; border-radius: 4px; margin-bottom: 15px;">
                <div class="progress-fill" style="width: 0%; height: 20px; background-color: #4CAF50; border-radius: 4px; transition: width 0.3s;"></div>
            </div>
            <div class="progress-text" style="margin-bottom: 15px; font-weight: bold;">Đang chuẩn bị...</div>
            <div class="import-log" style="max-height: 300px; overflow-y: auto; margin-top: 15px; padding: 10px; border: 1px solid #eee; background-color: #fafafa; font-family: monospace; font-size: 12px;">
                <div class="log-header" style="font-weight: bold; margin-bottom: 10px; color: #333;">Log chi tiết:</div>
            </div>
        </div>

        <?php

    } catch (Exception $e) {
        echo '<div class="notice notice-error"><p>Lỗi đọc file Excel: ' . esc_html($e->getMessage()) . '</p></div>';
    }
}

/**
 * AJAX handler cho import business row
 */
function handle_import_business_row()
{
    // Kiểm tra nonce
    if (!wp_verify_nonce($_POST['nonce'], 'import_business_row')) {
        wp_die('Security check failed');
    }

    // Kiểm tra quyền
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }

    $file_path = sanitize_text_field($_POST['file_path']);
    $row_number = intval($_POST['row_number']);

    try {
        require_once get_template_directory() . '/vendor/autoload.php';

        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xlsx');
        $reader->setReadDataOnly(true);
        $spreadsheet = $reader->load($file_path);
        $worksheet = $spreadsheet->getActiveSheet();
        $data = $worksheet->toArray();

        // Lấy header và dòng dữ liệu
        $headers = array_shift($data);

        if (!isset($data[$row_number - 1])) {
            wp_send_json_error('Dòng dữ liệu không tồn tại');
            return;
        }

        $row_data = $data[$row_number - 1];

        // Kết hợp header với dữ liệu
        $combined_data = array_combine($headers, $row_data);

        // Tạo post từ dữ liệu
        $result = create_business_post_from_data($combined_data);

        if ($result['success']) {
            wp_send_json_success([
                'success' => true,
                'message' => $result['message'],
                'post_id' => $result['post_id']
            ]);
        } else {
            wp_send_json_success([
                'success' => false,
                'message' => $result['message']
            ]);
        }

    } catch (Exception $e) {
        wp_send_json_error('Lỗi xử lý: ' . $e->getMessage());
    }
}

add_action('wp_ajax_import_business_row', 'handle_import_business_row');

/**
 * Tạo post business-space từ dữ liệu Excel
 */
function create_business_post_from_data($data)
{
    try {
        // Kiểm tra dữ liệu cơ bản
        if (empty($data) || !is_array($data)) {
            return [
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ'
            ];
        }

        // Kiểm tra xem post đã tồn tại chưa
        $existing_post_id = null;

        // Kiểm tra theo ID nếu có
        if (isset($data['ID']) && !empty($data['ID'])) {
            $existing_post_id = get_posts(array(
                'post_type' => 'business-space',
                'meta_query' => array(
                    array(
                        'key' => '_original_id',
                        'value' => $data['ID'],
                        'compare' => '='
                    )
                ),
                'posts_per_page' => 1,
                'fields' => 'ids'
            ));

            if (!empty($existing_post_id)) {
                $existing_post_id = $existing_post_id[0];
            } else {
                $existing_post_id = null;
            }
        }

        // Nếu không tìm thấy theo ID, kiểm tra theo slug từ đường dẫn VI hoặc URL web cũ
        if (!$existing_post_id) {
            // Ưu tiên kiểm tra theo đường dẫn VI trước
            if (isset($data['Đường dẫn (VI)']) && !empty($data['Đường dẫn (VI)'])) {
                $vi_slug = trim($data['Đường dẫn (VI)'], '/');
                if (!empty($vi_slug)) {
                    $existing_post_id = check_if_post_exists_by_direct_slug($vi_slug, 'business-space');
                }
            }

            // Nếu vẫn không tìm thấy, kiểm tra theo URL web cũ
            if (!$existing_post_id && isset($data['URL web cũ']) && !empty($data['URL web cũ'])) {
                $existing_post_id = check_if_post_exists_by_slug($data['URL web cũ'], 'business-space');
            }
        }

        // Nếu vẫn không tìm thấy theo slug, kiểm tra theo tiêu đề (chỉ check theo tên tiếng Việt)
        if (!$existing_post_id) {
            // Tìm tiêu đề từ dữ liệu - chỉ dùng tên tiếng Việt
            $post_title = '';
            if (isset($data['Tên văn phòng (VI)'])) {
                $post_title = $data['Tên văn phòng (VI)'];
            } else {
                // Tìm trường tiêu đề thích hợp nếu không có trường cụ thể
                foreach ($data as $key => $value) {
                    if (stripos($key, 'tên') !== false || stripos($key, 'tiêu đề') !== false) {
                        $post_title = $value;
                        break;
                    }
                }

                // Nếu vẫn không tìm thấy, dùng giá trị mặc định
                if (empty($post_title)) {
                    $post_title = 'Mặt bằng kinh doanh ' . current_time('Y-m-d');
                }
            }

            // Kiểm tra xem bài viết đã tồn tại chưa - chỉ check theo tên tiếng Việt
            $existing_post_id = check_if_post_exists($post_title, 'business-space');
        }

        $is_update = $existing_post_id ? true : false;
        $post_id = $existing_post_id; // Lưu lại ID cho việc xử lý sau này

        // Đảm bảo có post_title cho việc tạo post mới
        if (empty($post_title)) {
            if (isset($data['Tên văn phòng (VI)'])) {
                $post_title = $data['Tên văn phòng (VI)'];
            } else {
                $post_title = 'Mặt bằng kinh doanh ' . current_time('Y-m-d');
            }
        }

        // Tìm nội dung chi tiết
        $post_content = '';
        if (isset($data['Bài viết chi tiết (VI)'])) {
            $post_content = $data['Bài viết chi tiết (VI)'];
        }

        // Tìm mô tả ngắn
        $post_excerpt = '';
        if (isset($data['Mô tả ngắn (VI)'])) {
            $post_excerpt = $data['Mô tả ngắn (VI)'];
        }

        // Xử lý ngày tháng
        $date_fields = process_date_fields($data);

        // Chuẩn bị dữ liệu post
        $post_data = array(
            'post_title'    => $post_title,
            'post_content'  => $post_content,
            'post_excerpt'  => $post_excerpt,
            'post_status'   => 'publish',
            'post_type'     => 'business-space',
            'post_date'     => $date_fields['date_created'],
            'post_modified' => $date_fields['date_modified'],
        );

        // Xử lý slug với ưu tiên: Đường dẫn (VI) > URL web cũ - chỉ set khi tạo mới
        if (!$existing_post_id) {
            $slug = get_slug_from_excel_data($data, 'vi');
            if ($slug) {
                $post_data['post_name'] = $slug;
            }
        }

        // Tạo post mới hoặc cập nhật post đã tồn tại
        if ($existing_post_id) {
            $post_data['ID'] = $existing_post_id;
            $post_id = wp_update_post($post_data);

            $is_update = true;
        } else {
            $post_id = wp_insert_post($post_data);
        }

        if (is_wp_error($post_id)) {
            return [
                'success' => false,
                'message' => 'Lỗi tạo post: ' . $post_id->get_error_message()
            ];
        }

        // Lưu ID gốc từ Excel
        if (isset($data['ID']) && !empty($data['ID'])) {
            update_post_meta($post_id, '_original_id', $data['ID']);
        }

        // Xử lý các trường ACF tương tự như office-rent
        // (Bạn có thể copy logic từ office-rent và điều chỉnh cho business-space)

        // Tạo bài viết tiếng Anh nếu có dữ liệu tiếng Anh và chưa tồn tại
        if (!$is_update && isset($data['Tên văn phòng (EN)']) && !empty($data['Tên văn phòng (EN)'])) {
            $english_post_data = array(
                'post_title'    => $data['Tên văn phòng (EN)'],
                'post_content'  => isset($data['Bài viết chi tiết (EN)']) ? $data['Bài viết chi tiết (EN)'] : '',
                'post_excerpt'  => isset($data['Mô tả ngắn (EN)']) ? $data['Mô tả ngắn (EN)'] : '',
                'post_status'   => 'publish',
                'post_type'     => 'business-space',
                'post_date'     => $date_fields['date_created'],
                'post_modified' => $date_fields['date_modified']
            );

            // Thêm slug cho bản tiếng Anh với ưu tiên: Đường dẫn (EN) > URL web cũ + -en
            $en_slug = get_slug_from_excel_data($data, 'en');
            if ($en_slug) {
                $english_post_data['post_name'] = $en_slug;
            }

            $english_post_id = wp_insert_post($english_post_data);

            if (!is_wp_error($english_post_id)) {
                // Liên kết 2 bài viết với nhau
                update_post_meta($post_id, '_english_version', $english_post_id);
                update_post_meta($english_post_id, '_vietnamese_version', $post_id);

                // Lưu ID gốc cho bản tiếng Anh
                if (isset($data['ID']) && !empty($data['ID'])) {
                    update_post_meta($english_post_id, '_original_id', $data['ID']);
                }
            }
        }

        $action = $is_update ? 'cập nhật' : 'tạo mới';
        return [
            'success' => true,
            'message' => "Đã {$action} mặt bằng kinh doanh: {$post_title}",
            'post_id' => $post_id
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Lỗi xử lý: ' . $e->getMessage()
        ];
    }
}
