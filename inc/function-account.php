<?php

/**
 * Form Login
 */

add_shortcode('wc_login_form_bbloomer', 'bbloomer_separate_login_form');

function bbloomer_separate_login_form()
{
	if (is_user_logged_in()) return '<p>You are already logged in</p>';
	ob_start();
	do_action('woocommerce_before_customer_login_form');
	woocommerce_login_form(array('redirect' => wc_get_page_permalink('myaccount')));
	return ob_get_clean();
}


// Append Register Link



// Form Register
function custom_woocommerce_registration_form()
{
	if (is_user_logged_in()) {
		return '<p>' . _e('Bạn đã đăng nhập thành công.') . '</p>';
	}

	ob_start(); ?>

	<form method="post" class="custom_registration_form">
		<p class="form-row form-row-wide">
			<label for="reg_fullname"><?php esc_html_e('Họ và tên', 'canhcamtheme'); ?> <span class="required">*</span></label>
			<input required type="text" oninput="this.setCustomValidity('')" oninvalid="this.setCustomValidity('<?php _e('Họ tên là bắt buộc', 'canhcamtheme') ?>')" class="input-text" name="fullname" id="reg_fullname" value="<?php if (!empty($_POST['fullname'])) echo esc_attr(wp_unslash($_POST['fullname'])); ?>" />
		</p>
		<p class="form-row form-row-wide">
			<label for="reg_gender"><?php esc_html_e('Giới tính', 'canhcamtheme'); ?></label>
			<select name="gender" id="reg_gender">
				<option value="male"><?php esc_html_e('Nam', 'canhcamtheme'); ?></option>
				<option value="female"><?php esc_html_e('Nữ', 'canhcamtheme'); ?></option>
				<option value="other"><?php esc_html_e('Khác', 'canhcamtheme'); ?></option>
			</select>
		</p>
		<p class="form-row form-row-wide">
			<label for="reg_email"><?php esc_html_e('Email address', 'woocommerce'); ?> <span class="required">*</span></label>
			<input required oninput="this.setCustomValidity('')" oninvalid="this.setCustomValidity('<?php _e('Email là bắt buộc', 'canhcamtheme') ?>')" type="email" class="input-text" name="email" id="reg_email" value="<?php if (!empty($_POST['email'])) echo esc_attr(wp_unslash($_POST['email'])); ?>" />
		</p>
		<p class="form-row form-row-wide">
			<label for="reg_email"><?php esc_html_e('Phone Number', 'woocommerce'); ?> <span class="required">*</span></label>
			<input required oninput="this.setCustomValidity('')" oninvalid="this.setCustomValidity('<?php _e('Số điện thoại là bắt buộc', 'canhcamtheme') ?>')" type="tel" class="input-text" name="phone" id="reg_phone" value="<?php if (!empty($_POST['phone'])) echo esc_attr(wp_unslash($_POST['phone'])); ?>" />
		</p>
		<p class="form-row form-row-wide">
			<label for="reg_password"><?php esc_html_e('Password', 'woocommerce'); ?> <span class="required">*</span></label>
			<input required oninput="this.setCustomValidity('')" oninvalid="this.setCustomValidity('<?php _e('Mật khẩu là bắt buộc', 'canhcamtheme') ?>')" type="password" class="input-text" name="password" id="reg_password" />
		</p>
		<p class="form-row form-row-wide">
			<label for="reg_repassword"><?php esc_html_e('Confirm Password', 'woocommerce'); ?> <span class="required">*</span></label>
			<input required oninput="this.setCustomValidity('')" oninvalid="this.setCustomValidity('<?php _e('Xác nhận mật khẩu là bắt buộc', 'canhcamtheme') ?>')" type="password" class="input-text" name="repassword" id="reg_repassword" />
		</p>
		<p class="form-row form-row-wide">
			<label for="reg_dob"><?php esc_html_e('Ngày sinh', 'canhcamtheme'); ?></label>
			<input type="date" class="input-text" name="dob" id="reg_dob" value="<?php if (!empty($_POST['dob'])) echo esc_attr(wp_unslash($_POST['dob'])); ?>" />
		</p>
		<p class="woocommerce-form-row form-row">
			<?php wp_nonce_field('woocommerce-register', 'woocommerce-register-nonce'); ?>
			<button type="submit" class="woocommerce-Button button btn w-full justify-center btn-primary" name="register" value="<?php esc_attr_e('Register', 'woocommerce'); ?>"><?php esc_html_e('Register', 'woocommerce'); ?></button>
		</p>
	</form>

<?php
	return ob_get_clean();
}
add_shortcode('custom_registration_form', 'custom_woocommerce_registration_form');
function custom_woocommerce_registration_save($customer_id)
{
	if (isset($_POST['fullname'])) {
		update_user_meta($customer_id, 'fullname', sanitize_text_field($_POST['fullname']));
		update_user_meta($customer_id, 'first_name', sanitize_text_field($_POST['fullname']));
	}
	if (isset($_POST['gender'])) {
		update_user_meta($customer_id, 'gender', sanitize_text_field($_POST['gender']));
	}
	if (isset($_POST['phone'])) {
		update_user_meta($customer_id, 'user_phone', sanitize_text_field($_POST['phone']));
	}
	if (isset($_POST['dob'])) {
		update_user_meta($customer_id, 'dob', sanitize_text_field($_POST['dob']));
	}
}
add_action('woocommerce_created_customer', 'custom_woocommerce_registration_save');

function add_phone_to_current_user($current_user)
{
	if (!is_a($current_user, 'WP_User')) {
		return $current_user;
	}

	// Fetch the user_phone meta value and add it to the current_user object
	$user_phone = get_user_meta($current_user->ID, 'user_phone', true);
	$current_user->user_phone = $user_phone;

	return $current_user;
}
add_filter('wp_get_current_user', 'add_phone_to_current_user');

function custom_woocommerce_registration_errors($errors, $username, $email)
{
	if (empty($_POST['fullname'])) {
		$errors->add('fullname_error', __('Full Name is required!', 'woocommerce'));
	}
	if (empty($_POST['email'])) {
		$errors->add('email_error', __('Email address is required!', 'woocommerce'));
	}
	if (empty($_POST['phone'])) {
		$errors->add('phone_error', __('Phone number is required!', 'woocommerce'));
	}
	if (empty($_POST['password'])) {
		$errors->add('password_error', __('Password is required!', 'woocommerce'));
	}
	if (empty($_POST['repassword'])) {
		$errors->add('repassword_error', __('Confirm Password is required!', 'woocommerce'));
	}
	if ($_POST['password'] !== $_POST['repassword']) {
		$errors->add('password_mismatch', __('Passwords do not match!', 'woocommerce'));
	}

	return $errors;
}
add_filter('woocommerce_registration_errors', 'custom_woocommerce_registration_errors', 10, 3);

// Redirect to the registration page with a success message after successful registration
function custom_registration_redirect($redirect_to)
{
	if (isset($_POST['register']) && !is_wp_error($redirect_to)) {
		$redirect_to = add_query_arg('registered', 'true', wc_get_page_permalink('myaccount'));
	}
	return $redirect_to;
}
add_filter('woocommerce_registration_redirect', 'custom_registration_redirect', 2);

// Display success message on the registration page
function custom_registration_success_message()
{
	if (isset($_GET['registered']) && $_GET['registered'] == 'true') {
		wc_add_notice(__('You have registered successfully.', 'woocommerce'), 'success');
	}
}
add_action('wp', 'custom_registration_success_message');




// add container for account

function add_container_account()
{
	echo '<div class="container wrap-grid-account">';
}
add_action('woocommerce_before_account_navigation', 'add_container_account', 1);



/**
 * Woo - Remove billing country - my account
 */
add_filter('woocommerce_billing_fields', 'remove_account_billing_phone_and_email_fields', 20, 1);
function remove_account_billing_phone_and_email_fields($billing_fields)
{
	if (is_wc_endpoint_url('edit-address')) {
		// unset($billing_fields['billing_company']);
		// $billing_fields['billing_country']['required'] = false;
	}
	return $billing_fields;
}

function hidden_field_nationality()
{
	echo '<style>.address-field {display:none}</style>';
}
add_action('woocommerce_before_edit_account_address_form', 'hidden_field_nationality');

/**
 * Woo - Custom information on page my account
 */
add_action('woocommerce_account_dashboard', 'custom_dashboard_user_info');

function custom_dashboard_user_info()
{
	$current_user = wp_get_current_user();
	if (!($current_user instanceof WP_User)) {
		return;
	}

	// Get user's shipping address
	$customer_id = $current_user->ID;
	$shipping_address = wc_get_account_formatted_address('shipping', $customer_id);

	// Output public user information
	get_template_part('modules/woo-components/my-account/box-account-address', '', array('user_info' => $current_user, 'address' => $shipping_address));



	// Display recent orders
	$order_args = array(
		'limit' => 10, // Limit to 10 orders
		'customer_id' => $current_user->ID,
		'status' => array_keys(wc_get_order_statuses())
	);
	$recent_orders = wc_get_orders($order_args);
	get_template_part('modules/woo-components/my-account/box-account-orders', '', array('user_orders' => $recent_orders));
}

/**
 * Woo - Remove first name in account detail
 */

add_filter(
	'woocommerce_save_account_details_required_fields',
	'edit_account_remove_required_names'
);
function edit_account_remove_required_names($fields)
{
	unset($fields['account_last_name']);
	return $fields;
}

add_action('woocommerce_before_edit_account_form', 'edit_account_remove_last_name');

function edit_account_remove_last_name($fields)
{
?>
	<style>
		.edit-account>p:nth-child(2) {
			display: none;
		}
	</style>
	<script>
		jQuery(document).ready(function($) {
			$('#account_last_name').closest('.woocommerce-form-row').remove();
		});
	</script>
<?php
}


/**
 * @note Woo - edit account - add field phone
 */

function add_phone_field_edit_account_form()
{
	$user_id = get_current_user_id();
	$user_phone = get_user_meta($user_id, 'user_phone', true);
?>
	<p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
		<label for="account_phone"><?php _e('Phone', 'woocommerce'); ?> *</label>
		<input type="text" class="woocommerce-Input woocommerce-Input--text input-text" name="account_phone" id="account_phone" value="<?php echo esc_attr($user_phone); ?>" />
	</p>
<?php
}
add_action('woocommerce_edit_account_form_fields', 'add_phone_field_edit_account_form', 99);


function save_phone_field_edit_account_form($user_id)
{
	if (isset($_POST['account_phone']) && !empty($_POST['account_phone'])) {
		update_user_meta($user_id, 'user_phone', sanitize_text_field($_POST['account_phone']));
	}
}
add_action('woocommerce_save_account_details', 'save_phone_field_edit_account_form');

function validate_phone_field_edit_account_form($args)
{
	if (isset($_POST['account_phone']) && empty($_POST['account_phone'])) {
		$args->add('account_phone_error', __('Phone Number is required', 'woocommerce'));
	}
}
add_action('woocommerce_save_account_details_errors', 'validate_phone_field_edit_account_form');



// Cho phép đăng ký người dùng
add_action('init', 'enable_user_registration');
function enable_user_registration()
{
	if (!get_option('users_can_register')) {
		update_option('users_can_register', 1);
	}
}

// Xử lý đăng nhập qua AJAX
add_action('wp_ajax_custom_login', 'custom_login_callback');
add_action('wp_ajax_nopriv_custom_login', 'custom_login_callback');
function custom_login_callback()
{
	check_ajax_referer('login_action', 'login_nonce');

	$username = sanitize_text_field($_POST['username']);
	$password = $_POST['password'];

	// Check if username is actually an email
	if (is_email($username)) {
		// Check if email exists
		if (!email_exists($username)) {
			wp_send_json(array(
				'success' => false,
				'message' => 'Email này chưa được đăng ký.'
			));
		}
	}

	$credentials = array(
		'user_login'    => $username,
		'user_password' => $password,
		'remember'      => true
	);

	$user = wp_signon($credentials, false);

	if (is_wp_error($user)) {
		wp_send_json(array(
			'success' => false,
			'message' => 'Tên đăng nhập hoặc mật khẩu không đúng.'
		));
	} else {
		wp_set_current_user($user->ID);
		wp_set_auth_cookie($user->ID);
		wp_send_json(array(
			'success' => true,
			'message' => 'Đăng nhập thành công! Đang chuyển hướng...'
		));
	}
}

// Xử lý đăng ký qua AJAX
add_action('wp_ajax_custom_register', 'custom_register_callback');
add_action('wp_ajax_nopriv_custom_register', 'custom_register_callback');
function custom_register_callback()
{
	check_ajax_referer('register_action', 'register_nonce');

	$username = sanitize_user($_POST['username']);
	$email = sanitize_email($_POST['email']);
	$password = $_POST['password'];

	if (username_exists($username)) {
		wp_send_json(array(
			'success' => false,
			'message' => 'Tên đăng nhập đã tồn tại.'
		));
	}

	if (email_exists($email)) {
		wp_send_json(array(
			'success' => false,
			'message' => 'Email đã được sử dụng.'
		));
	}

	$user_id = wp_create_user($username, $password, $email);

	if (is_wp_error($user_id)) {
		wp_send_json(array(
			'success' => false,
			'message' => 'Đăng ký thất bại. Vui lòng thử lại.'
		));
	} else {
		wp_set_current_user($user_id);
		wp_set_auth_cookie($user_id);
		wp_send_json(array(
			'success' => true,
			'message' => 'Đăng ký thành công! Đang chuyển hướng...'
		));
	}
}

// Xử lý quên mật khẩu qua AJAX
add_action('wp_ajax_custom_lost_password', 'custom_lost_password_callback');
add_action('wp_ajax_nopriv_custom_lost_password', 'custom_lost_password_callback');
function custom_lost_password_callback()
{
	check_ajax_referer('lost_password_action', 'lost_password_nonce');

	$user_login = sanitize_text_field($_POST['user_login']);
	$user = get_user_by('login', $user_login) ? get_user_by('login', $user_login) : get_user_by('email', $user_login);

	if (!$user) {
		wp_send_json(array(
			'success' => false,
			'message' => 'Không tìm thấy người dùng với thông tin này.'
		));
	}

	$reset_key = get_password_reset_key($user);
	if (is_wp_error($reset_key)) {
		wp_send_json(array(
			'success' => false,
			'message' => 'Không thể tạo liên kết đặt lại mật khẩu.'
		));
	}

	$message = "Xin chào,\n\n";
	$message .= "Bạn đã yêu cầu đặt lại mật khẩu. Vui lòng nhấp vào liên kết sau để đặt lại mật khẩu của bạn:\n";
	$message .= esc_url_raw(network_site_url("wp-login.php?action=rp&key=$reset_key&login=" . rawurlencode($user->user_login), 'login'));
	$message .= "\n\nNếu bạn không yêu cầu điều này, vui lòng bỏ qua email này.\n";
	$message .= "Trân trọng,\n" . get_bloginfo('name');

	$sent = wp_mail($user->user_email, 'Yêu cầu đặt lại mật khẩu', $message);

	if ($sent) {
		wp_send_json(array(
			'success' => true,
			'message' => 'Liên kết đặt lại mật khẩu đã được gửi đến email của bạn.'
		));
	} else {
		wp_send_json(array(
			'success' => false,
			'message' => 'Không thể gửi email. Vui lòng thử lại.'
		));
	}
}


add_action('after_setup_theme', 'remove_admin_bar_for_subscribers');
function remove_admin_bar_for_subscribers() {
    if (current_user_can('subscriber')) {
        show_admin_bar(false);
    }
}

?>
<?php

function getUserInfo()
{
	$is_login = is_user_logged_in();
	if (!$is_login) {
		return null;
	};
	$current_user = wp_get_current_user();
	$current_user_id = $current_user->ID;
	$user_role = $current_user->roles[0];
	$role_object = get_role($user_role);
	$role_display_name = $user_role;
	$account_link = '';
	if ($role_display_name === 'student') {
		$role_display_name = __("Học viên", 'canhcamtheme');
	}
	return array(
		'id' => $current_user->ID,
		'name' => $current_user->display_name,
		'role' => $role_display_name,
		'email' => $current_user->user_email,
		'phone' => get_field('phone_number', 'user_' . $current_user_id),
		'account_link' => $account_link,
	);
}

?>