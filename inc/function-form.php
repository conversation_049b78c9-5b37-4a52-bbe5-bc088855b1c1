<?php

/**
 * Custom field CF7
 */

// add_filter('shortcode_atts_wpcf7', 'custom_shortcode_atts_wpcf7_filter', 10, 3);

// function custom_shortcode_atts_wpcf7_filter($out, $pairs, $atts)
// {
//     $form_title = 'form_title';
//     if (isset($atts[$form_title])) {
//         $out[$form_title] = $atts[$form_title];
//     }
//     return $out;
// };


/**
 * WPForms
 */


function wpf_dev_disable_scroll_effect_on_all_forms($forms)
{
    foreach ($forms as $form) {
?>
        <script type="text/javascript">
            wpforms.scrollToError = function() {};
            wpforms.animateScrollTop = function() {};
        </script>
<?php

    }
}
add_action('wpforms_wp_footer_end', 'wpf_dev_disable_scroll_effect_on_all_forms', 10, 1);
