<?php
function weichie_dynamic_load_more()
{
    $postType = $_POST['postType'];
    if ($_POST['posts_per_page'])
        $posts_per_page = $_POST['posts_per_page'];

    $taxonomyID = $_POST['taxonomyID'];
    $taxonomy = $_POST['taxonomy'];

    $args = array(
        'post_type' => $postType,
        'posts_per_page' => $posts_per_page,
        'paged' => $_POST['paged'],
        'post_parent' => $_POST['parentPageID'],
    );
    if ($taxonomyID) $args['tax_query'] = array(
        'relation' => 'AND',
        array(
            'taxonomy' => $taxonomy,
            'field' => 'term_id',
            'terms' => array($taxonomyID),
        ),
    );

    $the_query = new WP_Query($args);

    $response = '';
    $max_pages = $the_query->max_num_pages;

    $template = $_POST['template'];
    if ($the_query->have_posts()) {
        ob_start();
        while ($the_query->have_posts()) : $the_query->the_post();
            $response .= get_template_part($template, null, array('id' => get_the_ID()));
        endwhile;
        $output = ob_get_contents();
        ob_end_clean();
    } else {
        $response = '';
    }
    $result = [
        'html' => $output,
        'max' => $max_pages,
    ];

    echo json_encode($result);

    // echo $response;
    exit;
}
add_action('wp_ajax_weichie_dynamic_load_more', 'weichie_dynamic_load_more');
add_action('wp_ajax_nopriv_weichie_dynamic_load_more', 'weichie_dynamic_load_more');



function get_project_callback()
{
    $tax_query = array();
    $meta_query = array();
    $data = $_POST['data'];
    $default_query =  array(
        'taxonomy' => $data['taxonomy'],
        'field' => 'term_id',
        'terms' => array($data['taxonomyID']),
    );
    $post_template = $data['post_template'];
    $template_wrap_top = wp_kses_post($data['template_wrap_top']);
    $template_wrap_bottom = wp_kses_post($data['template_wrap_bottom']);

    $tax_query[] = $default_query;

    $args = array(
        'post_type' => $data['post_type'],
        'posts_per_page' => $data['posts_per_page'],
        'paged' => 1,
    );
    if (isset($data['program']) && $data['program'] !== '-1' && intval($data['program'])) {
        $tax_query[] = array(
            'taxonomy' => 'project-program-tax',
            'field' => 'term_id',
            'terms' => array($data['program']),
        );
    }
    if (isset($data['semester']) && $data['semester'] !== '-1' && intval($data['semester'])) {
        $tax_query[] = array(
            'taxonomy' => 'project-semester-tax',
            'field' => 'term_id',
            'terms' => array($data['semester']),
        );
    }
    if (isset($data['school_year']) && $data['school_year'] !== '-1' && intval($data['school_year'])) {
        $tax_query[] = array(
            'taxonomy' => 'project-school-year-tax',
            'field' => 'term_id',
            'terms' => array($data['school_year']),
        );
    }
    if (isset($data['teacher']) && $data['teacher'] !== '-1' && intval($data['teacher'])) {
        $meta_query[] = array(
            'key' => 'teacher',
            'value' => '"' . $_POST['teacher'] . '"',
            'compare' => 'LIKE'
        );
    }
    if (!empty($tax_query)) {
        $args['tax_query'] = $tax_query;
    }
    $the_query = new WP_Query($args);
    $max_page = $the_query->max_num_pages;
    if ($the_query->have_posts()) {
        ob_start();
        while ($the_query->have_posts()) : $the_query->the_post();
            echo $template_wrap_top;
            get_template_part($post_template, null, array(
                'id' => get_the_ID(),
            ));
            echo $template_wrap_bottom;
        endwhile;
        $html = ob_get_contents();
        ob_end_clean();
    }

    $result = [
        'html' => $html,
        'max_page' => $max_page
    ];

    echo json_encode($result);
    exit;
}
add_action('wp_ajax_get_project_callback', 'get_project_callback');
add_action('wp_ajax_nopriv_get_project_callback', 'get_project_callback');





function get_child_term_column_2()
{
    // Verify nonce
    check_ajax_referer('ajax-nonce', 'nonce');

    $term_id = isset($_POST['term_id']) ? intval($_POST['term_id']) : 0;

    if (!$term_id) {
        wp_send_json_error('Invalid term ID');
    }

    // Get all levels of hierarchy for this taxonomy


    // Get the taxonomy of the parent term
    $parent_term = get_term($term_id);
    if (is_wp_error($parent_term)) {
        wp_send_json_error('Term not found');
    }

    // Get immediate child terms (next level)
    $child_terms = get_terms(array(
        'taxonomy' => $parent_term->taxonomy,
        'hide_empty' => false,
        'parent' => $term_id
    ));

    if (is_wp_error($child_terms)) {
        wp_send_json_error('Error getting child terms');
    }

    // Build HTML for response
    $html = '<ul>';
    foreach ($child_terms as $term) {
        $html .= '<li class="item">';
        $html .= '<a class="top" data-id="' . esc_attr($term->term_id) . '">' . esc_html($term->name) . '</a>';

        // Get next level terms (grandchildren)
        $grandchildren = get_terms(array(
            'taxonomy' => $parent_term->taxonomy,
            'hide_empty' => false,
            'parent' => $term->term_id
        ));

        // Only show next level if it's not the second-to-last level
        if (!empty($grandchildren) && !is_wp_error($grandchildren)) {
            $html .= '<ul class="bottom">';
            foreach ($grandchildren as $grandchild) {
                // Check if this grandchild has children (to determine if it's not the last level)
                $has_great_grandchildren = get_terms(array(
                    'taxonomy' => $parent_term->taxonomy,
                    'hide_empty' => false,
                    'parent' => $grandchild->term_id,
                ));
                if (!empty($has_great_grandchildren) && !is_wp_error($has_great_grandchildren)) {
                    $html .= '<li><a data-id="' . esc_attr($grandchild->term_id) . '">' . esc_html($grandchild->name) . '</a></li>';
                }
            }
            $html .= '</ul>';
        }

        $html .= '</li>';
    }
    $html .= '</ul>';
    wp_send_json_success($html);
}
add_action('wp_ajax_get_child_term_column_2', 'get_child_term_column_2');
add_action('wp_ajax_nopriv_get_child_term_column_2', 'get_child_term_column_2');

function get_child_term_or_posts_column_3()
{
    check_ajax_referer('ajax-nonce', 'nonce');

    $term_id = $_POST['term_id'];
    $term = get_term($term_id);
    $post_type = $term->object_type[0];

    // Helper function to get posts for a term
    function get_term_posts($term_id, $taxonomy, $post_type)
    {
        return get_posts(array(
            'post_type' => $post_type,
            'tax_query' => array(array(
                'taxonomy' => $taxonomy,
                'field' => 'term_id',
                'terms' => $term_id
            )),
            'posts_per_page' => -1
        ));
    }

    // Helper function to render posts list
    function render_posts_list($posts)
    {
        if (empty($posts)) {
            return '';
        }

        $output = '';
        foreach ($posts as $post) {
            ob_start();
            get_template_part('components/share-layout/post-item', null, array(
                'id' => $post->ID
            ));
            $output .= ob_get_clean();
        }
        return $output;
    }

    // Helper function to render term with its posts
    function render_term_with_posts($term, $posts, $children_html = '')
    {
        $output = '<li class="item">';
        $output .= sprintf('<a class="top">%s</a>', esc_html($term->name));
        $output .= '<ul class="bottom">';

        // Add posts that belong to this term
        if (!empty($posts)) {
            $output .= render_posts_list($posts);
        }

        // Add any child terms
        $output .= $children_html;

        $output .= '</ul>';
        $output .= '</li>';

        return $output;
    }
    // ... earlier code remains the same ...

    $descendants = get_term_children($term_id, $term->taxonomy);
    $output = '<ul class="bottom">';

    if (!empty($descendants) && !is_wp_error($descendants)) {
        // Create hierarchy
        $term_hierarchy = array();
        foreach ($descendants as $descendant_id) {
            $descendant = get_term($descendant_id);
            if ($descendant->parent == $term_id) {
                $term_hierarchy[$descendant->term_id] = array(
                    'term' => $descendant,
                    'children' => array()
                );
            } else {
                foreach ($term_hierarchy as &$parent_term) {
                    if ($descendant->parent == $parent_term['term']->term_id) {
                        $parent_term['children'][] = $descendant;
                    }
                }
            }
        }

        // Build output
        foreach ($term_hierarchy as $hierarchy) {
            // Don't get posts for parent terms that have children
            $term_posts = empty($hierarchy['children']) ?
                get_term_posts($hierarchy['term']->term_id, $term->taxonomy, $post_type) :
                array();

            // Process children
            $children_html = '';
            foreach ($hierarchy['children'] as $child) {
                $child_posts = get_term_posts($child->term_id, $term->taxonomy, $post_type);
                $children_html .= render_term_with_posts($child, $child_posts);
            }

            // Add current term with its posts and children
            $output .= render_term_with_posts($hierarchy['term'], $term_posts, $children_html);
        }
    } else {
        // Just show posts for the clicked term
        $term_posts = get_term_posts($term_id, $term->taxonomy, $post_type);
        if (!empty($term_posts)) {
            $output .= render_term_with_posts($term, $term_posts);
        }
    }

    $output .= '</ul>';
    wp_send_json_success($output);
}


add_action('wp_ajax_get_child_term_or_posts_column_3', 'get_child_term_or_posts_column_3');
add_action('wp_ajax_nopriv_get_child_term_or_posts_column_3', 'get_child_term_or_posts_column_3');
