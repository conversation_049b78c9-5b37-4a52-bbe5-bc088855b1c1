.row {
  display: flex;
  flex-wrap: wrap;
  margin-left: -8px;
  margin-right: -8px;
  row-gap: 0.8333333333333334rem }

@media (min-width: 1200px) {

  .row {
    row-gap: 2.083333333333333rem;
    margin-left: clamp(-20px, calc(-20/1920*100rem), calc(-20/1920*100rem));
    margin-right: clamp(-20px, calc(-20/1920*100rem), calc(-20/1920*100rem)) }
    .row.medium-spacing {
      margin-left: clamp(-10px, calc(-10/1920*100rem), calc(-10/1920*100rem));
      margin-right: clamp(-10px, calc(-10/1920*100rem), calc(-10/1920*100rem)); }
  @media (min-width: 1200px) {
    .row.medium-spacing > [class*="col-"] {
      padding-right: clamp(10px, calc(10/1920*100rem), calc(10/1920*100rem));
      padding-left: clamp(10px, calc(10/1920*100rem), calc(10/1920*100rem)); } }
    .row.lg-spacing {
      margin-left: clamp(-12px, calc(-12/1920*100rem), calc(-12/1920*100rem));
      margin-right: clamp(-12px, calc(-12/1920*100rem), calc(-12/1920*100rem)); }
  @media (min-width: 1200px) {
    .row.lg-spacing > [class*="col-"] {
      padding-right: clamp(12px, calc(12/1920*100rem), calc(12/1920*100rem));
      padding-left: clamp(12px, calc(12/1920*100rem), calc(12/1920*100rem)); } } }
  .row > [class*="col-"] {
    position: relative;
    width: 100%;
    padding-right: 8px;
    padding-left: 8px; }

@media (min-width: 1200px) {
  .row > [class*="col-"] {
    padding-right: clamp(20px, calc(20/1920*100rem), calc(20/1920*100rem));
    padding-left: clamp(20px, calc(20/1920*100rem), calc(20/1920*100rem)); } }
  .row .col {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%; }

@media (max-width: 575.98px) {
  .row.column-full-no-gutter {
    margin-left: 0;
    margin-right: 0 }
    .row.column-full-no-gutter > [class*='col-'] {
    padding-left: 0;
    padding-right: 0 } }

.no-gutters {
  margin-left: 0;
  margin-right: 0; }

.p-0 {
  padding-left: 0;
  padding-right: 0; }

.col-auto {
  flex: 0 0 auto;
  width: auto;
  max-width: 100%; }

.col-1 {
  flex: 0 0 8.33333%;
  max-width: 8.33333%; }

.col-2 {
  flex: 0 0 16.66667%;
  max-width: 16.66667%; }

.col-3 {
  flex: 0 0 25%;
  max-width: 25%; }

.col-4 {
  flex: 0 0 33.33333%;
  max-width: 33.33333%; }

.col-5 {
  flex: 0 0 41.66667%;
  max-width: 41.66667%; }

.col-6 {
  flex: 0 0 50%;
  max-width: 50%; }

.col-7 {
  flex: 0 0 58.33333%;
  max-width: 58.33333%; }

.col-8 {
  flex: 0 0 66.66667%;
  max-width: 66.66667%; }

.col-9 {
  flex: 0 0 75%;
  max-width: 75%; }

.col-10 {
  flex: 0 0 83.33333%;
  max-width: 83.33333%; }

.col-11 {
  flex: 0 0 91.66667%;
  max-width: 91.66667%; }

.col-12 {
  flex: 0 0 100%;
  max-width: 100%; }

@media (min-width: 576px) {
  .col-sm {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%; }
  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%; }
  .col-sm-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%; }
  .col-sm-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%; }
  .col-sm-3 {
    flex: 0 0 25%;
    max-width: 25%; }
  .col-sm-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%; }
  .col-sm-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%; }
  .col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%; }
  .col-sm-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%; }
  .col-sm-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%; }
  .col-sm-9 {
    flex: 0 0 75%;
    max-width: 75%; }
  .col-sm-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%; }
  .col-sm-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%; }
  .col-sm-12 {
    flex: 0 0 100%;
    max-width: 100%; } }

@media (min-width: 768px) {
  .col-md {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%; }
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%; }
  .col-md-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%; }
  .col-md-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%; }
  .col-md-3 {
    flex: 0 0 25%;
    max-width: 25%; }
  .col-md-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%; }
  .col-md-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%; }
  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%; }
  .col-md-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%; }
  .col-md-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%; }
  .col-md-9 {
    flex: 0 0 75%;
    max-width: 75%; }
  .col-md-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%; }
  .col-md-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%; }
  .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%; } }

@media (min-width: 1024px) {
  .col-lg {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%; }
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%; }
  .col-lg-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%; }
  .col-lg-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%; }
  .col-lg-3 {
    flex: 0 0 25%;
    max-width: 25%; }
  .col-lg-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%; }
  .col-lg-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%; }
  .col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%; }
  .col-lg-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%; }
  .col-lg-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%; }
  .col-lg-9 {
    flex: 0 0 75%;
    max-width: 75%; }
  .col-lg-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%; }
  .col-lg-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%; }
  .col-lg-12 {
    flex: 0 0 100%;
    max-width: 100%; } }

@media (min-width: 1200px) {
  .col-xl {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%; }
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%; }
  .col-xl-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%; }
  .col-xl-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%; }
  .col-xl-3 {
    flex: 0 0 25%;
    max-width: 25%; }
  .col-xl-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%; }
  .col-xl-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%; }
  .col-xl-6 {
    flex: 0 0 50%;
    max-width: 50%; }
  .col-xl-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%; }
  .col-xl-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%; }
  .col-xl-9 {
    flex: 0 0 75%;
    max-width: 75%; }
  .col-xl-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%; }
  .col-xl-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%; }
  .col-xl-12 {
    flex: 0 0 100%;
    max-width: 100%; } }

.container, header .wrap-bottom .mega-menu-inner {
  max-width: 100%;
  padding-left: 12px;
  padding-right: 12px;
  margin-left: auto;
  margin-right: auto;
  width: 100% }

@media (min-width: 1200px) {
  .container, header .wrap-bottom .mega-menu-inner {
    max-width: 75rem;
    padding-left: 1.0416666666666667rem;
    padding-right: 1.0416666666666667rem } }

.container-fluid {
  max-width: 100%;
  padding-left: 12px;
  padding-right: 12px }

@media (min-width: 1200px) {

  .container-fluid {
    padding-left: 2.0833333333333335rem;
    padding-right: 2.0833333333333335rem } }

.container-fluid {
  margin-left: auto;
  margin-right: auto;
  width: 100% }

body, html {
  font-size: 14px; }

@media (min-width: 576px) {
  body, html {
    font-size: 16px; } }

@media (min-width: 1200px) {
  body, html {
    font-size: 1vw; } }

body {
  --mr: 16px;
  --spv: 1; }

main {
  max-width: 100vw;
  --primary-3: #f47621; }

::-moz-selection {
  --tw-bg-opacity: 1;
  background-color: rgb(244 118 33 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }

::selection {
  --tw-bg-opacity: 1;
  background-color: rgb(244 118 33 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }

img.noImage {
  padding: 5% !important;
  -o-object-fit: contain !important;
     object-fit: contain !important; }

.zoom-img {
  overflow: hidden; }

@media (min-width: 1200px) {
  .zoom-img:hover img, .zoom-img:hover svg {
    transform: scale(1.1); } }
  .zoom-img .img {
  overflow: hidden }
  .zoom-img img, .zoom-img svg {
    transition: 0.8s transform ease-in-out !important; }
  .zoom-img .not-zoom img, .zoom-img .not-zoom svg {
    transform: none !important;
    transition: none !important; }
  .zoom-img .img:not(.not-zoom) {
  overflow: hidden }
    .zoom-img .img:not(.not-zoom) img, .zoom-img .img:not(.not-zoom) svg {
      transition: 0.3s transform ease-in-out !important; }

@media (min-width: 1200px) {
  .zoom-img .img:not(.not-zoom):hover img, .zoom-img .img:not(.not-zoom):hover svg {
    transform: scale(1.1); } }

@keyframes slide-in-blurred-right {
  0% {
    transform: translateX(1000px) scaleX(2.5) scaleY(0.2);
    transform-origin: 0% 50%;
    filter: blur(40px);
    opacity: 0; }
  100% {
    transform: translateX(0) scaleY(1) scaleX(1);
    transform-origin: 50% 50%;
    filter: blur(0);
    opacity: 1; } }

@keyframes swing {
  0% {
    transform: translateX(clamp(-10px, calc(-10/1920*100rem), calc(-10/1920*100rem))); }
  100% {
    transform: translateX(clamp(10px, calc(10/1920*100rem), calc(10/1920*100rem))); } }

@keyframes copycheck {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }

@keyframes slide-in-blurred-right {
  0% {
    transform: translateX(1000px) scaleX(2.5) scaleY(0.2);
    transform-origin: 0% 50%;
    filter: blur(40px);
    opacity: 0; }
  100% {
    transform: translateX(0) scaleY(1) scaleX(1);
    transform-origin: 50% 50%;
    filter: blur(0);
    opacity: 1; } }

@keyframes ping {
  0% {
    transform: scale(0.7);
    opacity: 1; }
  75% {
    transform: scale(1.05);
    opacity: 0.75; }
  100% {
    transform: scale(1.2);
    opacity: 0; } }

@keyframes fade-in-bck {
  0% {
    transform: translateZ(80px);
    opacity: 0; }
  100% {
    transform: translateZ(0) scale(1.1);
    opacity: 1; } }

@keyframes next-section-icon-jumping {
  from {
    transform: translateY(0); }
  to {
    transform: translateY(10px); } }

.btn {
  transition: all 0.4s cubic-bezier(0.5, 0.24, 0, 1); }
  .btn::before {
    transition: all 0.4s cubic-bezier(0.5, 0.24, 0, 1); }

.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  text-align: center;
  height: clamp(40px, calc(48/1920*100rem), calc(48/1920*100rem)) }
  .btn[disabled] {
  pointer-events: none;
  opacity: 0.8;
  --tw-grayscale: grayscale(.7);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) }

.form-submit .btn-primary {
  min-width: 6.25rem }

.btn-primary {
  border-radius: 0.20833333333333334rem;
  position: relative;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  overflow: hidden;
  width: -moz-fit-content;
  width: fit-content;
  gap: 0.20833333333333334rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  font-size: 13px }

@media (min-width: 576px) {

  .btn-primary {
    font-size: 0.8333333333333334rem } }

.btn-primary {
  padding-left: 0.8333333333333334rem;
  padding-right: 0.8333333333333334rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  display: flex;
  align-items: center;
  background-color: transparent;
  z-index: 1;
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(0 166 75 / var(--tw-border-opacity, 1)) }
  .btn-primary:not(i) {
  font-weight: 700 }
  .btn-primary i {
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.28571 }
  @media (min-width: 1200px) {

  .btn-primary i {
    font-size: 0.7291666666666667rem } }
  .btn-primary i {
  display: flex;
  align-items: center;
  justify-content: center;
  height: clamp(24px, calc(24/1920*100rem), calc(24/1920*100rem));
  width: clamp(20px, calc(20/1920*100rem), calc(20/1920*100rem)) }
  .btn-primary.btn-orange {
  --tw-border-opacity: 1;
  border-color: rgb(244 118 33 / var(--tw-border-opacity, 1)) }
    .btn-primary.btn-orange::before {
      background: none;
      --tw-bg-opacity: 1;
      background-color: rgb(244 118 33 / var(--tw-bg-opacity, 1)) }
    .btn-primary.btn-orange:hover {
      box-shadow: 0px 0px 6px 0px var(--primary-3); }
  .btn-primary.btn-border-orange {
  --tw-border-opacity: 1;
  border-color: rgb(244 118 33 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1)) }
    .btn-primary.btn-border-orange::before {
      background: none;
      opacity: 0 }
    .btn-primary.btn-border-orange:hover {
      box-shadow: 0px 0px 6px 0px var(--primary-3); }
  .btn-primary.btn-border-green {
  --tw-border-opacity: 1;
  border-color: rgb(14 107 56 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }
    .btn-primary.btn-border-green::before {
      background: none;
      opacity: 0 }
    .btn-primary.btn-border-green:hover {
      box-shadow: 0px 0px 6px 0px var(--primary-3); }
  .btn-primary.btn-light {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(227 239 232 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(19 133 71 / var(--tw-text-opacity, 1)) }
  @media (min-width: 1200px) {

  .btn-primary.btn-light {
    font-size: clamp(18px, calc(18/1920*100rem), calc(18/1920*100rem));
    line-height: 1.33333 }
  @media (min-width: 1200px) {

    .btn-primary.btn-light {
      font-size: 0.9375rem } } }
  .btn-primary.btn-light {
  border-radius: 0.4**************7rem }
    @media (min-width: 1200px) {

  .btn-primary.btn-light i {
    font-size: clamp(18px, calc(18/1920*100rem), calc(18/1920*100rem));
    line-height: 1.33333 }
  @media (min-width: 1200px) {

    .btn-primary.btn-light i {
      font-size: 0.9375rem } } }
    .btn-primary.btn-light::before {
  opacity: 0 }
    .btn-primary.btn-light:hover {
  --tw-border-opacity: 1;
  border-color: rgb(14 107 56 / var(--tw-border-opacity, 1));
  background-color: transparent }
      .btn-primary.btn-light:hover::before {
  opacity: 1 }
  .btn-primary.btn-border-gray {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  --tw-border-opacity: 1;
  border-color: rgb(220 220 220 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(129 129 129 / var(--tw-text-opacity, 1)) }
  @media (min-width: 1200px) {

  .btn-primary.btn-border-gray {
    font-size: clamp(18px, calc(18/1920*100rem), calc(18/1920*100rem));
    line-height: 1.33333 }
  @media (min-width: 1200px) {

    .btn-primary.btn-border-gray {
      font-size: 0.9375rem } }
  .btn-primary.btn-border-gray i {
    font-size: clamp(18px, calc(18/1920*100rem), calc(18/1920*100rem));
    line-height: 1.33333 }
  @media (min-width: 1200px) {

    .btn-primary.btn-border-gray i {
      font-size: 0.9375rem } } }
    .btn-primary.btn-border-gray::before {
  opacity: 0;
  --tw-bg-opacity: 1;
  background-color: rgb(244 118 33 / var(--tw-bg-opacity, 1));
  background-image: none }
    .btn-primary.btn-border-gray:hover {
  --tw-border-opacity: 1;
  border-color: rgb(244 118 33 / var(--tw-border-opacity, 1));
  background-color: transparent }
      .btn-primary.btn-border-gray:hover::before {
  opacity: 1 }
  .btn-primary::before {
    content: '';
    background: linear-gradient(270deg, #0E6B38 0%, #0B8C45 100%);
    position: absolute;
    inset: 0;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
    z-index: -1 }
  .btn-primary::after {
    content: '';
    background-image: url(../img/loading.svg);
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    position: absolute;
    left: 50%;
    top: 50%;
    --tw-translate-x: -50%;
    --tw-translate-y: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    pointer-events: none;
    width: 2.083333333333333rem;
    height: 2.083333333333333rem;
    opacity: 0;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms }
  .btn-primary:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  --tw-border-opacity: 1;
  border-color: rgb(244 118 33 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(244 118 33 / var(--tw-bg-opacity, 1)) }
    .btn-primary:hover::before {
  opacity: 0 }
  .btn-primary.has-icon {
  padding-right: 0.8333333333333334rem }
  .btn-primary.btn-loading {
  pointer-events: none }
  @keyframes pulse {

  50% {
    opacity: .5 } }
  .btn-primary.btn-loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --tw-bg-opacity: 1;
  background-color: rgb(209 209 209 / var(--tw-bg-opacity, 1));
  color: transparent;
  border-color: transparent }
    .btn-primary.btn-loading span {
  opacity: 0 }
    .btn-primary.btn-loading::after {
  opacity: 1 }
  .btn-primary.btn-expand span:nth-of-type(1) {
  display: block }
  .btn-primary.btn-expand span:nth-of-type(2) {
  display: none }
  .btn-primary.btn-expand.active span:nth-of-type(1) {
  display: none }
  .btn-primary.btn-expand.active span:nth-of-type(2) {
  display: block }

.btn-secondary {
  background: linear-gradient(195deg, #0E6B38 5.3%, #0E6B38 89.89%);
  border-radius: 0.20833333333333334rem;
  position: relative;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  overflow: hidden;
  width: -moz-fit-content;
  width: fit-content;
  gap: 0.8333333333333334rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  font-size: 13px;
  font-weight: 700 }

@media (min-width: 576px) {

  .btn-secondary {
    font-size: 0.8333333333333334rem } }

.btn-secondary {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  display: flex;
  align-items: center;
  z-index: 1;
  font-size: clamp(18px, calc(18/1920*100rem), calc(18/1920*100rem));
  line-height: 1.33333 }

@media (min-width: 1200px) {

  .btn-secondary {
    font-size: 0.9375rem } }
  .btn-secondary i {
  font-size: clamp(18px, calc(18/1920*100rem), calc(18/1920*100rem));
  line-height: 1.33333 }
  @media (min-width: 1200px) {

  .btn-secondary i {
    font-size: 0.9375rem } }
  .btn-secondary i {
  font-weight: 400 }
  .btn-secondary::before {
    content: '';
    --tw-bg-opacity: 1;
    background-color: rgb(19 133 71 / var(--tw-bg-opacity, 1));
    position: absolute;
    inset: 0;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
    z-index: -1;
    opacity: 0 }
  .btn-secondary::after {
    content: '';
    background-image: url(../img/loading.svg);
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    position: absolute;
    left: 50%;
    top: 50%;
    --tw-translate-x: -50%;
    --tw-translate-y: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    pointer-events: none;
    width: 2.083333333333333rem;
    height: 2.083333333333333rem;
    opacity: 0;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms }
  .btn-secondary:hover {
    box-shadow: 0px 0px 6px 0px #138547;
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
    background-color: transparent }
    .btn-secondary:hover::before {
  opacity: 1 }
  .btn-secondary.btn-loading {
  pointer-events: none }
  @keyframes pulse {

  50% {
    opacity: .5 } }
  .btn-secondary.btn-loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --tw-bg-opacity: 1;
  background-color: rgb(209 209 209 / var(--tw-bg-opacity, 1));
  color: transparent }
    .btn-secondary.btn-loading span {
  opacity: 0 }
    .btn-secondary.btn-loading::after {
  opacity: 1 }

.view-link-long {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  padding-left: 0.8333333333333334rem;
  padding-right: 0.8333333333333334rem;
  min-height: clamp(36px, calc(36/1920*100rem), calc(36/1920*100rem));
  border-bottom-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  border-color: transparent;
  font-weight: 500;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }
  .view-link-long:hover {
  --tw-border-opacity: 1;
  border-color: rgb(14 107 56 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }

.popup-member.fancybox__container .fancybox__content.fancybox__content.fancybox__content {
  border-radius: 0.8333333333333334rem;
  border-width: 0px;
  max-width: clamp(1040px, calc(1040/1920*100rem), calc(1040/1920*100rem));
  padding: 2.083333333333333rem;
  width: 100%;
  border-color: transparent }

.popup-contact-template.fancybox__container .fancybox__content.fancybox__content.fancybox__content {
  border-radius: 0.8333333333333334rem;
  border-width: 0px;
  max-width: clamp(1040px, calc(1040/1920*100rem), calc(1040/1920*100rem));
  padding-left: 0.8333333333333334rem;
  padding-right: 0.8333333333333334rem;
  padding-top: 2.9**************5rem;
  padding-bottom: 2.9**************5rem }

@media (min-width: 576px) {

  .popup-contact-template.fancybox__container .fancybox__content.fancybox__content.fancybox__content {
    padding-left: 2.083333333333333rem;
    padding-right: 2.083333333333333rem } }

@media (min-width: 1200px) {

  .popup-contact-template.fancybox__container .fancybox__content.fancybox__content.fancybox__content {
    padding: 4.**************6rem } }

.popup-contact-template.fancybox__container .fancybox__content.fancybox__content.fancybox__content {
  border-color: transparent }

.popup-contact-template .form-wrap {
  display: grid;
  gap: 1.25rem }

@media (min-width: 768px) {

  .popup-contact-template .form-wrap {
    grid-template-columns: repeat(2, minmax(0, 1fr)) } }

@media (min-width: 1200px) {

  .popup-contact-template .form-wrap {
    -moz-column-gap: 1.6666666666666667rem;
         column-gap: 1.6666666666666667rem;
    row-gap: 1.25rem } }
  .popup-contact-template .form-wrap textarea {
  height: clamp(107px, calc(107/1920*100rem), calc(107/1920*100rem)) }

.fancybox__container.fancybox__container.fancybox__container .fancybox__content {
  --tw-text-opacity: 1;
  color: rgb(41 41 41 / var(--tw-text-opacity, 1)) }

.fancybox__container.fancybox__container.fancybox__container .fancybox__backdrop {
  background-color: rgb(0 0 0 / 0.5) }

.popup-form.fancybox__container .fancybox__viewport .fancybox__content {
  border-radius: 1.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(246 246 246 / var(--tw-bg-opacity, 1));
  padding: 2.083333333333333rem;
  max-width: clamp(764px, calc(764/1920*100rem), calc(764/1920*100rem));
  padding-top: 2.083333333333333rem;
  padding-bottom: 2.083333333333333rem;
  padding-left: 0.8333333333333334rem;
  padding-right: 0.8333333333333334rem }

@media (min-width: 1024px) {

  .popup-form.fancybox__container .fancybox__viewport .fancybox__content {
    padding: 2.083333333333333rem } }

.popup-form.fancybox__container .fancybox__viewport textarea {
  height: clamp(124px, calc(124/1920*100rem), calc(124/1920*100rem)); }

.popup-form .form-group label {
  font-weight: 400 }

.popup-form .form-group input, .popup-form .form-group textarea, .popup-form .form-group .file-upload-type {
  width: 100%;
  resize: none;
  border-radius: 0.625rem;
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(239 239 239 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 1.0416666666666665rem;
  padding-right: 1.0416666666666665rem;
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.28571 }

@media (min-width: 1200px) {

  .popup-form .form-group input, .popup-form .form-group textarea, .popup-form .form-group .file-upload-type {
    font-size: 0.7291666666666667rem } }

.popup-form .form-group input, .popup-form .form-group textarea, .popup-form .form-group .file-upload-type {
  display: block }

.popup-form .form-group textarea {
  height: 100% }

.popup-form .form-notice {
  grid-column: 1 / -1 }
  .popup-form .form-notice a {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }
    .popup-form .form-notice a:hover {
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1));
  text-decoration-line: underline }

.popup-form .form-upload .file-upload-type {
  --tw-bg-opacity: 1;
  background-color: rgb(189 189 189 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.28571 }

@media (min-width: 1200px) {

  .popup-form .form-upload .file-upload-type {
    font-size: 0.7291666666666667rem } }

.popup-form .form-upload .file-upload-type {
  border-color: transparent;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.4**************7rem;
  cursor: pointer }
  .popup-form .form-upload .file-upload-type::after {
    content: "\f0ee";
    font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
    line-height: 1.28571 }
  @media (min-width: 1200px) {

  .popup-form .form-upload .file-upload-type::after {
    font-size: 0.7291666666666667rem } }
  .popup-form .form-upload .file-upload-type::after {
  font-family: 'Font Awesome 6 Pro';
  font-weight: 400 }
  .popup-form .form-upload .file-upload-type .wpcf7-form-control-wrap {
  display: none }

.popup-form .form-upload .file-info {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.4**************7rem;
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.28571 }

@media (min-width: 1200px) {

  .popup-form .form-upload .file-info {
    font-size: 0.7291666666666667rem } }

.popup-form .form-upload .file-info {
  margin-top: 0.4**************7rem;
  border-radius: 0.625rem;
  --tw-bg-opacity: 1;
  background-color: rgb(239 239 239 / var(--tw-bg-opacity, 1));
  padding-left: 1.0416666666666665rem;
  padding-right: 1.0416666666666665rem;
  padding-top: 0.4**************7rem;
  padding-bottom: 0.4**************7rem }
  .popup-form .form-upload .file-info .file-name {
  flex: 1 1 0% }
  .popup-form .form-upload .file-info .file-clear {
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 9999px;
  background-color: rgb(189 189 189 / 0.7);
  padding: 0.4**************7rem;
  cursor: pointer;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }
    .popup-form .form-upload .file-info .file-clear:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(189 189 189 / var(--tw-bg-opacity, 1)) }
    .popup-form .form-upload .file-info .file-clear svg {
  height: 100%;
  width: 100% }
  .popup-form .form-upload .file-info .file-error {
  width: 100%;
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.5;
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1)) }

.has-custom-close-normal-button.fancybox__container .fancybox__viewport .fancybox__content [data-fancybox-close] {
  border: 0 !important;
  box-shadow: none !important;
  width: 40px;
  height: 40px;
  padding: 0;
  opacity: 1;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  background-color: transparent;
  top: 0;
  right: 0;
  border-radius: 0.20833333333333334rem }

@media (min-width: 1200px) {
  .has-custom-close-normal-button.fancybox__container .fancybox__viewport .fancybox__content [data-fancybox-close] {
    width: clamp(40px, calc(40/1920*100rem), calc(40/1920*100rem));
    height: clamp(40px, calc(40/1920*100rem), calc(40/1920*100rem)); } }
  .has-custom-close-normal-button.fancybox__container .fancybox__viewport .fancybox__content [data-fancybox-close] i {
  font-size: clamp(22px, calc(24/1920*100rem), calc(24/1920*100rem));
  line-height: 1.25 }
  @media (min-width: 1200px) {

  .has-custom-close-normal-button.fancybox__container .fancybox__viewport .fancybox__content [data-fancybox-close] i {
    font-size: 1.25rem } }
  .has-custom-close-normal-button.fancybox__container .fancybox__viewport .fancybox__content [data-fancybox-close] i {
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }
  .has-custom-close-normal-button.fancybox__container .fancybox__viewport .fancybox__content [data-fancybox-close]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(244 118 33 / var(--tw-bg-opacity, 1)) }
    .has-custom-close-normal-button.fancybox__container .fancybox__viewport .fancybox__content [data-fancybox-close]:hover i {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }

.has-custom-close-normal-button.fancybox__container .fancybox__carousel .f-button::before {
  display: none }

.fancybox__container.fancybox__container .fancybox__viewport .fancybox__content {
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(152 152 152 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1)) }

[data-fancybox] {
  cursor: pointer }

.fancybox__container .fancybox__slide.has-youtube .fancybox__content {
  max-width: 1176px;
  width: 100% !important; }

.fancybox__container .fancybox__carousel .fancybox__viewport .fancybox__track .fancybox__slide.has-pdf {
  padding: 0;
  padding-left: 0;
  padding-right: 0 }
  .fancybox__container .fancybox__carousel .fancybox__viewport .fancybox__track .fancybox__slide.has-pdf .fancybox__content {
  height: 100%;
  width: 100%;
  padding-top: 2.083333333333333rem }
    .fancybox__container .fancybox__carousel .fancybox__viewport .fancybox__track .fancybox__slide.has-pdf .fancybox__content [data-fancybox-close] {
      top: 0;
      right: 0;
      border: 0 !important;
      box-shadow: none !important;
      width: 30px;
      height: 30px;
      padding: 0;
      background-color: transparent;
      opacity: 1;
      transition-property: all;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 150ms;
      border-radius: 0px }

@media (min-width: 1200px) {
  .fancybox__container .fancybox__carousel .fancybox__viewport .fancybox__track .fancybox__slide.has-pdf .fancybox__content [data-fancybox-close] {
    width: 1.5625rem;
    height: 1.5625rem; } }
      .fancybox__container .fancybox__carousel .fancybox__viewport .fancybox__track .fancybox__slide.has-pdf .fancybox__content [data-fancybox-close] i {
  font-size: clamp(20px,calc(20/1920*100rem),calc(20/1920*100rem));
  line-height: 1.5;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
      .fancybox__container .fancybox__carousel .fancybox__viewport .fancybox__track .fancybox__slide.has-pdf .fancybox__content [data-fancybox-close]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(204 204 204 / var(--tw-bg-opacity, 1)) }

.wpcf7-not-valid-tip, .error-message {
  margin-top: 0.4**************7rem;
  margin-bottom: 0.4**************7rem;
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.5;
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1)) }

.wpcf7 form.wpcf7-form .wpcf7-response-output {
  margin: 0;
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
  border-width: 0px;
  text-align: center;
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.5;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  border-radius: 0.20833333333333334rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  width: 100% }

.wpcf7 form.wpcf7-form.submitting .button {
  cursor: not-allowed }

.wpcf7 form.wpcf7-form.submitting button {
  pointer-events: none;
  --tw-bg-opacity: 1;
  background-color: rgb(220 220 220 / var(--tw-bg-opacity, 1));
  opacity: 0.5 }

.wpcf7 form.wpcf7-form.unaccepted .wpcf7-response-output {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1)) }

.wpcf7 form.wpcf7-form.sent .wpcf7-response-output, .wpcf7 form.wpcf7-form.validating .wpcf7-response-output {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1)) }

.wpcf7 form.wpcf7-form.invalid .wpcf7-response-output {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1)) }

.wpcf7 form.wpcf7-form.invalid .form-group .wpcf7-not-valid-tip {
  margin-top: 0.4**************7rem;
  margin-bottom: 0.4**************7rem;
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.5;
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1)) }

.wpcf7 form.wpcf7-form .form-group.phoneInput input, .wpcf7 form.wpcf7-form .form-group.countryInput input {
  padding-left: 2.2916666666666665rem }

.form-group input, .form-group textarea, .form-group select, .form-group .fake-input {
  display: block;
  width: 100%;
  font-size: clamp(16px, calc(16/1920*100rem), calc(16/1920*100rem));
  line-height: 1.375 }

@media (min-width: 1200px) {

  .form-group input, .form-group textarea, .form-group select, .form-group .fake-input {
    font-size: 0.8333333333333334rem } }

.form-group input, .form-group textarea, .form-group select, .form-group .fake-input {
  --tw-text-opacity: 1;
  color: rgb(129 129 129 / var(--tw-text-opacity, 1));
  padding-top: clamp(8px, calc(8/1920*100rem), calc(8/1920*100rem));
  padding-bottom: clamp(8px, calc(8/1920*100rem), calc(8/1920*100rem));
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(239 239 239 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  border-radius: 0.20833333333333334rem;
  padding-left: 1.0416666666666665rem;
  padding-right: 1.0416666666666665rem }
  .form-group input:disabled, .form-group textarea:disabled, .form-group select:disabled, .form-group .fake-input:disabled {
  --tw-border-opacity: 1;
  border-color: rgb(189 189 189 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(189 189 189 / var(--tw-bg-opacity, 1)) }

.form-group textarea {
  height: 4.**************6rem;
  resize: none }

.form-group label {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1)) }

.form-wrap-layout label input, .form-wrap-layout label select, .form-wrap-layout label textarea, .form-wrap-layout label .fake-input {
  margin-top: 0.4**************7rem }

.form-wrap-layout input, .form-wrap-layout select, .form-wrap-layout textarea, .form-wrap-layout .fake-input {
  border-radius: 0.20833333333333334rem;
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(239 239 239 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(246 246 246 / var(--tw-bg-opacity, 1));
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 0.8333333333333334rem;
  padding-right: 0.8333333333333334rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(129 129 129 / var(--tw-text-opacity, 1)) }

.form-title {
  font-size: clamp(20px, calc(20/1920*100rem), calc(20/1920*100rem));
  line-height: 1.3 }

@media (min-width: 1200px) {

  .form-title {
    font-size: 1.0416666666666667rem } }

.form-title {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1)) }

.search-page .search-form form {
  position: relative }
  .search-page .search-form form input {
    padding: 0.52083rem 1.04167rem;
    border: thin solid;
    padding-right: 2.34375rem;
    width: 100%;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms }
    .search-page .search-form form input:not(:-moz-placeholder-shown) {
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
    .search-page .search-form form input:not(:placeholder-shown) {
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
  .search-page .search-form form button {
    width: 2.08333rem;
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    height: 100%;
    align-items: center;
    justify-content: center }

.search-page .search-query {
  margin-top: 0.8333333333333334rem;
  margin-bottom: 0.8333333333333334rem }

.search-page .found-nothing {
  background-image: url(../img/nothing.png);
  height: 30vh;
  background-position: center;
  background-repeat: no-repeat }

.search-page .found-nothing-title {
  position: relative;
  font-size: clamp(20px,calc(20/1920*100rem),calc(20/1920*100rem));
  line-height: 1.5;
  font-weight: 700;
  text-transform: uppercase;
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }

.search-page .list-search {
  display: grid;
  gap: 0.625rem }

@media (min-width: 576px) {

  .search-page .list-search {
    grid-template-columns: repeat(2, minmax(0, 1fr)) } }

@media (min-width: 768px) {

  .search-page .list-search {
    gap: 1.25rem } }

@media (min-width: 1024px) {

  .search-page .list-search {
    grid-template-columns: repeat(3, minmax(0, 1fr)) } }

@media (min-width: 1200px) {

  .search-page .list-search {
    gap: 2.083333333333333rem } }

.wp-pagination.wp-pagination {
  margin-top: 2.083333333333333rem }
  .wp-pagination.wp-pagination ul {
  display: flex;
  list-style-type: none;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: 0.625rem }
    .wp-pagination.wp-pagination ul li {
  list-style-type: none;
      flex: 0 0 clamp(40px, calc(40/1920*100rem), calc(40/1920*100rem));
      width: clamp(40px, calc(40/1920*100rem), calc(40/1920*100rem));
      height: clamp(40px, calc(40/1920*100rem), calc(40/1920*100rem));
      transition-property: all;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 150ms }
      .wp-pagination.wp-pagination ul li.prev-page, .wp-pagination.wp-pagination ul li.next-page {
  display: none }
        .wp-pagination.wp-pagination ul li.prev-page a, .wp-pagination.wp-pagination ul li.next-page a {
          background-image: url('data:image/svg+xml,<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0 4C0 1.79086 1.79086 0 4 0H36C38.2091 0 40 1.79086 40 4V36C40 38.2091 38.2091 40 36 40H4C1.79086 40 0 38.2091 0 36V4Z" /><path d="M25 13.899L18.7973 20.1017L25 26.3043L23.101 28.2033L15 20.101L23.101 12L25 13.899Z" fill="%23F36F22"/></svg>');
          background-position: center;
          background-repeat: no-repeat;
          font-size: 0;
          line-height: 0 }
      .wp-pagination.wp-pagination ul li a {
  display: flex;
  align-items: center;
  justify-content: center;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  font-size: clamp(18px, calc(18/1920*100rem), calc(18/1920*100rem));
  line-height: 1.33333 }
      @media (min-width: 1200px) {

  .wp-pagination.wp-pagination ul li a {
    font-size: 0.9375rem } }
      .wp-pagination.wp-pagination ul li a {
  font-weight: 700;
  text-transform: uppercase;
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
  height: 100%;
  width: 100%;
  border-radius: 0.4**************7rem;
  border-color: rgb(220 220 220 / var(--tw-border-opacity, 1));
  position: relative;
  z-index: 1;
  overflow: hidden;
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(14 107 56 / var(--tw-border-opacity, 1)) }
      .wp-pagination.wp-pagination ul li.disabled a {
  border-color: transparent;
  background-color: transparent }
      .wp-pagination.wp-pagination ul li.active:not(.disabled) a, .wp-pagination.wp-pagination ul li:hover:not(.disabled) a {
  --tw-bg-opacity: 1;
  background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
      .wp-pagination.wp-pagination ul li.active:not(.disabled).prev-page a, .wp-pagination.wp-pagination ul li.active:not(.disabled).next-page a, .wp-pagination.wp-pagination ul li:hover:not(.disabled).prev-page a, .wp-pagination.wp-pagination ul li:hover:not(.disabled).next-page a {
  background-color: rgb(14 107 56 / 0.3) }

.facetwp-type-pager[data-type="pager"] {
  margin-top: 2.083333333333333rem }
  .facetwp-type-pager[data-type="pager"] .facetwp-pager {
  display: flex;
  list-style-type: none;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: 0.625rem }
  .facetwp-type-pager[data-type="pager"] .facetwp-page {
    flex: 0 0 clamp(40px, calc(40/1920*100rem), calc(40/1920*100rem));
    width: clamp(40px, calc(40/1920*100rem), calc(40/1920*100rem));
    height: clamp(40px, calc(40/1920*100rem), calc(40/1920*100rem));
    display: flex;
    align-items: center;
    justify-content: center;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
    font-size: clamp(18px, calc(18/1920*100rem), calc(18/1920*100rem));
    line-height: 1.33333 }
  @media (min-width: 1200px) {

  .facetwp-type-pager[data-type="pager"] .facetwp-page {
    font-size: 0.9375rem } }
  .facetwp-type-pager[data-type="pager"] .facetwp-page {
  font-weight: 700;
  text-transform: uppercase;
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
  border-radius: 0.4**************7rem;
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(220 220 220 / var(--tw-border-opacity, 1));
  position: relative;
  z-index: 1;
  overflow: hidden;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none }
    .facetwp-type-pager[data-type="pager"] .facetwp-page.prev, .facetwp-type-pager[data-type="pager"] .facetwp-page.next {
  font-size: 0 }
      .facetwp-type-pager[data-type="pager"] .facetwp-page.prev::after, .facetwp-type-pager[data-type="pager"] .facetwp-page.next::after {
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.28571 }
      @media (min-width: 1200px) {

  .facetwp-type-pager[data-type="pager"] .facetwp-page.prev::after, .facetwp-type-pager[data-type="pager"] .facetwp-page.next::after {
    font-size: 0.7291666666666667rem } }
      .facetwp-type-pager[data-type="pager"] .facetwp-page.prev::after, .facetwp-type-pager[data-type="pager"] .facetwp-page.next::after {
  font-family: 'Font Awesome 6 Pro';
  font-weight: 300;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }
    .facetwp-type-pager[data-type="pager"] .facetwp-page.prev::after {
      content: '\f053'; }
    .facetwp-type-pager[data-type="pager"] .facetwp-page.next::after {
      content: '\f054'; }
    .facetwp-type-pager[data-type="pager"] .facetwp-page.active, .facetwp-type-pager[data-type="pager"] .facetwp-page:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
      .facetwp-type-pager[data-type="pager"] .facetwp-page.active.prev a, .facetwp-type-pager[data-type="pager"] .facetwp-page.active.next a, .facetwp-type-pager[data-type="pager"] .facetwp-page:hover.prev a, .facetwp-type-pager[data-type="pager"] .facetwp-page:hover.next a {
  background-color: rgb(14 107 56 / 0.1) }

.section {
  padding: 40px 0; }

@media (min-width: 768px) {
  .section {
    padding: 50px 0; } }

@media (min-width: 1200px) {
  .section {
    padding: 3.125rem 0 3.125rem 0; } }

.arrow-button {
  --data-length: 40px;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none }

@media (min-width: 1200px) {
  .arrow-button {
    --data-length: calc(48/1920*100rem); } }
  .arrow-button .button-prev {
    left: 0; }

@media (min-width: 1200px) {
  .arrow-button .button-prev {
    left: -4.583333333333333rem } }
    .arrow-button .button-prev::after {
      background-image: url(../img/slide/arrow-left.svg); }
  .arrow-button .button-next {
    right: 0; }

@media (min-width: 1200px) {
  .arrow-button .button-next {
    right: -4.583333333333333rem } }
    .arrow-button .button-next::after {
      background-image: url(../img/slide/arrow-right.svg); }
  .arrow-button.arrow-2 .button-prev::after {
    background-image: url(../img/slide/arrow-left-dark.svg); }
  .arrow-button.arrow-2 .button-next::after {
    background-image: url(../img/slide/arrow-right-dark.svg); }
  .arrow-button .button-prev:hover::after {
    background-image: url(../img/slide/arrow-left-hover.svg); }
  .arrow-button .button-next:hover::after {
    background-image: url(../img/slide/arrow-right-hover.svg); }
  @media (min-width: 1200px) {

  .arrow-button.inner-arrow .button-prev {
    left: 0.8333333333333334rem } }
  .arrow-button.inner-arrow .button-next {
  left: unset }
  @media (min-width: 1200px) {

  .arrow-button.inner-arrow .button-next {
    right: 0.8333333333333334rem } }
  .arrow-button.close-arrow {
  display: flex;
  align-items: center;
  gap: 0.625rem }
    .arrow-button.close-arrow .button-prev, .arrow-button.close-arrow .button-next {
  position: static;
  --tw-translate-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }

@media (max-width: 1199.98px) {
  .arrow-button.close-arrow .button-prev, .arrow-button.close-arrow .button-next {
    margin-top: 0;
    margin-left: 0 } }
  .arrow-button .button-prev, .arrow-button .button-next {
    margin: 0;
    top: 50%;
    transform: translateY(-50%);
    width: var(--data-length);
    height: var(--data-length);
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
    overflow: hidden;
    position: absolute;
    z-index: 3 }
  @media (hover: hover) and (pointer: fine) {

  .arrow-button .button-prev:hover, .arrow-button .button-next:hover {
    border-color: transparent } }
  .arrow-button .button-prev, .arrow-button .button-next {
  cursor: pointer }
    .arrow-button .button-prev.swiper-button-disabled, .arrow-button .button-next.swiper-button-disabled {
  pointer-events: none;
  opacity: 0.3 }
    .arrow-button .button-prev::after, .arrow-button .button-next::after {
      content: '';
      position: absolute;
      inset: 0;
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
      --tw-text-opacity: 1;
      color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
    @media (min-width: 1200px) {

  .arrow-button .button-prev::after, .arrow-button .button-next::after {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms } }

@media (max-width: 1199.98px) {
  .arrow-button {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative; }
    .arrow-button .button-prev, .arrow-button .button-next {
      margin-top: 24px;
      position: static;
      transform: translateY(0); }
    .arrow-button .button-next {
      margin-left: 8px; }
  .arrow-button.no-responsive {
    display: contents; }
    .arrow-button.no-responsive .button-prev, .arrow-button.no-responsive .button-next {
      position: absolute !important;
      margin: 0 !important;
      top: 50%;
      transform: translateY(-50%); }
    .arrow-button.no-responsive .button-prev {
      left: 0; }
    .arrow-button.no-responsive .button-next {
      right: 0; } }

.swiper-column-auto {
  --mr: 16px;
  --spv: 1; }

@media (min-width: 1200px) {
  .swiper-column-auto {
    --mr: calc(40/1920*100rem); } }
  .swiper-column-auto[data-time='0'] .swiper-wrapper {
    transition-timing-function: linear; }
  .swiper-column-auto .swiper-slide.swiper-slide {
  overflow: visible }
  .swiper-column-auto .swiper-slide {
    width: calc(calc(100% - calc(var(--mr) * calc(var(--spv) - 1)))/var(--spv));
    height: auto }
    .swiper-column-auto .swiper-slide:not(:last-child) {
      margin-right: var(--mr); }
  .swiper-column-auto.small-gap {
    --mr: calc(10/1920*100rem); }
  .swiper-column-auto.medium-gap {
    --mr: calc(24/1920*100rem); }

@media (min-width: 768px) {
  .swiper-column-auto.auto-2-column {
    --spv: 2; }
  .swiper-column-auto.auto-3-column {
    --spv: 2; } }

@media (min-width: 1024px) {
  .swiper-column-auto.auto-3-column {
    --spv: 3; } }

@media (min-width: 576px) {
  .swiper-column-auto.auto-4-column {
    --spv: 2; } }

@media (min-width: 768px) {
  .swiper-column-auto.auto-4-column {
    --spv: 3; } }

@media (min-width: 1200px) {
  .swiper-column-auto.auto-4-column {
    --spv: 4; } }
  .swiper-column-auto.auto-5-column {
    --spv: 1; }

@media (min-width: 576px) {
  .swiper-column-auto.auto-5-column {
    --spv: 2; } }

@media (min-width: 768px) {
  .swiper-column-auto.auto-5-column {
    --spv: 3; } }

@media (min-width: 1024px) {
  .swiper-column-auto.auto-5-column {
    --spv: 4; } }

@media (min-width: 1200px) {
  .swiper-column-auto.auto-5-column {
    --spv: 5; } }

@media (max-width: 575.98px) {
  .swiper-column-auto.show-half-mobile {
    --spv: 1.5; } }
  .swiper-column-auto .swiper-scrollbar.swiper-scrollbar {
  position: static;
  margin-top: 0.8333333333333334rem }
  @media (min-width: 1200px) {

  .swiper-column-auto .swiper-scrollbar.swiper-scrollbar {
    margin-top: 2.083333333333333rem } }
  .swiper-column-auto .swiper-scrollbar.swiper-scrollbar {
  width: 100%;
  border-radius: 0px;
  --tw-bg-opacity: 1;
  background-color: rgb(189 189 189 / var(--tw-bg-opacity, 1)) }
    .swiper-column-auto .swiper-scrollbar.swiper-scrollbar .swiper-scrollbar-drag {
  border-radius: 0px;
  --tw-bg-opacity: 1;
  background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1)) }
  .swiper-column-auto.arrow-edge .arrow-button .button-prev {
  left: 0 }
  .swiper-column-auto.arrow-edge .arrow-button .button-next {
  right: 0 }

.visible-slide .swiper {
  overflow: visible }

.allow-touchMove .swiper-slide {
  cursor: grab }

.swiper-pagination.swiper-pagination.swiper-pagination {
  position: static;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  width: -moz-fit-content;
  width: fit-content;
  justify-content: center;
  gap: 0.8333333333333334rem;
  border-radius: 0.20833333333333334rem;
  padding: 0.4**************7rem;
  align-items: center }
  .swiper-pagination.swiper-pagination.swiper-pagination.swiper-pagination-lock {
  padding: 0;
  padding-left: 0;
  padding-right: 0 }
  .swiper-pagination.swiper-pagination.swiper-pagination span {
  margin-left: 0;
  margin-right: 0;
  height: 4px;
  width: 16px;
  opacity: 1 }
  @media (min-width: 1200px) {

  .swiper-pagination.swiper-pagination.swiper-pagination span {
    height: 0.20833333333333334rem;
    width: 0.8333333333333334rem } }
  .swiper-pagination.swiper-pagination.swiper-pagination span {
  border-radius: 0.20833333333333334rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms;
  display: block;
  position: relative;
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  border-color: transparent;
  background-color: rgb(255 255 255 / 0.5) }
    .swiper-pagination.swiper-pagination.swiper-pagination span::before {
      content: '';
      position: absolute;
      inset: 1px;
      border-radius: 0.20833333333333334rem;
      --tw-bg-opacity: 1;
      background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1));
      transition-property: all;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 500ms;
      opacity: 0 }
    .swiper-pagination.swiper-pagination.swiper-pagination span.swiper-pagination-bullet-active {
  --tw-scale-x: 1.5;
  --tw-scale-y: 1.5;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1)) }
      .swiper-pagination.swiper-pagination.swiper-pagination span.swiper-pagination-bullet-active::before {
  opacity: 1 }

.swiper-column-1 .swiper-slide, .swiper-column-2 .swiper-slide, .swiper-xl-3 .swiper-slide, .swiper-xl-4 .swiper-slide {
  height: auto }

.swiper-center .swiper-wrapper {
  margin-left: auto;
  margin-right: auto;
  width: -moz-fit-content;
  width: fit-content }

@media (min-width: 1200px) {
  .swiper-shadow-spacing .swiper {
    padding: 1.66667rem;
    margin: -1.66667rem; } }

.swiper.swiper-fade .swiper-slide {
  opacity: 0 !important; }

.swiper.swiper-fade .swiper-slide-active {
  opacity: 1 !important; }

.dynamic-slide.swiper-slide {
  width: calc(calc(100% - calc(var(--mr) * calc(var(--spv) - 1)))/var(--spv));
  height: auto }
  .dynamic-slide.swiper-slide:not(:last-child) {
    margin-right: var(--mr); }

.block-title {
  font-size: 1.875rem;
  font-weight: 400;
  line-height: 1.33333;
  font-family: Anton, sans-serif;
  text-transform: uppercase;
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }
  .block-title span {
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1)) }

.small-block-title {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(41 41 41 / var(--tw-text-opacity, 1));
  font-size: 2.0833333333333335rem }

.ft-title {
  font-size: clamp(16px, calc(16/1920*100rem), calc(16/1920*100rem));
  line-height: 1.375 }

@media (min-width: 1200px) {

  .ft-title {
    font-size: 0.8333333333333334rem } }

.ft-title {
  margin-bottom: 1.0416666666666665rem;
  font-weight: 700 }

@media (min-width: 576px) {

  .ft-title {
    font-size: clamp(18px, calc(18/1920*100rem), calc(18/1920*100rem));
    line-height: 1.33333 }
  @media (min-width: 1200px) {

    .ft-title {
      font-size: 0.9375rem } } }

@media (min-width: 1200px) {

  .ft-title {
    font-size: 0.9375rem } }

html.lenis,
html.lenis body {
  height: auto; }

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain; }

.lenis.lenis-stopped {
  overflow: clip; }

.lenis.lenis-smooth iframe {
  pointer-events: none; }

/*!
 * Toastify js 1.12.0
 * https://github.com/apvarun/toastify-js
 * @license MIT licensed
 *
 * Copyright (C) 2018 Varun A P
 */
.toastify {
  padding: 12px 20px;
  color: #ffffff;
  display: inline-block;
  box-shadow: 0 3px 6px -1px rgba(0, 0, 0, 0.12), 0 10px 36px -4px rgba(77, 96, 232, 0.3);
  background: linear-gradient(135deg, #73a5ff, #5477f5);
  position: fixed;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
  border-radius: 2px;
  cursor: pointer;
  text-decoration: none;
  max-width: calc(50% - 20px);
  z-index: **********; }

.toastify.on {
  opacity: 1; }

.toast-close {
  background: transparent;
  border: 0;
  color: white;
  cursor: pointer;
  font-family: inherit;
  font-size: 1em;
  opacity: 0.4;
  padding: 0 5px; }

.toastify-right {
  right: 15px; }

.toastify-left {
  left: 15px; }

.toastify-top {
  top: -150px; }

.toastify-bottom {
  bottom: -150px; }

.toastify-rounded {
  border-radius: 25px; }

.toastify-avatar {
  width: 1.5em;
  height: 1.5em;
  margin: -7px 5px;
  border-radius: 2px; }

.toastify-center {
  margin-left: auto;
  margin-right: auto;
  left: 0;
  right: 0;
  max-width: fit-content;
  max-width: -moz-fit-content; }

@media only screen and (max-width: 360px) {
  .toastify-right, .toastify-left {
    margin-left: auto;
    margin-right: auto;
    left: 0;
    right: 0;
    max-width: -moz-fit-content;
    max-width: fit-content; } }

/* Functional styling;
 * These styles are required for noUiSlider to function.
 * You don't need to change these rules to apply your design.
 */
.noUi-target,
.noUi-target * {
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-user-select: none;
  touch-action: none;
  -moz-user-select: none;
  user-select: none;
  box-sizing: border-box; }

.noUi-target {
  position: relative; }

.noUi-base,
.noUi-connects {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1; }

/* Wrapper for all connect elements.
 */
.noUi-connects {
  overflow: hidden;
  z-index: 0; }

.noUi-connect,
.noUi-origin {
  will-change: transform;
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  height: 100%;
  width: 100%;
  -ms-transform-origin: 0 0;
  -webkit-transform-origin: 0 0;
  -webkit-transform-style: preserve-3d;
  transform-origin: 0 0;
  transform-style: flat; }

/* Offset direction
 */
.noUi-txt-dir-rtl.noUi-horizontal .noUi-origin {
  left: 0;
  right: auto; }

/* Give origins 0 height/width so they don't interfere with clicking the
 * connect elements.
 */
.noUi-vertical .noUi-origin {
  top: -100%;
  width: 0; }

.noUi-horizontal .noUi-origin {
  height: 0; }

.noUi-handle {
  backface-visibility: hidden;
  position: absolute; }

.noUi-touch-area {
  height: 100%;
  width: 100%; }

.noUi-state-tap .noUi-connect,
.noUi-state-tap .noUi-origin {
  transition: transform 0.3s; }

.noUi-state-drag * {
  cursor: inherit !important; }

/* Slider size and handle placement;
 */
.noUi-horizontal {
  height: 18px; }

.noUi-horizontal .noUi-handle {
  width: 34px;
  height: 28px;
  right: -17px;
  top: -6px; }

.noUi-vertical {
  width: 18px; }

.noUi-vertical .noUi-handle {
  width: 28px;
  height: 34px;
  right: -6px;
  bottom: -17px; }

.noUi-txt-dir-rtl.noUi-horizontal .noUi-handle {
  left: -17px;
  right: auto; }

/* Styling;
 * Giving the connect element a border radius causes issues with using transform: scale
 */
.noUi-target {
  background: #FAFAFA;
  border-radius: 4px;
  border: 1px solid #D3D3D3;
  box-shadow: inset 0 1px 1px #F0F0F0, 0 3px 6px -5px #BBB; }

.noUi-connects {
  border-radius: 3px; }

.noUi-connect {
  background: #3FB8AF; }

/* Handles and cursors;
 */
.noUi-draggable {
  cursor: ew-resize; }

.noUi-vertical .noUi-draggable {
  cursor: ns-resize; }

.noUi-handle {
  border: 1px solid #D9D9D9;
  border-radius: 3px;
  background: #FFF;
  cursor: default;
  box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #EBEBEB, 0 3px 6px -3px #BBB; }

.noUi-active {
  box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #DDD, 0 3px 6px -3px #BBB; }

/* Handle stripes;
 */
.noUi-handle:before,
.noUi-handle:after {
  content: "";
  display: block;
  position: absolute;
  height: 14px;
  width: 1px;
  background: #E8E7E6;
  left: 14px;
  top: 6px; }

.noUi-handle:after {
  left: 17px; }

.noUi-vertical .noUi-handle:before,
.noUi-vertical .noUi-handle:after {
  width: 14px;
  height: 1px;
  left: 6px;
  top: 14px; }

.noUi-vertical .noUi-handle:after {
  top: 17px; }

/* Disabled state;
 */
[disabled] .noUi-connect {
  background: #B8B8B8; }

[disabled].noUi-target,
[disabled].noUi-handle,
[disabled] .noUi-handle {
  cursor: not-allowed; }

/* Base;
 *
 */
.noUi-pips,
.noUi-pips * {
  box-sizing: border-box; }

.noUi-pips {
  position: absolute;
  color: #999; }

/* Values;
 *
 */
.noUi-value {
  position: absolute;
  white-space: nowrap;
  text-align: center; }

.noUi-value-sub {
  color: #ccc;
  font-size: 10px; }

/* Markings;
 *
 */
.noUi-marker {
  position: absolute;
  background: #CCC; }

.noUi-marker-sub {
  background: #AAA; }

.noUi-marker-large {
  background: #AAA; }

/* Horizontal layout;
 *
 */
.noUi-pips-horizontal {
  padding: 10px 0;
  height: 80px;
  top: 100%;
  left: 0;
  width: 100%; }

.noUi-value-horizontal {
  transform: translate(-50%, 50%); }

.noUi-rtl .noUi-value-horizontal {
  transform: translate(50%, 50%); }

.noUi-marker-horizontal.noUi-marker {
  margin-left: -1px;
  width: 2px;
  height: 5px; }

.noUi-marker-horizontal.noUi-marker-sub {
  height: 10px; }

.noUi-marker-horizontal.noUi-marker-large {
  height: 15px; }

/* Vertical layout;
 *
 */
.noUi-pips-vertical {
  padding: 0 10px;
  height: 100%;
  top: 0;
  left: 100%; }

.noUi-value-vertical {
  transform: translate(0, -50%);
  padding-left: 25px; }

.noUi-rtl .noUi-value-vertical {
  transform: translate(0, 50%); }

.noUi-marker-vertical.noUi-marker {
  width: 5px;
  height: 2px;
  margin-top: -1px; }

.noUi-marker-vertical.noUi-marker-sub {
  width: 10px; }

.noUi-marker-vertical.noUi-marker-large {
  width: 15px; }

.noUi-tooltip {
  display: block;
  position: absolute;
  border: 1px solid #D9D9D9;
  border-radius: 3px;
  background: #fff;
  color: #000;
  padding: 5px;
  text-align: center;
  white-space: nowrap; }

.noUi-horizontal .noUi-tooltip {
  transform: translate(-50%, 0);
  left: 50%;
  bottom: 120%; }

.noUi-vertical .noUi-tooltip {
  transform: translate(0, -50%);
  top: 50%;
  right: 120%; }

.noUi-horizontal .noUi-origin > .noUi-tooltip {
  transform: translate(50%, 0);
  left: auto;
  bottom: 10px; }

.noUi-vertical .noUi-origin > .noUi-tooltip {
  transform: translate(0, -18px);
  top: auto;
  right: 28px; }

#overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  pointer-events: none;
  z-index: 30;
  opacity: 0;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }
  #overlay.active {
  pointer-events: auto;
  opacity: 1 }

.desktop-show {
  display: none }

@media (min-width: 1200px) {

  .desktop-show {
    display: block } }

.mobile-show {
  display: block }

@media (min-width: 1200px) {

  .mobile-show {
    display: none } }

#fixed-tool {
  --gap: 0px;
  --icon-size: 32px;
  bottom: 1.25rem;
  right: 0;
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }

@media (min-width: 576px) {
  #fixed-tool {
    --icon-size: clamp(48px,calc(48/1920*100rem),calc(48/1920*100rem)); } }

@media (min-width: 1200px) {
  #fixed-tool {
    --icon-size: calc(48/1920*100rem); } }
  #fixed-tool li {
    transform: translateX(calc(100% - var(--icon-size)));
    background: linear-gradient(270deg, #0E6B38 0%, #0B8C45 100%), #0E6B38;
    margin-left: auto;
    width: -moz-fit-content;
    width: fit-content;
    display: flex;
    align-items: center;
    gap: var(--gap);
    pointer-events: auto;
    border-top-left-radius: 0.4**************7rem;
    border-bottom-left-radius: 0.4**************7rem;
    border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
    border-color: rgb(0 166 75 / var(--tw-border-opacity, 1));
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
    border-right-width: clamp(4px, calc(4/1920*100rem), calc(4/1920*100rem));
    --tw-border-opacity: 1;
    border-right-color: rgb(244 118 33 / var(--tw-border-opacity, 1)) }
    #fixed-tool li:hover {
  --tw-translate-x: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }
    #fixed-tool li .icon {
  width: var(--icon-size);
  height: var(--icon-size);
  border-top-left-radius: 0.4**************7rem;
  border-bottom-left-radius: 0.4**************7rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Font Awesome 6 Pro' }
      #fixed-tool li .icon img {
  width: 100%;
  height: 100%;
  border-radius: 9999px;
  -o-object-fit: cover;
     object-fit: cover;
  padding: 0.4**************7rem }
      #fixed-tool li .icon i {
  font-style: normal;
  font-size: 16px;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
      @media (min-width: 576px) {

  #fixed-tool li .icon i {
    font-size: clamp(24px, calc(24/1920*100rem), calc(24/1920*100rem)) } }
        #fixed-tool li .icon i:not([class*='fa-']) {
  font-weight: 900 }
    #fixed-tool li .content {
  padding-top: 0.20833333333333334rem;
  padding-bottom: 0.20833333333333334rem;
  padding-right: 0.4**************7rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
    #fixed-tool li.scrollToTop {
  pointer-events: none;
  --tw-translate-y: 0.8333333333333334rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 0;
  border-width: 0px }
      #fixed-tool li.scrollToTop .icon {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(189 189 189 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
      #fixed-tool li.scrollToTop.active {
  pointer-events: auto;
  --tw-translate-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 1 }
  #fixed-tool.list-item-added {
    padding-left: clamp(19px, calc(19/1920*100rem), calc(19/1920*100rem));
    list-style-type: disc }

.ul-check ul {
  list-style-type: none;
  padding: 0;
  padding-left: 0;
  padding-right: 0 }
  .ul-check ul li {
  display: flex;
  gap: 0.625rem }
    .ul-check ul li::before {
      content: "\f00c";
      font-family: 'Font Awesome 6 Pro' }

[data-toggle="tabslet"] .tabslet-tab li.active {
  pointer-events: none }

[data-toggle="tabslet"] .tab-content .tabslet-content, [data-toggle="tabslet"] .tab-content > [class*='tabslet-custom'], [data-toggle="tabslet"] .tab-content .tabslet-content-other, [data-toggle="tabslet"] [class*='tab-custom'] .tabslet-content, [data-toggle="tabslet"] [class*='tab-custom'] > [class*='tabslet-custom'], [data-toggle="tabslet"] [class*='tab-custom'] .tabslet-content-other {
  display: none }
  [data-toggle="tabslet"] .tab-content .tabslet-content:first-child, [data-toggle="tabslet"] .tab-content > [class*='tabslet-custom']:first-child, [data-toggle="tabslet"] .tab-content .tabslet-content-other:first-child, [data-toggle="tabslet"] [class*='tab-custom'] .tabslet-content:first-child, [data-toggle="tabslet"] [class*='tab-custom'] > [class*='tabslet-custom']:first-child, [data-toggle="tabslet"] [class*='tab-custom'] .tabslet-content-other:first-child {
  display: block }

.content-spacing * + * {
  margin-top: 1.0416666666666665rem }

.edit-link-post {
  width: clamp(20px, calc(20/1920*100rem), calc(20/1920*100rem));
  height: clamp(20px, calc(20/1920*100rem), calc(20/1920*100rem));
  pointer-events: auto;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 10 }
  .edit-link-post span {
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }

.edit-term-post {
  width: clamp(20px, calc(20/1920*100rem), calc(20/1920*100rem));
  height: clamp(20px, calc(20/1920*100rem), calc(20/1920*100rem));
  pointer-events: auto;
  display: inline-flex;
  align-items: center;
  justify-content: center }
  .edit-term-post span {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1)) }

.loading-overlay::before {
  content: '';
  z-index: 20;
  position: absolute;
  inset: 0 }

@keyframes pulse {

  50% {
    opacity: .5 } }

.loading-overlay::before {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  cursor: not-allowed;
  background-color: rgb(255 255 255 / 0.2);
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia) }

@keyframes pulse {

  50% {
    opacity: .5 } }

.loading-overlay::before {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite }

.loading-overlay .loading {
  background-image: url(../img/loading.svg);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 3.125rem;
  height: 3.125rem;
  --tw-translate-x: -50%;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  pointer-events: none;
  z-index: 2 }

.loading-overlay.medium .loading {
  width: 2.083333333333333rem;
  height: 2.083333333333333rem }

.image-svg img {
  opacity: 0 }

.image-svg .img-generate img {
  opacity: 1 }

.image-svg svg {
  height: 100%;
  width: 100% }

.image-svg.image-absolute .svg-generate {
  position: absolute;
  inset: 0;
  height: 100%;
  width: 100% }

.expander {
  display: grid;
  grid-template-rows: 0fr;
  overflow: hidden;
  transition: grid-template-rows .3s; }
  .expander .expander-content {
    min-height: 0;
    transition: visibility .3s;
    visibility: hidden; }

.image-not-found {
  font-size: clamp(12px, calc(12/1920*100rem), calc(12/1920*100rem));
  line-height: 1.5;
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1)) }

.play-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 2;
  --tw-translate-x: -50%;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }
  .play-btn .icon {
  top: 50%;
  left: 50%;
  z-index: 2;
  --tw-translate-x: -50%;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  position: absolute;
  width: 50px;
  height: 50px }
    @media (min-width: 768px) {
      .play-btn .icon {
    width: 60px;
    height: 60px } }

@media (min-width: 1200px) {
  .play-btn .icon {
    width: 4.**************7rem;
    height: 4.**************7rem } }
    .play-btn .icon a {
      position: relative;
      display: block;
      height: 0;
      overflow: hidden;
      padding-top: 100%;
      overflow: visible }
      .play-btn .icon a img, .play-btn .icon a iframe, .play-btn .icon a video {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        -o-object-fit: cover;
           object-fit: cover;
        transition-property: all;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 150ms }
    .play-btn .icon svg {
  overflow: visible }
    .play-btn .icon rect {
      transform-origin: center;
      transform-box: fill-box;
      transition-property: all;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 150ms }
      .play-btn .icon rect:nth-of-type(1) {
  position: relative;
  z-index: 2 }
      .play-btn .icon rect:nth-of-type(2) {
        animation: ping 1.3s ease-in-out infinite both; }
      .play-btn .icon rect:nth-of-type(3) {
        animation: ping 1.3s .3s ease-in-out infinite both; }

@keyframes pathMove {
  0% {
    stroke-dashoffset: var(--data-stroke-dasharray);
    stroke-dasharray: var(--data-stroke-dasharray); }
  100% {
    stroke-dasharray: 0;
    stroke-dashoffset: var(--data-stroke-dasharray); } }
    .play-btn .icon .pause {
  display: none }
  .play-btn:hover .icon {
  --tw-scale-x: .9;
  --tw-scale-y: .9;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }
    .play-btn:hover .icon path {
  fill: rgb(255 255 255 / 0.5) }

[class*='ratio-'] {
  position: relative;
  display: block;
  height: 0;
  overflow: hidden }
  [class*='ratio-'] img, [class*='ratio-'] iframe, [class*='ratio-'] video, [class*='ratio-'] .ratio-frame, [class*='ratio-'] picture {
    -o-object-fit: cover;
       object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100% }
  [class*='ratio-']:not(.no-transition) img, [class*='ratio-']:not(.no-transition) iframe, [class*='ratio-']:not(.no-transition) video, [class*='ratio-']:not(.no-transition) .ratio-frame, [class*='ratio-']:not(.no-transition) picture {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }
  [class*='ratio-'].ratio-contain img, [class*='ratio-'].ratio-contain video, [class*='ratio-'].ratio-contain picture {
  -o-object-fit: contain;
     object-fit: contain }
  [class*='ratio-'] iframe {
    -o-object-fit: none !important;
       object-fit: none !important; }

[class*='line-clamp-'] {
  overflow-wrap: break-word }

.flow-form-booking {
  pointer-events: none;
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 0 }
  .flow-form-booking.active {
  pointer-events: auto;
  --tw-translate-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 1 }

img.lozad {
  opacity: 0 }
  img.lozad[data-loaded] {
  opacity: 1 }

[data-tabslet] [class*='tabslet-content'] {
  display: none }

.ovh {
  overflow: hidden }

.overflow-auto, .overflow-scroll {
  overscroll-behavior: contain;
  transform: translate3d(0, 0, 0);
  will-change: transform; }

.toastify {
  border-radius: 9999px;
  font-size: clamp(18px, calc(20/1920*100rem), calc(20/1920*100rem));
  line-height: 1.6;
  padding-top: 0.4**************7rem;
  padding-bottom: 0.4**************7rem;
  padding-left: 0.625rem;
  padding-right: 0.625rem }
  .toastify .toastify-content {
  display: flex;
  align-items: center;
  gap: 1.0416666666666665rem }
  .toastify .toastify-icon {
  display: flex;
  width: 1.25rem;
  height: 1.25rem;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  flex: none }
    .toastify .toastify-icon::before {
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.28571 }
    @media (min-width: 1200px) {

  .toastify .toastify-icon::before {
    font-size: 0.7291666666666667rem } }
    .toastify .toastify-icon::before {
  font-family: 'Font Awesome 6 Pro' }
  .toastify.success {
    background: #0E6B38;
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
    .toastify.success .toastify-icon::before {
      content: '\f00c';
      --tw-text-opacity: 1;
      color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }
  .toastify.warning {
    background: #FFBF00;
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
    .toastify.warning .toastify-icon::before {
      content: '\f071';
      --tw-text-opacity: 1;
      color: rgb(255 191 0 / var(--tw-text-opacity, 1)) }
  .toastify.error {
    background: #ef4444;
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
    .toastify.error .toastify-icon::before {
      content: '\f00d';
      --tw-text-opacity: 1;
      color: rgb(239 68 68 / var(--tw-text-opacity, 1)) }
  .toastify.info {
    background: #3b82f6;
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
    .toastify.info .toastify-icon::before {
      content: '\f05a';
      --tw-text-opacity: 1;
      color: rgb(59 130 246 / var(--tw-text-opacity, 1)) }

[tab-wrapper]:not(.tab-wrapper-initialized) [tab-content] {
  display: none }
  [tab-wrapper]:not(.tab-wrapper-initialized) [tab-content]:first-child {
  display: block }

[tab-wrapper].tab-wrapper-initialized [tab-content] {
  display: none }

.clearfix::before, .clearfix::after {
  content: '';
  clear: both;
  display: block }

.animejs-onscroll-debug {
  z-index: 200 !important }

.hover-helper {
  display: none }

@media (min-width: 1200px) {

  .hover-helper {
    display: block } }

.home .stars i, .home .block-title, .home .description, .home .item, .home .btn, .home .col-right, .home .col-left, .home .bpd-1 {
  will-change: transform, opacity; }

.btn-search {
  cursor: pointer }

#ez-toc-container[class*='ez-toc-container'] {
  width: 100% }

.table-of-content > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.8333333333333334rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.8333333333333334rem * var(--tw-space-y-reverse)) }
  .table-of-content .js-toc {
  flex: 1 1 0% }
    @media (max-width: 575.98px) {
      .table-of-content .js-toc {
    width: 100% } }
  .table-of-content .title {
  font-size: clamp(18px, calc(18/1920*100rem), calc(18/1920*100rem));
  line-height: 1.33333 }
  @media (min-width: 1200px) {

  .table-of-content .title {
    font-size: 0.9375rem } }
  .table-of-content .title {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }
  .table-of-content .toc-list a {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  font-size: clamp(16px, calc(16/1920*100rem), calc(16/1920*100rem));
  line-height: 1.375 }
  @media (min-width: 1200px) {

  .table-of-content .toc-list a {
    font-size: 0.8333333333333334rem } }
  .table-of-content .toc-list a {
  font-weight: 500;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  --tw-text-opacity: 1;
  color: rgb(0 168 229 / var(--tw-text-opacity, 1)) }
    .table-of-content .toc-list a:hover {
  padding-left: 0.20833333333333334rem;
  padding-right: 0.625rem;
  text-decoration-line: underline }

.aligncenter,
div.aligncenter {
  display: block;
  margin-left: auto !important;
  margin-right: auto !important; }

a img.alignleft {
  margin: 5px 20px 20px 0; }

a img.aligncenter {
  display: block;
  margin-left: auto !important;
  margin-right: auto !important; }

.wp-caption {
  background: #fff;
  max-width: 100%;
  text-align: center; }
  .wp-caption img {
    border: 0 none;
    height: auto;
    margin: 0;
    max-width: 98.5%;
    padding: 0;
    width: auto; }
  .wp-caption p.wp-caption-text {
    font-size: 11px;
    line-height: 17px;
    margin: 0;
    padding: 0 4px 5px; }

.screen-reader-text {
  border: 0;
  clip: rect(1px, 1px, 1px, 1px);
  clip-path: inset(50%);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute !important;
  width: 1px;
  word-wrap: normal !important; }
  .screen-reader-text:focus {
    background-color: #eee;
    clip: auto !important;
    clip-path: none;
    color: #444;
    display: block;
    font-size: 1em;
    height: auto;
    left: 5px;
    line-height: normal;
    padding: 15px 23px 14px;
    text-decoration: none;
    top: 5px;
    width: auto;
    z-index: 100000; }

[class*='bn-'] .title {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }

[class*='bn-']:hover .title {
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1)) }

.bpd-1 .img a::before {
  content: '';
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.1) 100%);
  position: absolute;
  inset: 0;
  z-index: 1 }

.bpd-1 .wrapper::before {
  content: '';
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 100%);
  position: absolute;
  inset: 0;
  z-index: -1;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }

.bpd-1 .wrapper::after {
  content: '';
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 100%);
  height: calc(480/240*100%);
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: -1;
  width: 100%;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }

.bpd-1:hover .btn-light {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  --tw-border-opacity: 1;
  border-color: rgb(14 107 56 / var(--tw-border-opacity, 1));
  background-color: transparent }
  .bpd-1:hover .btn-light::before {
  opacity: 1 }

.product-cat-1 .content {
  min-height: 50%; }
  .product-cat-1 .content::before {
    content: '';
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.75) 100%);
    position: absolute;
    inset: 0;
    z-index: -1 }

.bpd-2 {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }
  .bpd-2 .buttons {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 0.5208333333333334rem }
    .bpd-2 .buttons .btn {
  width: 100%;
  height: clamp(40px, calc(48/1920*100rem), calc(48/1920*100rem)) }
  .bpd-2:hover {
    box-shadow: 0.20833rem 0.20833rem 1.66667rem 0 rgba(0, 0, 0, 0.08);
    --tw-border-opacity: 1;
    border-color: rgb(14 107 56 / var(--tw-border-opacity, 1)) }
    .bpd-2:hover .title {
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }

.favorite i {
  --tw-text-opacity: 1;
  color: rgb(247 0 4 / var(--tw-text-opacity, 1));
  cursor: pointer;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }

@keyframes spin {

  to {
    transform: rotate(360deg) } }

.favorite.loading i {
  animation: spin 1s linear infinite }
  .favorite.loading i::before {
    content: '\e62a' }
  @keyframes pulse {

  50% {
    opacity: .5 } }
  .favorite.loading i::before {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite }

.favorite.is-favorite i {
  font-weight: 700 }

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:   }

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:   }

/*! tailwindcss v3.4.15 | MIT License | https://tailwindcss.com
 */

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #BBBBBB; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #777777; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #777777; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

html {
    scroll-behavior: initial; }

select {
    background-image: url('data:image/svg+xml,<svg width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10.8496 1.8125L6.09961 6.28125C5.94336 6.4375 5.75586 6.5 5.59961 6.5C5.41211 6.5 5.22461 6.4375 5.06836 6.3125L0.318359 1.8125C0.00585938 1.53125 0.00585938 1.0625 0.287109 0.75C0.568359 0.4375 1.03711 0.4375 1.34961 0.71875L5.59961 4.71875L9.81836 0.71875C10.1309 0.4375 10.5996 0.4375 10.8809 0.75C11.1621 1.0625 11.1621 1.53125 10.8496 1.8125Z" fill="%230E6B38"/></svg>');
    background-size: clamp(12px, calc(12/1920*100rem), calc(12/1920*100rem)) clamp(7px, calc(7/1920*100rem), calc(7/1920*100rem));
    background-position: center right clamp(16px, calc(16/1920*100rem), calc(16/1920*100rem));
    -webkit-appearance: none;
       -moz-appearance: none;
            appearance: none;
    background-repeat: no-repeat;
    padding-left: 0.8333333333333334rem;
    padding-right: 1.6666666666666667rem }

select option {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(41 41 41 / var(--tw-text-opacity, 1)) }

.\!description th, .\!description td {
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(220 220 220 / var(--tw-border-opacity, 1));
  padding: 0.20833333333333334rem }

.description th, .description td, .content th, .content td, .ctn th, .ctn td {
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(220 220 220 / var(--tw-border-opacity, 1));
  padding: 0.20833333333333334rem }

.\!description th, .\!description td {
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(220 220 220 / var(--tw-border-opacity, 1));
  padding: 0.20833333333333334rem }

 .\!content th, .\!content td {
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(220 220 220 / var(--tw-border-opacity, 1));
  padding: 0.20833333333333334rem }

*,
  *::before,
  *::after {
    box-sizing: border-box; }

*::-webkit-scrollbar-track {
  border-radius: 0px;
  --tw-bg-opacity: 1;
  background-color: rgb(220 220 220 / var(--tw-bg-opacity, 1)) }

*::-webkit-scrollbar-thumb {
    border-radius: clamp(2px, calc(2/1920*100rem), calc(2/1920*100rem));
    border-radius: 0px;
    --tw-bg-opacity: 1;
    background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1)) }

*:focus-within, *:focus-visible {
  outline-width: 0px }

html {
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    --padding-left: calc(15/1920*100rem);
    --padding-right: calc(15/1920*100rem); }

@media (min-width: 1200px) {
    html {
      --padding-left: calc(100/1920*100rem);
      --padding-right: calc(100/1920*100rem); } }

body {
    font-optical-sizing: auto;
    font-family: Inter, sans-serif;
    --tw-text-opacity: 1;
    color: rgb(41 41 41 / var(--tw-text-opacity, 1));
    font-size: clamp(16px, calc(16/1920*100rem), calc(16/1920*100rem));
    line-height: 1.375 }

@media (min-width: 1200px) {

  body {
    font-size: 0.8333333333333334rem } }

body {
  font-weight: 400 }

img {
    display: inline; }

main {
  margin-top: 70px }

@media (min-width: 1200px) {
    main {
    margin-top: 5.208333333333334rem } }

::-moz-range-track {
    background: black;
    border: 0; }

input::-moz-focus-inner,
  input::-moz-focus-outer {
    border: 0; }.prose {
  color: var(--tw-prose-body);
  max-width: unset; }.prose :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em }.prose :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-lead);
  font-size: 1.25em;
  line-height: 1.6;
  margin-top: 1.2em;
  margin-bottom: 1.2em }.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #0000EE;
  text-decoration: underline;
  font-weight: 500; }.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)):hover {
  color: #EE0000 }.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)):visited {
  color: #551A8B }.prose :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-weight: 700 }.prose :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit }.prose :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit }.prose :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit }.prose :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em }.prose :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha }.prose :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha }.prose :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha }.prose :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha }.prose :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman }.prose :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman }.prose :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman }.prose :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman }.prose :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal }.prose :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: disc;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
  padding-left: 1.25rem; }.prose :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) li {
  padding-left: 0;
  margin: 0 0; }.prose :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) li::marker {
  color: #292929 }.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  font-weight: 400;
  color: var(--tw-prose-counters) }.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  color: var(--tw-prose-bullets) }.prose :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.25em }.prose :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-color: var(--tw-prose-hr);
  border-top-width: 1px;
  margin-top: 3em;
  margin-bottom: 3em }.prose :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-style: normal;
  color: var(--tw-prose-quotes);
  border-inline-start-width: 0.25rem;
  border-inline-start-color: #0E6B38;
  quotes: "\201C""\201D""\2018""\2019";
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-inline-start: 1em;
  background-color: #f7f0e7;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem }.prose :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: open-quote }.prose :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: close-quote }.prose :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111 }.prose :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 900;
  color: inherit }.prose :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 700;
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333 }.prose :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 800;
  color: inherit }.prose :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.6 }.prose :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit }.prose :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.5 }.prose :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit }.prose :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em }.prose :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  display: block;
  margin-top: 2em;
  margin-bottom: 2em }.prose :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em }.prose :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-family: inherit;
  color: var(--tw-prose-kbd);
  box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);
  font-size: 0.875em;
  border-radius: 0.3125rem;
  padding-top: 0.1875em;
  padding-inline-end: 0.375em;
  padding-bottom: 0.1875em;
  padding-inline-start: 0.375em }.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-code);
  font-weight: 600;
  font-size: 0.875em }.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: "`" }.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: "`" }.prose :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit }.prose :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit }.prose :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.875em }.prose :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.9em }.prose :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit }.prose :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit }.prose :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit }.prose :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-pre-code);
  background-color: var(--tw-prose-pre-bg);
  overflow-x: auto;
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding-top: 0.8571429em;
  padding-inline-end: 1.1428571em;
  padding-bottom: 0.8571429em;
  padding-inline-start: 1.1428571em }.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  background-color: transparent;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-weight: inherit;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit }.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: none }.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: none }.prose :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  width: 100%;
  table-layout: auto;
  margin-top: 2em;
  margin-bottom: 2em;
  font-size: 0.875em;
  line-height: 1.7142857; }.prose :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) td {
  border: thin solid #e8e8e8;
  padding: 0.5rem }.prose :where(thead):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-th-borders) }.prose :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  vertical-align: bottom;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em }.prose :where(tbody tr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-td-borders) }.prose :where(tbody tr:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 0 }.prose :where(tbody td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: baseline }.prose :where(tfoot):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-top-width: 1px;
  border-top-color: var(--tw-prose-th-borders) }.prose :where(tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: top }.prose :where(th, td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  text-align: start }.prose :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0 }.prose :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-captions);
  font-size: 0.78125rem;
  line-height: 1.4285714;
  margin-top: 0.8571429em }.prose {
  --tw-prose-body: inherit;
  --tw-prose-headings: #111827;
  --tw-prose-lead: #4b5563;
  --tw-prose-links: #111827;
  --tw-prose-bold: #111827;
  --tw-prose-counters: #6b7280;
  --tw-prose-bullets: #d1d5db;
  --tw-prose-hr: #e5e7eb;
  --tw-prose-quotes: #111827;
  --tw-prose-quote-borders: #e5e7eb;
  --tw-prose-captions: #6b7280;
  --tw-prose-kbd: #111827;
  --tw-prose-kbd-shadows: 17 24 39;
  --tw-prose-code: #111827;
  --tw-prose-pre-code: #e5e7eb;
  --tw-prose-pre-bg: #1f2937;
  --tw-prose-th-borders: #d1d5db;
  --tw-prose-td-borders: #e5e7eb;
  --tw-prose-invert-body: #d1d5db;
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: #9ca3af;
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: #9ca3af;
  --tw-prose-invert-bullets: #4b5563;
  --tw-prose-invert-hr: #374151;
  --tw-prose-invert-quotes: #f3f4f6;
  --tw-prose-invert-quote-borders: #374151;
  --tw-prose-invert-captions: #9ca3af;
  --tw-prose-invert-kbd: #fff;
  --tw-prose-invert-kbd-shadows: 255 255 255;
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: #d1d5db;
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: #4b5563;
  --tw-prose-invert-td-borders: #374151;
  font-size: inherit;
  line-height: inherit }.prose :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0 }.prose :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em }.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em }.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em }.prose :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em }.prose :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em }.prose :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em }.prose :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em }.prose :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em }.prose :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em }.prose :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em }.prose :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  padding-inline-start: 1.625em }.prose :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0 }.prose :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0 }.prose :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0 }.prose :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0 }.prose :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0 }.prose :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0 }.prose :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.5714286em;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em }.prose :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0 }.prose :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0 }.prose :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em }.prose :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0 }.prose :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0 }.prose :where(h1,h2,h3,h4,h5,h6):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.0416666666666665rem;
  font-weight: 700;
  line-height: 1.3; }@media (min-width: 1200px) {

  .prose :where(h1,h2,h3,h4,h5,h6):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.0416666666666665rem } }.prose :where(*):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin: 0.8333333333333334rem 0 }.prose :where(.prose > *:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0 }.prose :where(.prose > *:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0 }.prose :where(div):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin: 0.8333333333333334rem 0 }.prose {
  margin: 0 }.base-gap {
  gap: 24px; }@media (min-width: 1200px) {

  .base-gap {
    gap: 2.083333333333333rem } }.allow-touchMove {
  cursor: grab }.body-14 {
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.28571 }.body-16 {
  font-size: clamp(16px, calc(16/1920*100rem), calc(16/1920*100rem));
  line-height: 1.375 }.body-18 {
  font-size: clamp(18px, calc(18/1920*100rem), calc(18/1920*100rem));
  line-height: 1.33333 }.label-12 {
  font-size: clamp(12px, calc(12/1920*100rem), calc(12/1920*100rem));
  line-height: 1.33333 }.subheader-20 {
  font-size: clamp(20px, calc(20/1920*100rem), calc(20/1920*100rem));
  line-height: 1.3 }.regular-20 {
  font-size: clamp(18px, calc(20/1920*100rem), calc(20/1920*100rem));
  line-height: 1.4 }.subheader-24 {
  font-size: clamp(22px, calc(24/1920*100rem), calc(24/1920*100rem));
  line-height: 1.25 }.header-24 {
  font-size: clamp(22px, calc(24/1920*100rem), calc(24/1920*100rem));
  line-height: 1.41667 }.header-28 {
  font-size: 1.**************33rem;
  line-height: 1.28571 }.header-32 {
  font-size: 1.6666666666666667rem;
  line-height: 1.25 }.header-36 {
  font-size: 1.875rem;
  line-height: 1.33333 }.header-40 {
  font-size: 2.083333333333333rem;
  line-height: 1.33333 }.header-48 {
  font-size: 2.5rem;
  line-height: 1.25 }.accordion-item .top i {
  font-size: clamp(22px, calc(24/1920*100rem), calc(24/1920*100rem));
  line-height: 1.25 }@media (min-width: 1200px) {

  .accordion-item .top i {
    font-size: 1.25rem } }.accordion-item .top i {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms;
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center }.accordion-item .top i:nth-of-type(2) {
  position: absolute;
  inset: 0;
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 0 }.accordion-item.active {
  --tw-border-opacity: 1;
  border-color: rgb(14 107 56 / var(--tw-border-opacity, 1)) }.accordion-item.active .top i:nth-of-type(1) {
  --tw-scale-x: .75;
  --tw-scale-y: .75;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 0 }.accordion-item.active .top i:nth-of-type(2) {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 1 }.module-toggle[class*="module-split"] {
  gap: 24px; }@media (min-width: 1200px) {

  .module-toggle[class*="module-split"] {
    gap: 2.083333333333333rem } }.module-toggle[class*="module-split"] {
  display: grid }@media (min-width: 1024px) {

  .module-toggle[class*="module-split"][data-split="2"] {
    grid-template-columns: repeat(2, minmax(0, 1fr)) }

  .module-toggle[class*="module-split"][data-split="3"] {
    grid-template-columns: repeat(3, minmax(0, 1fr)) }

  .module-toggle[class*="module-split"][data-split="4"] {
    grid-template-columns: repeat(4, minmax(0, 1fr)) } }.custom-scrollbar::-webkit-scrollbar {
    width: clamp(4px, calc(4/1920*100rem), calc(4/1920*100rem));
    height: clamp(4px, calc(4/1920*100rem), calc(4/1920*100rem)); }.custom-scrollbar::-webkit-scrollbar-track {
  --tw-bg-opacity: 1;
  background-color: rgb(239 239 239 / var(--tw-bg-opacity, 1)) }.custom-scrollbar::-webkit-scrollbar-thumb {
  --tw-bg-opacity: 1;
  background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1)) }.custom-scrollbar.color-2::-webkit-scrollbar-track {
  --tw-bg-opacity: 1;
  background-color: rgb(217 217 217 / var(--tw-bg-opacity, 1)) }.keep-ul-disc ul, .description ul, .content ul, .ctn ul {
  list-style-type: disc;
  padding-left: 1.25rem } .\!description ul {
  list-style-type: disc;
  padding-left: 1.25rem } .\!content ul {
  list-style-type: disc;
  padding-left: 1.25rem }.keep-ul-disc ul li::marker, .description ul li::marker, .content ul li::marker, .ctn ul li::marker {
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.28571 }@media (min-width: 1200px) {

  .keep-ul-disc ul li::marker, .description ul li::marker, .content ul li::marker, .ctn ul li::marker {
    font-size: 0.7291666666666667rem } } .\!description ul li::marker {
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.28571 }@media (min-width: 1200px) {

   .\!description ul li::marker {
    font-size: 0.7291666666666667rem } } .\!content ul li::marker {
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.28571 }@media (min-width: 1200px) {

   .\!content ul li::marker {
    font-size: 0.7291666666666667rem } }.primary-nav {
  display: flex;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  justify-content: center }.primary-nav ul {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  overflow: auto;
  white-space: nowrap;
  overscroll-behavior: contain;
  transform: translate3d(0, 0, 0);
  will-change: transform }.primary-nav ul::-webkit-scrollbar {
        height: 0; }.primary-nav a {
  --tw-text-opacity: 1;
  color: rgb(129 129 129 / var(--tw-text-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-top: 0.4**************7rem;
  padding-bottom: 0.4**************7rem;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  display: block;
  border-radius: 0.20833333333333334rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(220 220 220 / var(--tw-border-opacity, 1));
  font-weight: 700 }.primary-nav li.active a, .primary-nav li:hover a {
  --tw-border-opacity: 1;
  border-color: rgb(244 118 33 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1)) }.primary-nav li.active a {
  pointer-events: none }.background-image {
  position: absolute;
  inset: 0 }.background-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover }.simple-prose * + * {
  margin-top: 1.25rem }.simple-prose h2, .simple-prose h3, .simple-prose h4, .simple-prose h5 {
  font-size: clamp(20px, calc(20/1920*100rem), calc(20/1920*100rem));
  line-height: 1.3 }@media (min-width: 1200px) {

  .simple-prose h2, .simple-prose h3, .simple-prose h4, .simple-prose h5 {
    font-size: 1.0416666666666667rem } }.simple-prose h2, .simple-prose h3, .simple-prose h4, .simple-prose h5 {
  font-weight: 700 }.simple-prose ul {
  list-style-type: disc;
  padding-left: 1.**************33rem }.simple-prose ul li::marker {
  font-size: clamp(12px, calc(12/1920*100rem), calc(12/1920*100rem)) }.simple-prose ul li + li {
  margin-top: 0.625rem }.simple-prose.ul-gap-5 li + li {
  margin-top: 1.0416666666666665rem }.secondary-nav {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none }.secondary-nav ul {
  display: flex;
  align-items: center;
  gap: 1.0416666666666665rem;
  overflow: auto;
  white-space: nowrap;
  overscroll-behavior: contain;
  transform: translate3d(0, 0, 0);
  will-change: transform }.secondary-nav li {
  font-size: clamp(20px, calc(20/1920*100rem), calc(20/1920*100rem));
  line-height: 1.3 }@media (min-width: 1200px) {

  .secondary-nav li {
    font-size: 1.0416666666666667rem } }.secondary-nav li {
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(41 41 41 / var(--tw-text-opacity, 1));
  padding-top: 0.20833333333333334rem;
  padding-bottom: 0.20833333333333334rem;
  padding-left: 1.0416666666666665rem;
  padding-right: 1.0416666666666665rem;
  border-radius: 9999px;
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(220 220 220 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(246 246 246 / var(--tw-bg-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }.secondary-nav li.active, .secondary-nav li:hover {
  --tw-border-opacity: 1;
  border-color: rgb(189 189 189 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));        @aply bg-neutral-300 {} }.layout-support .col-left li i {
  font-size: clamp(16px, calc(16/1920*100rem), calc(16/1920*100rem));
  line-height: 1.375 }@media (min-width: 1200px) {

  .layout-support .col-left li i {
    font-size: 0.8333333333333334rem } }.layout-support .col-left li.active i::before, .layout-support .col-left li:hover i::before {
    content: '\f068'; }.social-icon {
  display: flex;
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center }@media (min-width: 1200px) {

  .social-icon {
    width: 32px;
    height: 32px } }@media (min-width: 1440px) {

  .social-icon {
    width: 2.0833333333333335rem;
    height: 2.0833333333333335rem } }.social-icon i {
  font-size: clamp(14px, calc(16/1920*100rem), calc(16/1920*100rem));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }.social-icon-2 {
  width: 2.083333333333333rem;
  height: 2.083333333333333rem;
  border-radius: 0.20833333333333334rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  display: flex;
  align-items: center;
  justify-content: center;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }.social-icon-2 i {
  font-size: clamp(16px, calc(16/1920*100rem), calc(16/1920*100rem));
  line-height: 1.375 }@media (min-width: 1200px) {

  .social-icon-2 i {
    font-size: 0.8333333333333334rem } }.social-icon-2 i {
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }.social-icon-2:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(244 118 33 / var(--tw-bg-opacity, 1)) }.social-icon-2:hover i {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }@media (min-width: 1200px) {
    .highlight-bottom-wrapper:hover .highlight-bottom::before {
    height: 100% }
    .highlight-bottom-wrapper:hover .highlight-bottom i {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1)) } }.highlight-bottom {
  border-bottom-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(14 107 56 / var(--tw-border-opacity, 1)) }@media (min-width: 1200px) {

  .highlight-bottom {
    margin-bottom: clamp(-1px, calc(-1/1920*100rem), calc(-1/1920*100rem)) } }.highlight-bottom {
  position: relative }.highlight-bottom::before {
      content: '';
      bottom: 0;
      left: 0;
      height: 0;
      width: 100%;
      --tw-bg-opacity: 1;
      background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1));
      position: absolute;
      z-index: -1;
      transition-property: all;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 150ms }@media (min-width: 1200px) {
    .highlight-bottom:hover::before {
    height: 100% }
    .highlight-bottom:hover i {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1)) } }.btn-auth {
  display: flex;
  align-items: center;
  gap: 0.4**************7rem;
  width: 6.25rem;
  justify-content: center;
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }.page-link {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.4**************7rem;
  white-space: nowrap;
  --tw-bg-opacity: 1;
  background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }.page-link:hover, .page-link[class*='current'] {
  --tw-bg-opacity: 1;
  background-color: rgb(244 118 33 / var(--tw-bg-opacity, 1)) }.page-link i {
  font-size: 0.8333333333333334rem;
  line-height: 1.375 }.checkbox-value .selected-text-result {
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  display: block;
  height: 100% }.checkbox-value .selected-text-result .selected-result-text-wrapper {
  position: absolute;
  display: flex;
  align-items: center;
  inset: 0;
  overflow: auto;
  overscroll-behavior: contain;
  transform: translate3d(0, 0, 0);
  will-change: transform }.checkbox-value .selected-text-result .selected-result-text-wrapper span.result-text {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1)) }.checkbox-value .selected-text-result + span {
  display: none }.checkbox-filter {
  position: relative;
  z-index: 1;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none }.checkbox-filter:not([id*='-address']) .checkbox-list {
  display: flex;
  max-height: 40vh;
  flex-direction: column }.checkbox-filter:not([id*='-address']) .checkbox-list .checkbox-list-wrapper {
  flex: 1 1 0%;
  overflow: auto;
  overscroll-behavior: contain;
  transform: translate3d(0, 0, 0);
  will-change: transform }@media (max-width: 575.98px) {
    .checkbox-filter {
    font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
    line-height: 1.28571 }
  @media (min-width: 1200px) {

    .checkbox-filter {
      font-size: 0.7291666666666667rem } } }.checkbox-filter.active {
  z-index: 10 }.checkbox-filter.active .checkbox-value i {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }.checkbox-filter .checkbox-value {
  display: flex;
  align-items: center;
  border-radius: 0.20833333333333334rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  gap: 0.4**************7rem;
  height: clamp(42px, calc(42/1920*100rem), calc(42/1920*100rem));
  cursor: pointer;
  padding-left: 0.8333333333333334rem;
  padding-right: 0.8333333333333334rem }.checkbox-filter .checkbox-value span {
  flex: 1 1 0% }.checkbox-filter .checkbox-value .result-text {
  flex: none }.checkbox-filter .checkbox-list-wrapper > :not([hidden]) ~ :not([hidden]), .checkbox-filter .inner-checkbox-wrapper > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.625rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.625rem * var(--tw-space-y-reverse)) }.checkbox-filter .checkbox-item {
  display: flex;
  align-items: baseline;
  min-height: clamp(40px, calc(40/1920*100rem), calc(40/1920*100rem));
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(239 239 239 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
  border-radius: 0.20833333333333334rem;
  gap: 0.625rem;
  padding-left: clamp(8px, calc(8/1920*100rem), calc(8/1920*100rem));
  padding-right: clamp(8px, calc(8/1920*100rem), calc(8/1920*100rem));
  padding-top: clamp(8px, calc(8/1920*100rem), calc(8/1920*100rem));
  padding-bottom: clamp(8px, calc(8/1920*100rem), calc(8/1920*100rem));
  cursor: pointer;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }.checkbox-filter .checkbox-item.loading {
  pointer-events: none }@keyframes pulse {

  50% {
    opacity: .5 } }.checkbox-filter .checkbox-item.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite }.checkbox-filter .checkbox-item.loading::before {
          content: '\f110' }@keyframes spin {

  to {
    transform: rotate(360deg) } }.checkbox-filter .checkbox-item.loading::before {
  animation: spin 1s linear infinite;
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1)) }.checkbox-filter .checkbox-item[disabled] {
  opacity: 0.5;
  pointer-events: none }.checkbox-filter .checkbox-item[disabled]::before {
          content: '\f05e'; }.checkbox-filter .checkbox-item[disabled] .inner-checkbox-list {
  display: none }.checkbox-filter .checkbox-item[disabled] > span {
  text-decoration-line: line-through }.checkbox-filter .checkbox-item::before {
        content: '\f0c8';
        font-family: 'Font Awesome 6 Pro';
        transition-property: all;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 150ms }.checkbox-filter .checkbox-item.active:not(.select-category) {
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1)) }.checkbox-filter .checkbox-item.active:not(.select-category)::before {
          content: '\f14a';
          font-weight: 700 }.checkbox-filter .checkbox-item.radio::before {
        content: '\f111'; }.checkbox-filter .checkbox-item.radio.active::before {
        content: '\f058'; }.checkbox-filter .select-category {
  flex-direction: row-reverse }.checkbox-filter .select-category span {
  flex: 1 1 auto }.checkbox-filter .select-category::before {
        content: '\f105'; }.checkbox-filter .clear-checkbox {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  margin-top: 0.625rem;
  --tw-text-opacity: 1;
  color: rgb(189 189 189 / var(--tw-text-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }.checkbox-filter .clear-checkbox:hover {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1)) }.checkbox-filter .clear-checkbox:active {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1)) }.checkbox-filter .clear-checkbox::before {
        content: '\f00d';
        font-family: 'Font Awesome 6 Pro' }.checkbox-filter .back-to-previous {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  margin-bottom: 0.625rem;
  border-radius: 0.20833333333333334rem;
  --tw-bg-opacity: 1;
  background-color: rgb(246 246 246 / var(--tw-bg-opacity, 1));
  height: clamp(40px, calc(40/1920*100rem), calc(40/1920*100rem));
  padding-left: clamp(8px, calc(8/1920*100rem), calc(8/1920*100rem));
  padding-right: clamp(8px, calc(8/1920*100rem), calc(8/1920*100rem)) }.checkbox-filter .back-to-previous:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 239 239 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }.checkbox-filter .back-to-previous:active {
  color: rgb(14 107 56 / 0.7) }.checkbox-filter .back-to-previous::before {
        content: '\f104';
        font-family: 'Font Awesome 6 Pro' }.checkbox-filter .checkbox-list {
  overflow: hidden }.checkbox-filter .checkbox-list, .checkbox-filter .inner-checkbox-list {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 0.20833333333333334rem;
  z-index: 1;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 1.0416666666666665rem;
  border-radius: 0.20833333333333334rem;
  --tw-shadow: 0.20833333333333334rem 0.20833333333333334rem 1.6666666666666667rem 0 rgba(0, 0, 0, 0.08);
  --tw-shadow-colored: 0.20833333333333334rem 0.20833333333333334rem 1.6666666666666667rem 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  pointer-events: none;
  opacity: 0;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  width: 100% }@media (min-width: 1200px) {

  .checkbox-filter .checkbox-list, .checkbox-filter .inner-checkbox-list {
    width: clamp(320px, calc(320/1920*100rem), calc(320/1920*100rem)) }

  .checkbox-filter .checkbox-list.size-medium, .checkbox-filter .inner-checkbox-list.size-medium {
    width: clamp(260px, calc(260/1920*100rem), calc(260/1920*100rem)) } }.checkbox-filter .checkbox-list.active, .checkbox-filter .inner-checkbox-list.active {
  opacity: 1 }.checkbox-filter .checkbox-list.active {
  pointer-events: auto }.checkbox-filter .checkbox-list.active .inner-checkbox-list.active {
  pointer-events: auto }.checkbox-filter .inner-checkbox-list {
  top: 0;
  margin-top: 0;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 100%;
  visibility: hidden;
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }.checkbox-filter .inner-checkbox-list.active {
  visibility: visible;
  --tw-translate-x: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }.checkbox-filter .inner-checkbox-wrapper {
  flex: 1 1 0%;
  overflow: auto;
  overscroll-behavior: contain;
  transform: translate3d(0, 0, 0);
  will-change: transform }.checkbox-filter .notification {
  margin-bottom: 0.625rem }.price-range {
  position: relative }.price-range .inputs {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 0.625rem }.price-range input {
  display: block;
  width: 100%;
  border-radius: 0.20833333333333334rem;
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(239 239 239 / var(--tw-border-opacity, 1));
  text-align: center;
  height: clamp(40px, calc(40/1920*100rem), calc(40/1920*100rem)) }.price-range input::-webkit-outer-spin-button, .price-range input::-webkit-inner-spin-button {
  -webkit-appearance: none;
          appearance: none }.price-range .range-slider {
  position: relative;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: clamp(6px, calc(6/1920*100rem), calc(6/1920*100rem));
  padding-right: clamp(6px, calc(6/1920*100rem), calc(6/1920*100rem)) }.price-range .range-slider::after {
        content: '';
        position: absolute;
        --tw-bg-opacity: 1;
        background-color: rgb(220 220 220 / var(--tw-bg-opacity, 1));
        height: clamp(2px, calc(2/1920*100rem), calc(2/1920*100rem));
        width: 100%;
        top: 50%;
        left: 0;
        --tw-translate-y: -50%;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }.price-range .range-slider-item {
  height: clamp(12px, calc(12/1920*100rem), calc(12/1920*100rem));
  border-radius: 0px;
  border-style: none;
  background-color: transparent;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) }.price-range .noUi-base {
  display: flex;
  align-items: center }.price-range .noUi-connects {
  --tw-bg-opacity: 1;
  background-color: rgb(220 220 220 / var(--tw-bg-opacity, 1));
  height: clamp(2px, calc(2/1920*100rem), calc(2/1920*100rem)) }.price-range .noUi-connect {
  --tw-bg-opacity: 1;
  background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1)) }.price-range .noUi-origin {
  top: 50%;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }.price-range .noUi-handle {
      box-shadow: none;
      border-radius: 9999px;
      border-style: none;
      --tw-bg-opacity: 1;
      background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1));
      width: clamp(12px, calc(12/1920*100rem), calc(12/1920*100rem));
      height: clamp(12px, calc(12/1920*100rem), calc(12/1920*100rem));
      top: 50%;
      --tw-translate-y: -50%;
      transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      right: clamp(-6px, calc(-6/1920*100rem), calc(-6/1920*100rem)) }.price-range .noUi-handle::before, .price-range .noUi-handle::after {
  display: none }.notification {
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.28571 }@media (min-width: 1200px) {

  .notification {
    font-size: 0.7291666666666667rem } }.notification {
  font-weight: 500 }.notification-warning {
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1)) }@media (min-width: 1200px) {

  .body-14 {
    font-size: 0.7291666666666667rem }

  .body-16 {
    font-size: 0.8333333333333334rem }

  .body-18 {
    font-size: 0.9375rem }

  .subheader-20 {
    font-size: 1.0416666666666667rem }

  .subheader-24 {
    font-size: 1.25rem } }.wrap-top-nav {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 1.25rem }@media (min-width: 1200px) {

  .wrap-top-nav {
    flex-wrap: nowrap } }.wrap-top-nav .primary-nav {
  width: 100% }@media (min-width: 1024px) {

  .wrap-top-nav .primary-nav {
    width: auto;
    flex: 1 1 0% } }.wrap-top-nav .primary-nav {
  justify-content: flex-end;
  overflow: hidden }@media (max-width: 1023.98px) {
    .wrap-top-nav .primary-nav {
    justify-content: flex-start } }.sticky-nav {
  border-bottom-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(239 239 239 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1)) }.sticky-nav nav {
  width: 100%;
  position: relative;
  white-space: nowrap;
  margin-bottom: clamp(-1px, calc(-1/1920*100rem), calc(-1/1920*100rem)) }.sticky-nav nav .scroll-prev, .sticky-nav nav .scroll-next {
  position: absolute;
  top: 50%;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  cursor: pointer;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }.sticky-nav nav .scroll-prev:hover, .sticky-nav nav .scroll-next:hover {
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1)) }.sticky-nav nav .scroll-prev[disabled], .sticky-nav nav .scroll-next[disabled] {
  pointer-events: none;
  opacity: 0.1 }@media (max-width: 1199.98px) {
    .sticky-nav nav .scroll-prev, .sticky-nav nav .scroll-next {
    display: none !important } }.sticky-nav nav .scroll-prev {
  left: 0 }@media (min-width: 1200px) {

  .sticky-nav nav .scroll-prev {
    left: -1.0416666666666665rem } }.sticky-nav nav .scroll-next {
  right: 0 }@media (min-width: 1200px) {

  .sticky-nav nav .scroll-next {
    right: -1.0416666666666665rem } }.sticky-nav nav ul {
  display: flex;
  justify-content: space-between;
  gap: 1.0416666666666665rem;
  overflow: auto;
  overscroll-behavior: contain;
  transform: translate3d(0, 0, 0);
  will-change: transform;
  padding-bottom: clamp(2px, calc(2/1920*100rem), calc(2/1920*100rem)) }.sticky-nav nav ul::-webkit-scrollbar {
          height: 0; }.sticky-nav nav ul li {
  width: -moz-fit-content;
  width: fit-content;
  flex: 1 1 auto }.sticky-nav nav ul li.active a, .sticky-nav nav ul li:hover a {
  --tw-border-opacity: 1;
  border-color: rgb(244 118 33 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1)) }.sticky-nav nav a {
  display: block;
  min-height: clamp(40px, calc(40/1920*100rem), calc(40/1920*100rem));
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 0.20833333333333334rem;
  padding-bottom: 0.20833333333333334rem;
  text-align: center;
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
  border-bottom-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  border-color: transparent;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }.filter-nav .checkbox-filter {
  width: 50% }@media (min-width: 576px) {

  .filter-nav .checkbox-filter {
    width: 33.333333% } }.filter-nav .checkbox-filter {
  flex: 1 1 auto }@media (min-width: 1024px) {

  .filter-nav .checkbox-filter {
    width: calc(187.8 / 1080 * 100%);
    flex: 1 1 0% } }.filter-nav .filter-wrapper {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  row-gap: 0.625rem;
  margin-left: -0.3125rem;
  margin-right: -0.3125rem }@media (min-width: 1200px) {
    .filter-nav .filter-wrapper {
    width: 100%;
    flex: 1 1 0% } }@media (max-width: 1199.98px) {
    .filter-nav .filter-wrapper {
    flex: 1 1 0% } }.filter-nav .filter-wrapper > div {
  padding-left: 0.3125rem;
  padding-right: 0.3125rem }@media (min-width: 1200px) {

  .filter-nav .search-filter {
    width: calc(320 / 1400 * 100%) } }@media (max-width: 1199.98px) {
    .filter-nav .search-filter {
    width: 100% } }.filter-nav .button-submit .btn {
  height: 100% }@media (max-width: 1023.98px) {
    .filter-nav .button-submit {
    width: 33.333333% }
      .filter-nav .button-submit .btn {
    width: 100% } }@media (max-width: 575.98px) {
    .filter-nav .button-submit {
    width: 50% } }.filter-nav .wrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.625rem }.filter-nav .wrapper .search, .filter-nav .wrapper .checkbox-value {
  border-bottom-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  border-color: rgb(14 107 56 / 0.5);
  height: clamp(48px, calc(48/1920*100rem), calc(48/1920*100rem));
  white-space: nowrap }.filter-nav .wrapper .search > span:not(.selected-text-result), .filter-nav .wrapper .checkbox-value > span:not(.selected-text-result) {
  flex: 1 1 0%;
  overflow: auto;
  overscroll-behavior: contain;
  transform: translate3d(0, 0, 0);
  will-change: transform }@media (min-width: 1200px) {

  .filter-nav .wrapper .search > span:not(.selected-text-result), .filter-nav .wrapper .checkbox-value > span:not(.selected-text-result) {
    font-size: clamp(14px, calc(16/1920*100rem), calc(16/1920*100rem)) } }.filter-nav .checkbox-value span:not(.selected-text-result) {
  --tw-text-opacity: 1;
  color: rgb(129 129 129 / var(--tw-text-opacity, 1)) }.filter-nav .search {
  padding-left: 0;
  padding-right: 0;
  display: flex;
  border-radius: 0.20833333333333334rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1)) }.filter-nav .search input {
  flex: 1 1 0%;
  background-color: transparent;
  padding-left: 0.8333333333333334rem;
  padding-right: 0.4**************7rem;
  width: 100% }.filter-nav .search input::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(129 129 129 / var(--tw-text-opacity, 1)) }.filter-nav .search input::placeholder {
  --tw-text-opacity: 1;
  color: rgb(129 129 129 / var(--tw-text-opacity, 1)) }@media (min-width: 1200px) {

  .filter-nav .search input {
    font-size: clamp(14px, calc(16/1920*100rem), calc(16/1920*100rem)) } }.filter-nav .search button {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: clamp(32px, calc(32/1920*100rem), calc(32/1920*100rem));
  pointer-events: none }.filter-nav .search button:hover i {
  font-weight: 700 }.filter-nav .search button i {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }.rate-page {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  position: relative;
  z-index: 5 }.rate-page .wrapper {
  position: relative }.rate-page i {
  cursor: pointer }.rate-page .new-stars-wrapper {
  position: absolute;
  left: 0;
  top: 50%;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  pointer-events: none }.rate-page .new-stars-wrapper i {
  position: absolute }.home .rate-page {
  background-color: rgb(14 107 56 / 0.05) }.wrap-show-content.expandable .show-content {
    -webkit-mask: linear-gradient(0deg, rgba(217, 217, 217, 0) 0%, #ffffff 127.39%, rgba(255, 255, 255, 0.94) 78.78%, rgba(255, 255, 255, 0.5) 100%);
            mask: linear-gradient(0deg, rgba(217, 217, 217, 0) 0%, #ffffff 127.39%, rgba(255, 255, 255, 0.94) 78.78%, rgba(255, 255, 255, 0.5) 100%); }.wrap-show-content.toggle-content .show-content {
    -webkit-mask: none;
            mask: none;
    max-height: unset }.hover-a-tag a {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }.hover-a-tag a:hover {
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1)) }.pointer-events-none {
  pointer-events: none }.pointer-events-auto {
  pointer-events: auto }.visible {
  visibility: visible }.fixed {
  position: fixed }.absolute {
  position: absolute }.relative {
  position: relative }.sticky {
  position: sticky }.inset-0 {
  inset: 0 }.-left-18 {
  left: -3.75rem }.bottom-0 {
  bottom: 0 }.left-0 {
  left: 0 }.left-1\/2 {
  left: 50% }.left-\[calc\(-80\/680\*100\%\)\] {
  left: calc(-80 / 680 * 100%) }.right-0 {
  right: 0 }.right-5 {
  right: 1.0416666666666665rem }.right-full {
  right: 100% }.top-0 {
  top: 0 }.top-1\/2 {
  top: 50% }.top-20 {
  top: 4.**************6rem }.top-28 {
  top: 5.833333333333333rem }.top-\[calc\(104\/600\*100\%\)\] {
  top: calc(104 / 600 * 100%) }.top-\[calc\(80\/800\*100\%\)\] {
  top: calc(80 / 800 * 100%) }.top-full {
  top: 100% }.z-1 {
  z-index: 1 }.z-2 {
  z-index: 2 }.z-3 {
  z-index: 3 }.z-5 {
  z-index: 5 }.z-\[100\] {
  z-index: 100 }.z-\[150\] {
  z-index: 150 }.z-\[158\] {
  z-index: 158 }.z-\[160\] {
  z-index: 160 }.z-\[90\] {
  z-index: 90 }.order-2 {
  order: 2 }.col-span-12 {
  grid-column: span 12 / span 12 }.col-span-full {
  grid-column: 1 / -1 }.-mx-5 {
  margin-left: -1.0416666666666665rem;
  margin-right: -1.0416666666666665rem }.mx-auto {
  margin-left: auto;
  margin-right: auto }.my-2 {
  margin-top: 0.4**************7rem;
  margin-bottom: 0.4**************7rem }.mb-6 {
  margin-bottom: 1.25rem }.ml-auto {
  margin-left: auto }.mr-2 {
  margin-right: 0.4**************7rem }.mt-1 {
  margin-top: 0.20833333333333334rem }.mt-1\.5 {
  margin-top: 0.3125rem }.mt-10 {
  margin-top: 2.083333333333333rem }.mt-11 {
  margin-top: 2.2916666666666665rem }.mt-2 {
  margin-top: 0.4**************7rem }.mt-2\.5 {
  margin-top: 0.5208333333333333rem }.mt-25 {
  margin-top: 5.208333333333334rem }.mt-3 {
  margin-top: 0.625rem }.mt-4 {
  margin-top: 0.8333333333333334rem }.mt-5 {
  margin-top: 1.0416666666666665rem }.mt-6 {
  margin-top: 1.25rem }.mt-8 {
  margin-top: 1.6666666666666667rem }.box-content {
  box-sizing: content-box }.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1 }.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2 }.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3 }.block {
  display: block }.inline {
  display: inline }.\!flex {
  display: flex !important }.flex {
  display: flex }.table {
  display: table }.grid {
  display: grid }.contents {
  display: contents }.hidden {
  display: none }.size-10 {
  width: 2.083333333333333rem;
  height: 2.083333333333333rem }.size-12 {
  width: 2.5rem;
  height: 2.5rem }.size-15 {
  width: 3.125rem;
  height: 3.125rem }.size-8 {
  width: 1.6666666666666667rem;
  height: 1.6666666666666667rem }.size-\[40px\] {
  width: 40px;
  height: 40px }.h-0\.5 {
  height: 0.10416666666666667rem }.h-full {
  height: 100% }.w-1\/2 {
  width: 50% }.w-12 {
  width: 2.5rem }.w-16 {
  width: 3.3333333333333335rem }.w-7 {
  width: 1.**************33rem }.w-\[calc\(104\/1920\*100\%\)\] {
  width: calc(104 / 1920 * 100%) }.w-\[calc\(240\/680\*100\%\)\] {
  width: calc(240 / 680 * 100%) }.w-fit {
  width: -moz-fit-content;
  width: fit-content }.w-full {
  width: 100% }.max-w-\[1100px\] {
  max-width: 1100px }.flex-1 {
  flex: 1 1 0% }.flex-auto {
  flex: 1 1 auto }.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }.cursor-not-allowed {
  cursor: not-allowed }.cursor-pointer {
  cursor: pointer }.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none }.select-all {
  -webkit-user-select: all;
     -moz-user-select: all;
          user-select: all }.resize {
  resize: both }.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr)) }.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr)) }.grid-cols-\[calc\(240\/640\*100\%\)_1fr\] {
  grid-template-columns: calc(240 / 640 * 100%) 1fr }.flex-col {
  flex-direction: column }.flex-wrap {
  flex-wrap: wrap }.items-end {
  align-items: flex-end }.items-center {
  align-items: center }.items-baseline {
  align-items: baseline }.justify-start {
  justify-content: flex-start }.justify-end {
  justify-content: flex-end }.justify-center {
  justify-content: center }.justify-between {
  justify-content: space-between }.gap-1 {
  gap: 0.20833333333333334rem }.gap-2 {
  gap: 0.4**************7rem }.gap-2\.5 {
  gap: 0.5208333333333333rem }.gap-3 {
  gap: 0.625rem }.gap-4 {
  gap: 0.8333333333333334rem }.gap-5 {
  gap: 1.0416666666666665rem }.gap-6 {
  gap: 1.25rem }.gap-8 {
  gap: 1.6666666666666667rem }.gap-x-11 {
  -moz-column-gap: 2.2916666666666665rem;
       column-gap: 2.2916666666666665rem }.gap-x-2 {
  -moz-column-gap: 0.4**************7rem;
       column-gap: 0.4**************7rem }.gap-x-6 {
  -moz-column-gap: 1.25rem;
       column-gap: 1.25rem }.gap-y-0 {
  row-gap: 0 }.gap-y-2 {
  row-gap: 0.4**************7rem }.gap-y-4 {
  row-gap: 0.8333333333333334rem }.gap-y-5 {
  row-gap: 1.0416666666666665rem }.space-y-10 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2.083333333333333rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2.083333333333333rem * var(--tw-space-y-reverse)) }.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.4**************7rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.4**************7rem * var(--tw-space-y-reverse)) }.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.625rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.625rem * var(--tw-space-y-reverse)) }.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.8333333333333334rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.8333333333333334rem * var(--tw-space-y-reverse)) }.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.0416666666666665rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.0416666666666665rem * var(--tw-space-y-reverse)) }.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse)) }.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.6666666666666667rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.6666666666666667rem * var(--tw-space-y-reverse)) }.space-y-\[24px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(24px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(24px * var(--tw-space-y-reverse)) }.self-end {
  align-self: flex-end }.self-center {
  align-self: center }.overflow-auto {
  overflow: auto }.overflow-hidden {
  overflow: hidden }.overflow-x-hidden {
  overflow-x: hidden }.rounded-1 {
  border-radius: 0.20833333333333334rem }.rounded-2 {
  border-radius: 0.4**************7rem }.rounded-3 {
  border-radius: 0.625rem }.rounded-4 {
  border-radius: 0.8333333333333334rem }.rounded-full {
  border-radius: 9999px }.rounded-l-6 {
  border-top-left-radius: 1.25rem;
  border-bottom-left-radius: 1.25rem }.rounded-br-4 {
  border-bottom-right-radius: 0.8333333333333334rem }.rounded-tl-4 {
  border-top-left-radius: 0.8333333333333334rem }.border {
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem)) }.border-2 {
  border-width: clamp(2px, calc(2/1920*100rem), calc(2/1920*100rem)) }.border-x {
  border-left-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  border-right-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem)) }.border-y {
  border-top-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  border-bottom-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem)) }.border-b {
  border-bottom-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem)) }.border-b-4 {
  border-bottom-width: clamp(4px, calc(4/1920*100rem), calc(4/1920*100rem)) }.border-t {
  border-top-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem)) }.border-neutral-100 {
  --tw-border-opacity: 1;
  border-color: rgb(239 239 239 / var(--tw-border-opacity, 1)) }.border-neutral-200 {
  --tw-border-opacity: 1;
  border-color: rgb(220 220 220 / var(--tw-border-opacity, 1)) }.border-primary-1 {
  --tw-border-opacity: 1;
  border-color: rgb(14 107 56 / var(--tw-border-opacity, 1)) }.border-secondary-5\/80 {
  border-color: rgb(229 205 150 / 0.8) }.border-transparent {
  border-color: transparent }.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1)) }.border-white\/30 {
  border-color: rgb(255 255 255 / 0.3) }.border-white\/40 {
  border-color: rgb(255 255 255 / 0.4) }.border-white\/50 {
  border-color: rgb(255 255 255 / 0.5) }.border-b-transparent {
  border-bottom-color: transparent }.border-t-neutral-200 {
  --tw-border-opacity: 1;
  border-top-color: rgb(220 220 220 / var(--tw-border-opacity, 1)) }.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1)) }.bg-neutral-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 239 239 / var(--tw-bg-opacity, 1)) }.bg-neutral-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 220 220 / var(--tw-bg-opacity, 1)) }.bg-neutral-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(189 189 189 / var(--tw-bg-opacity, 1)) }.bg-neutral-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(246 246 246 / var(--tw-bg-opacity, 1)) }.bg-neutral-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(129 129 129 / var(--tw-bg-opacity, 1)) }.bg-primary-1 {
  --tw-bg-opacity: 1;
  background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1)) }.bg-primary-1\/5 {
  background-color: rgb(14 107 56 / 0.05) }.bg-primary-1\/80 {
  background-color: rgb(14 107 56 / 0.8) }.bg-primary-2 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 118 33 / var(--tw-bg-opacity, 1)) }.bg-transparent {
  background-color: transparent }.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1)) }.to-neutral-950 {
  --tw-gradient-to: #292929 var(--tw-gradient-to-position) }.p-1 {
  padding: 0.20833333333333334rem }.p-10 {
  padding: 2.083333333333333rem }.p-3 {
  padding: 0.625rem }.p-4 {
  padding: 0.8333333333333334rem }.p-5 {
  padding: 1.0416666666666665rem }.p-6 {
  padding: 1.25rem }.px-1 {
  padding-left: 0.20833333333333334rem;
  padding-right: 0.20833333333333334rem }.px-2 {
  padding-left: 0.4**************7rem;
  padding-right: 0.4**************7rem }.px-4 {
  padding-left: 0.8333333333333334rem;
  padding-right: 0.8333333333333334rem }.px-5 {
  padding-left: 1.0416666666666665rem;
  padding-right: 1.0416666666666665rem }.px-6 {
  padding-left: 1.25rem;
  padding-right: 1.25rem }.py-1 {
  padding-top: 0.20833333333333334rem;
  padding-bottom: 0.20833333333333334rem }.py-10 {
  padding-top: 2.083333333333333rem;
  padding-bottom: 2.083333333333333rem }.py-2 {
  padding-top: 0.4**************7rem;
  padding-bottom: 0.4**************7rem }.py-3 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem }.py-4 {
  padding-top: 0.8333333333333334rem;
  padding-bottom: 0.8333333333333334rem }.py-5 {
  padding-top: 1.0416666666666665rem;
  padding-bottom: 1.0416666666666665rem }.py-5\.5 {
  padding-top: 1.1**************3rem;
  padding-bottom: 1.1**************3rem }.py-6 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem }.py-7 {
  padding-top: 1.**************33rem;
  padding-bottom: 1.**************33rem }.py-8 {
  padding-top: 1.6666666666666667rem;
  padding-bottom: 1.6666666666666667rem }.pb-0 {
  padding-bottom: 0 }.pb-12 {
  padding-bottom: 2.5rem }.pb-4 {
  padding-bottom: 0.8333333333333334rem }.pb-5 {
  padding-bottom: 1.0416666666666665rem }.pb-6 {
  padding-bottom: 1.25rem }.pb-8 {
  padding-bottom: 1.6666666666666667rem }.pb-\[24px\] {
  padding-bottom: 24px }.pl-20 {
  padding-left: 4.**************6rem }.pr-4 {
  padding-right: 0.8333333333333334rem }.pr-8 {
  padding-right: 1.6666666666666667rem }.pt-0 {
  padding-top: 0 }.pt-10 {
  padding-top: 2.083333333333333rem }.pt-2 {
  padding-top: 0.4**************7rem }.pt-3 {
  padding-top: 0.625rem }.pt-4 {
  padding-top: 0.8333333333333334rem }.pt-5 {
  padding-top: 1.0416666666666665rem }.text-left {
  text-align: left }.text-center {
  text-align: center }.text-justify {
  text-align: justify }.align-top {
  vertical-align: top }.font-heading {
  font-family: Anton, sans-serif }.text-\[2rem\] {
  font-size: 2rem }.text-\[5rem\] {
  font-size: 5rem }.font-black {
  font-weight: 900 }.font-bold {
  font-weight: 700 }.font-extrabold {
  font-weight: 800 }.font-extralight {
  font-weight: 200 }.font-medium {
  font-weight: 500 }.uppercase {
  text-transform: uppercase }.normal-case {
  text-transform: none }.leading-\[1\.27273\] {
  line-height: 1.27273 }.leading-\[1\.2\] {
  line-height: 1.2 }.leading-\[1\.41667\] {
  line-height: 1.41667 }.leading-none {
  line-height: 1 }.leading-normal {
  line-height: normal }.text-\[\#F6C100\] {
  --tw-text-opacity: 1;
  color: rgb(246 193 0 / var(--tw-text-opacity, 1)) }.text-\[\#FFBF00\] {
  --tw-text-opacity: 1;
  color: rgb(255 191 0 / var(--tw-text-opacity, 1)) }.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1)) }.text-neutral-200 {
  --tw-text-opacity: 1;
  color: rgb(220 220 220 / var(--tw-text-opacity, 1)) }.text-neutral-400 {
  --tw-text-opacity: 1;
  color: rgb(152 152 152 / var(--tw-text-opacity, 1)) }.text-neutral-500 {
  --tw-text-opacity: 1;
  color: rgb(129 129 129 / var(--tw-text-opacity, 1)) }.text-neutral-600 {
  --tw-text-opacity: 1;
  color: rgb(101 101 101 / var(--tw-text-opacity, 1)) }.text-neutral-700 {
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1)) }.text-neutral-800 {
  --tw-text-opacity: 1;
  color: rgb(70 70 70 / var(--tw-text-opacity, 1)) }.text-neutral-900 {
  --tw-text-opacity: 1;
  color: rgb(61 61 61 / var(--tw-text-opacity, 1)) }.text-neutral-950 {
  --tw-text-opacity: 1;
  color: rgb(41 41 41 / var(--tw-text-opacity, 1)) }.text-primary-1 {
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }.text-primary-2 {
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1)) }.text-secondary-5 {
  --tw-text-opacity: 1;
  color: rgb(229 205 150 / var(--tw-text-opacity, 1)) }.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }.underline {
  text-decoration-line: underline }.opacity-0 {
  opacity: 0 }.opacity-100 {
  opacity: 1 }.mix-blend-multiply {
  mix-blend-mode: multiply }.shadow-light {
  --tw-shadow: 0.20833333333333334rem 0.20833333333333334rem 1.6666666666666667rem 0 rgba(0, 0, 0, 0.08);
  --tw-shadow-colored: 0.20833333333333334rem 0.20833333333333334rem 1.6666666666666667rem 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) }.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) }.shadow-medium {
  --tw-shadow: 0.20833333333333334rem 0.4**************7rem 0.4**************7rem 0.20833333333333334rem rgba(0,0,0,0.12);
  --tw-shadow-colored: 0.20833333333333334rem 0.4**************7rem 0.4**************7rem 0.20833333333333334rem var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) }.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) }.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }.duration-300 {
  transition-duration: 300ms }.duration-500 {
  transition-duration: 500ms }.max-w-clamp-600px {
  max-width: clamp(600px, calc(600/1920*100rem), calc(600/1920*100rem)) }.ratio-\[1\/1\] {
  padding-top: 100% }.ratio-\[112\/200\] {
  padding-top: 56.00000000000001% }.ratio-\[160\/240\] {
  padding-top: 66.66666666666666% }.ratio-\[18\/28\] {
  padding-top: 64.28571428571429% }.ratio-\[180\/240\] {
  padding-top: 75% }.ratio-\[208\/320\] {
  padding-top: 65% }.ratio-\[223\/335\] {
  padding-top: 66.56716417910448% }.ratio-\[234\/360\] {
  padding-top: 65% }.ratio-\[248\/440\] {
  padding-top: 56.36363636363636% }.ratio-\[282\/424\] {
  padding-top: 66.50943396226415% }.ratio-\[287\/430\] {
  padding-top: 66.74418604651163% }.ratio-\[293\/440\] {
  padding-top: 66.5909090909091% }.ratio-\[300\/360\] {
  padding-top: 83.33333333333334% }.ratio-\[304\/180\] {
  padding-top: 168.88888888888889% }.ratio-\[330\/560\] {
  padding-top: 58.92857142857143% }.ratio-\[360\/420\] {
  padding-top: 85.71428571428571% }.ratio-\[372\/320\] {
  padding-top: 116.25000000000001% }.ratio-\[380\/640\] {
  padding-top: 59.375% }.ratio-\[382\/680\] {
  padding-top: 56.1764705882353% }.ratio-\[400\/680\] {
  padding-top: 58.82352941176471% }.ratio-\[44\/104\] {
  padding-top: 42.30769230769231% }.ratio-\[440\/660\] {
  padding-top: 66.66666666666666% }.ratio-\[45\/235\] {
  padding-top: 19.148936170212767% }.ratio-\[450\/600\] {
  padding-top: 75% }.ratio-\[466\/690\] {
  padding-top: 67.53623188405797% }.ratio-\[520\/960\] {
  padding-top: 54.**************4% }.ratio-\[560\/1920\] {
  padding-top: 29.**************8% }.ratio-\[570\/720\] {
  padding-top: 79.**************% }.ratio-\[600\/400\] {
  padding-top: 150% }.ratio-\[604\/820\] {
  padding-top: 73.**************% }.ratio-\[640\/480\] {
  padding-top: 133.**************% }.ratio-\[642\/1400\] {
  padding-top: 45.***************% }.ratio-\[660\/960\] {
  padding-top: 68.75% }.ratio-\[72\/379\] {
  padding-top: 18.***************% }.ratio-\[734\/960\] {
  padding-top: 76.**************% }.ratio-\[800\/680\] {
  padding-top: 117.**************% }.ratio-\[96\/148\] {
  padding-top: 64.**************% }

.account-page .account-menu li a {
  display: flex;
  padding-left: 1.0416666666666665rem;
  padding-right: 1.0416666666666665rem;
  padding-top: 0.8333333333333334rem;
  padding-bottom: 0.8333333333333334rem;
  --tw-bg-opacity: 1;
  background-color: rgb(239 239 239 / var(--tw-bg-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  align-items: center;
  gap: 0.625rem }

.account-page .account-menu li:hover a, .account-page .account-menu li.active a {
  --tw-bg-opacity: 1;
  background-color: rgb(244 118 33 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }

.account-page .account-menu .signout a {
  --tw-bg-opacity: 1;
  background-color: rgb(101 101 101 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }

.account-page .form-wrap {
  display: grid;
  gap: 0.8333333333333334rem;
  margin-top: 1.25rem }

.compare .table-wrapper {
  overflow-x: auto;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1)) }

@media (max-width: 767.98px) {
  .compare .table-wrapper {
    font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
    line-height: 1.28571 }
  @media (min-width: 1200px) {

    .compare .table-wrapper {
      font-size: 0.7291666666666667rem } } }

@media (max-width: 575.98px) {
  .compare .big-title {
    font-size: 1.8rem } }

.compare table {
  width: 100%;
  white-space: nowrap }
  .compare table .bpd-compare {
  border-width: 1px }
    .compare table .bpd-compare .img a {
  height: 100% }
  .compare table th {
  font-weight: 400 }
    .compare table th:nth-of-type(1) {
  min-width: 90px }

@media (min-width: 576px) {
  .compare table th:nth-of-type(1) {
    min-width: 120px } }

@media (min-width: 768px) {
  .compare table th:nth-of-type(1) {
    min-width: 16.666666666666668rem } }
    .compare table th:not(:first-of-type):nth-of-type(n+1) {
  min-width: 18.75rem }
    .compare table th:nth-of-type(2) .bpd-compare {
  border-top-left-radius: 0.8333333333333334rem;
  border-bottom-left-radius: 0.8333333333333334rem }
      .compare table th:nth-of-type(2) .bpd-compare .img a {
  border-top-left-radius: 0.8333333333333334rem }
    .compare table th:not(:first-of-type) {
  white-space: normal }
      .compare table th:not(:first-of-type):nth-of-type(2) .bpd-compare {
  margin-left: 1px }
      .compare table th:not(:first-of-type) .bpd-compare {
  margin-left: -3px }
      .compare table th:not(:first-of-type):last-child .bpd-compare {
  border-top-right-radius: 0.8333333333333334rem;
  border-bottom-right-radius: 0.8333333333333334rem }
        .compare table th:not(:first-of-type):last-child .bpd-compare .img a {
  border-top-right-radius: 0.8333333333333334rem }

.compare tbody {
  white-space: normal }
  .compare tbody .title {
  white-space: nowrap;
  position: absolute;
  top: 0;
  z-index: 10;
  left: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem)) }
  .compare tbody.active tr:first-child .title {
  margin-bottom: 1.0416666666666665rem;
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }
    .compare tbody.active tr:first-child .title i {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  font-weight: 700 }
  .compare tbody.active tr:not(:first-child) {
  display: table-row }
  .compare tbody:not(:first-of-type) {
  border-top-width: 1px;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(239 239 239 / var(--tw-border-opacity, 1)) }
  .compare tbody::before, .compare tbody::after {
    content: '';
    display: block;
    height: 2.083333333333333rem }
  .compare tbody:nth-of-type(1)::before {
  height: 3.125rem }
  .compare tbody tr:first-child {
  cursor: pointer;
  overflow: hidden }
  .compare tbody tr:not(:first-of-type) {
  display: none }
    .compare tbody tr:not(:first-of-type) td {
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(220 220 220 / var(--tw-border-opacity, 1));
  padding-left: 1.0416666666666665rem;
  padding-right: 1.0416666666666665rem;
  padding-top: 0.7552083333333334rem;
  padding-bottom: 0.7552083333333334rem }
    .compare tbody tr:not(:first-of-type) td:nth-of-type(1) {
  font-weight: 700 }
    .compare tbody tr:not(:first-of-type):nth-of-type(even) td {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1)) }
    .compare tbody tr:not(:first-of-type):nth-of-type(odd) td {
  --tw-bg-opacity: 1;
  background-color: rgb(246 246 246 / var(--tw-bg-opacity, 1)) }

.compare thead th:first-child, .compare tbody tr td:first-child {
  position: sticky;
  left: clamp(-1px, calc(-1/1920*100rem), calc(-1/1920*100rem));
  z-index: 2 }

.compare tbody tr:first-child td::before, .compare tbody tr:first-child td::after {
  display: none }

.compare tbody tr td:first-child.sticky-sidebar::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  --tw-bg-opacity: 1;
  background-color: rgb(220 220 220 / var(--tw-bg-opacity, 1));
  width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem)) }

.compare tbody tr td:first-child.sticky-sidebar::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  --tw-bg-opacity: 1;
  background-color: rgb(220 220 220 / var(--tw-bg-opacity, 1));
  width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem)) }

.bpd-compare {
  position: relative }
  .bpd-compare .remove-btn {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
  display: flex;
  width: 2.083333333333333rem;
  height: 2.083333333333333rem;
  align-items: center;
  justify-content: center;
  --tw-bg-opacity: 1;
  background-color: rgb(244 118 33 / var(--tw-bg-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }
    .bpd-compare .remove-btn:hover {
  background-color: rgb(0 0 0 / 0.4) }
    .bpd-compare .remove-btn i {
  font-size: clamp(18px, calc(18/1920*100rem), calc(18/1920*100rem));
  line-height: 1.33333 }
    @media (min-width: 1200px) {

  .bpd-compare .remove-btn i {
    font-size: 0.9375rem } }
    .bpd-compare .remove-btn i {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
  .bpd-compare:hover .remove-btn {
  pointer-events: auto }

.contact .form-wrap {
  margin-top: 1.6666666666666667rem }

.contact .form-wrap > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.625rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.625rem * var(--tw-space-y-reverse)) }
  .contact .form-wrap .form-group select, .contact .form-wrap .form-group input, .contact .form-wrap .form-group textarea {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1)) }
  .contact .form-wrap textarea {
  height: 6.25rem }

.contact-form {
  background-image: url(../img/contact-form/home-bg.png);
  background-image: url(../img/contact-form/white-bg.png);
  background-size: cover;
  background-position: top;
  background-repeat: no-repeat }
  .contact-form .form-wrap {
    background: rgba(14, 107, 56, 0.8);
    border-radius: 0.8333333333333334rem;
    --tw-backdrop-blur: blur(0.5208333333333334rem);
    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    padding: 1.25rem;
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 0.4**************7rem }
  .contact-form textarea {
  height: 6.25rem }
  .contact-form .swiper-column-auto {
    --mr: 8px; }

@media (min-width: 576px) {
  .contact-form .swiper-column-auto {
    --mr: clamp(20px,calc(20/1920*100rem),calc(20/1920*100rem)); } }

@media (max-width: 575.98px) {
  .contact-form .swiper-column-auto {
    --spv: 2; } }

body.home .contact-form {
  background-image: url(../img/contact-form/home-bg.png); }

footer ul.menu {
  list-style-type: disc }

footer ul.menu > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.8333333333333334rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.8333333333333334rem * var(--tw-space-y-reverse)) }

footer ul.menu {
  padding-left: 1.**************33rem }
  footer ul.menu a {
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.28571 }
  @media (min-width: 1200px) {

  footer ul.menu a {
    font-size: 0.7291666666666667rem } }
  footer ul.menu a {
  display: block;
  font-weight: 500 }
  @media (min-width: 576px) {

  footer ul.menu a {
    font-size: clamp(16px, calc(16/1920*100rem), calc(16/1920*100rem));
    line-height: 1.375 }
  @media (min-width: 1200px) {

    footer ul.menu a {
      font-size: 0.8333333333333334rem } } }
  footer ul.menu li[class*="current"] a, footer ul.menu li:hover a {
  text-decoration-line: underline }

footer .description a:hover {
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1));
  text-decoration-line: underline }

footer i {
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.28571 }

@media (min-width: 1200px) {

  footer i {
    font-size: 0.7291666666666667rem } }

@media (max-width: 1199.98px) {
  footer .middle {
    flex-wrap: wrap } }

footer .middle .column-1 {
  max-width: 100% }

@media (min-width: 1200px) {

  footer .middle .column-1 {
    max-width: calc(235 / 1400 * 100%) } }

@media (max-width: 1199.98px) {
  footer .middle .column-1 .wrapper {
    margin-left: auto;
    margin-right: auto;
    width: 30%;
    text-align: center } }

@media (max-width: 575.98px) {
  footer .middle .column-1 .wrapper {
    width: 80% } }

@media (min-width: 1024px) {

  footer .middle .column-2 {
    max-width: calc(380 / 1000 * 100%) } }

@media (min-width: 1200px) {

  footer .middle .column-2 {
    max-width: calc(320 / 1400 * 100%) } }

@media (min-width: 576px) {

  footer .middle .column-3 {
    max-width: 40% } }

@media (min-width: 1024px) {

  footer .middle .column-3 {
    max-width: calc(280 / 1000 * 100%) } }

@media (min-width: 1200px) {

  footer .middle .column-3 {
    max-width: calc(320 / 1400 * 100%) } }

footer .middle .column-4 {
  max-width: 40% }

@media (min-width: 576px) {

  footer .middle .column-4 {
    max-width: 20% } }

@media (min-width: 1024px) {

  footer .middle .column-4 {
    max-width: calc(120 / 1000 * 100%) } }

@media (min-width: 1200px) {

  footer .middle .column-4 {
    max-width: calc(130 / 1400 * 100%) } }

footer .middle .column-5 {
  max-width: 54% }

@media (min-width: 576px) {

  footer .middle .column-5 {
    max-width: 30% } }

@media (min-width: 1024px) {

  footer .middle .column-5 {
    max-width: calc(160 / 1000 * 100%) } }

@media (min-width: 1200px) {

  footer .middle .column-5 {
    max-width: calc(186 / 1400 * 100%) } }

footer .bottom .ft-title {
  margin-bottom: 0.8333333333333334rem }

footer .bottom ul.menu {
  list-style-type: none;
  padding: 0;
  padding-left: 0;
  padding-right: 0;
  display: flex;
  align-items: center }

footer .bottom ul.menu > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0 * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0 * var(--tw-space-y-reverse)) }
  footer .bottom ul.menu a {
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.28571 }
  @media (min-width: 1200px) {

  footer .bottom ul.menu a {
    font-size: 0.7291666666666667rem } }
  footer .bottom ul.menu a {
  --tw-text-opacity: 1;
  color: rgb(220 220 220 / var(--tw-text-opacity, 1)) }
  footer .bottom ul.menu li + li {
  display: flex;
  align-items: center }
    footer .bottom ul.menu li + li::before {
      content: '';
      height: 0.625rem;
      width: 0.052083333333333336rem;
      --tw-bg-opacity: 1;
      background-color: rgb(220 220 220 / var(--tw-bg-opacity, 1));
      margin-left: 0.4**************7rem;
      margin-right: 0.4**************7rem }
    @media (min-width: 768px) {

  footer .bottom ul.menu li + li::before {
    margin-left: 0.625rem;
    margin-right: 0.625rem } }

footer .buttons .btn-primary {
  width: 100%;
  gap: 0.4**************7rem }

header {
  --fs: 12px;
  --lh: 1.375;
  font-size: var(--fs);
  line-height: var(--lh);
  height: 70px }

@media (min-width: 1200px) {

  header {
    height: 5.208333333333334rem } }

header {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms }

@media (min-width: 1200px) {
  header #autoClone-WrapTop {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
    height: 2.083333333333333rem } }

@media (min-width: 576px) {
  header {
    --fs: 16px; } }

@media (min-width: 1200px) {
  header {
    --fs: calc(14/1920*100rem);
    --lh: calc(18/1920*100rem); } }

@media (max-width: 1199.98px) {
  header {
    border-bottom-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
    --tw-border-opacity: 1;
    border-color: rgb(189 189 189 / var(--tw-border-opacity, 1)) } }
  header.header-active {
  --tw-shadow: 0.20833333333333334rem 0.20833333333333334rem 1.6666666666666667rem 0 rgba(0, 0, 0, 0.08);
  --tw-shadow-colored: 0.20833333333333334rem 0.20833333333333334rem 1.6666666666666667rem 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) }
  header .header-wrapper {
  display: flex }

@media (min-width: 1200px) {
  header .header-wrapper {
    gap: 24px; }
  @media (min-width: 1200px) {

    header .header-wrapper {
      gap: 2.083333333333333rem } }
  header .header-wrapper {
    display: grid;
    grid-template-columns: calc(316 / 1840 * 100%) calc(1423 / 1840 * 100%) } }
  header .container-fluid {
  height: 100% }
    header .container-fluid > .header-wrapper {
  height: 100% }

@media (min-width: 1200px) {
  header .container-fluid .col-right {
    display: flex;
    height: 100%;
    flex-direction: column }
    header .container-fluid .col-right .wrap-bottom {
    flex: 1 1 0% }
    header .container-fluid .col-right #autoClone-MainMenu {
    height: 100% }
      header .container-fluid .col-right #autoClone-MainMenu nav, header .container-fluid .col-right #autoClone-MainMenu nav > ul, header .container-fluid .col-right #autoClone-MainMenu nav > ul > li, header .container-fluid .col-right #autoClone-MainMenu nav > ul > li > a, header .container-fluid .col-right #autoClone-MainMenu nav > ul > li > .title, header .container-fluid .col-right #autoClone-MainMenu nav > ul > li > .title > a {
    height: 100% } }
  header .hotline {
  line-height: 1.375;
  font-size: 0.8333333333333334rem }

@media (max-width: 1199.98px) {
  header .logo {
    height: 100%;
    width: 180px } }

@media (max-width: 575.98px) {
  header .logo {
    width: 140px } }
  header .logo a {
  padding-top: 18.9873417721519% }
    header .logo a img {
  -o-object-fit: contain;
     object-fit: contain }
    @media (min-width: 1200px) {

  header .logo a img {
    padding-top: 0.4**************7rem;
    padding-bottom: 0.4**************7rem } }

@media (max-width: 1199.98px) {
  header .logo a {
    height: 100% } }
  header .separator {
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: center;
  --tw-bg-opacity: 1;
  background-color: rgb(220 220 220 / var(--tw-bg-opacity, 1));
  height: clamp(12px, calc(12/1920*100rem), calc(12/1920*100rem));
  width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  margin-left: 4px;
  margin-right: 4px }
  @media (min-width: 1440px) {

  header .separator {
    margin-left: 0.625rem;
    margin-right: 0.625rem } }
  header .language {
  position: relative;
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1));
  z-index: 1;
  display: flex;
  align-items: center;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }

@media (max-width: 1199.98px) {
  header .language {
    margin-bottom: 0;
    border-bottom-width: 0 } }
    header .language a {
  display: block;
  padding: 0;
  padding-left: 0;
  padding-right: 0;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }
    header .language .wpml-ls {
  border-width: 0px;
  padding: 0;
  padding-left: 0;
  padding-right: 0 }
      header .language .wpml-ls li {
  display: block }
    header .language .active-language {
  display: flex;
  width: 4.**************6rem;
  align-items: center;
  justify-content: center }

@media (min-width: 1200px) {
  header .language .active-language li:not([class*='current']) {
    display: none }
  header .language .active-language li[class*='current'] {
    display: flex;
    align-items: center;
    gap: 0.4**************7rem }
    header .language .active-language li[class*='current']::before {
      content: '\f0ac';
      font-family: 'Font Awesome 6 Pro' }
    header .language .active-language li[class*='current']::after {
      content: '\f078';
      font-family: 'Font Awesome 6 Pro' } }

@media (max-width: 1199.98px) {
  header .language .active-language ul {
    display: flex;
    align-items: center;
    border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
    --tw-border-opacity: 1;
    border-color: rgb(14 107 56 / var(--tw-border-opacity, 1)) }
    header .language .active-language ul a {
    display: flex;
    width: 32px;
    height: 32px;
    align-items: center;
    justify-content: center }
    header .language .active-language ul li[class*='current'] a {
    --tw-bg-opacity: 1;
    background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1));
    font-weight: 700;
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1)) } }

@media (min-width: 1200px) {
  header .language:hover {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
    header .language:hover .hover-language {
    pointer-events: auto;
    --tw-translate-y: 0;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    opacity: 1 } }
    header .language .hover-language {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  pointer-events: none;
  opacity: 0;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  --tw-translate-y: -0.20833333333333334rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }
    @media (max-width: 1199.98px) {

  header .language .hover-language {
    display: none } }
      header .language .hover-language li[class*='current'] {
  display: none }
      header .language .hover-language li a {
  --tw-bg-opacity: 1;
  background-color: rgb(239 239 239 / var(--tw-bg-opacity, 1));
  padding-top: 0.4**************7rem;
  padding-bottom: 0.4**************7rem;
  text-align: center;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  --tw-text-opacity: 1;
  color: rgb(129 129 129 / var(--tw-text-opacity, 1)) }
      header .language .hover-language li:hover a {
  --tw-bg-opacity: 1;
  background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
  header .btn-auth:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }

@media (min-width: 1200px) {
  header .page-link:nth-of-type(1) {
    width: 9.270833333333334rem }
  header .page-link:nth-of-type(2) {
    width: 8.645833333333334rem }
  header .page-link:nth-of-type(3) {
    width: 6.666666666666667rem } }
  header .active .highlight-bottom::before {
  height: 100% }
  header .active .highlight-bottom i {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
  header .wrap-bottom nav a {
  display: flex;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }
  header .wrap-bottom nav li:hover > a, header .wrap-bottom nav li:hover > .title > a, header .wrap-bottom nav li:hover > .title > i, header .wrap-bottom nav li[class*='current'] > a, header .wrap-bottom nav li[class*='current'] > .title > a, header .wrap-bottom nav li[class*='current'] > .title > i {
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }
    header .wrap-bottom nav li:hover > a::before, header .wrap-bottom nav li:hover > .title > a::before, header .wrap-bottom nav li:hover > .title > i::before, header .wrap-bottom nav li[class*='current'] > a::before, header .wrap-bottom nav li[class*='current'] > .title > a::before, header .wrap-bottom nav li[class*='current'] > .title > i::before {
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }
  header .wrap-bottom nav li > .title {
  display: flex;
  align-items: center;
  gap: 0.4**************7rem }
  header .wrap-bottom nav > ul {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.4**************7rem }
    header .wrap-bottom nav > ul > li:first-child > a, header .wrap-bottom nav > ul > li:first-child > .title > a {
  font-size: 0;
  line-height: 0 }
      header .wrap-bottom nav > ul > li:first-child > a::before, header .wrap-bottom nav > ul > li:first-child > .title > a::before {
        content: '\f015';
        font-family: 'Font Awesome 6 Pro';
        font-size: clamp(14px, calc(16/1920*100rem), calc(16/1920*100rem));
        font-weight: 900;
        transition-property: all;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 150ms }
    header .wrap-bottom nav > ul > li > a, header .wrap-bottom nav > ul > li > .title > a {
  font-size: 0.9375rem;
  line-height: 1.25rem;
  font-weight: 700;
  text-transform: uppercase }
    header .wrap-bottom nav > ul > li > a, header .wrap-bottom nav > ul > li > .title, header .wrap-bottom nav > ul > li > .title > a {
  position: relative;
  display: flex;
  align-items: center }
      header .wrap-bottom nav > ul > li > a::after, header .wrap-bottom nav > ul > li > .title::after, header .wrap-bottom nav > ul > li > .title > a::after {
        content: '';
        position: absolute;
        bottom: 0;
        --tw-bg-opacity: 1;
        background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1));
        height: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
        width: 0;
        transition-property: all;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 150ms }
    header .wrap-bottom nav > ul > li.normal-dropdown > ul > li:hover > a, header .wrap-bottom nav > ul > li.normal-dropdown > ul > li[class*='current'] > a {
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1)) }
    header .wrap-bottom nav > ul > li.normal-dropdown.has-child-arrow > ul > li > a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1.0416666666666665rem }
      header .wrap-bottom nav > ul > li.normal-dropdown.has-child-arrow > ul > li > a::after {
        content: '\f105';
        font-family: 'Font Awesome 6 Pro';
        line-height: normal;
        font-size: clamp(12px, calc(12/1920*100rem), calc(12/1920*100rem)) }
    header .wrap-bottom nav > ul > li[class*='current'] > a, header .wrap-bottom nav > ul > li[class*='current'] > .title > a, header .wrap-bottom nav > ul > li:hover > a, header .wrap-bottom nav > ul > li:hover > .title > a {
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }
      header .wrap-bottom nav > ul > li[class*='current'] > a::after, header .wrap-bottom nav > ul > li[class*='current'] > .title > a::after, header .wrap-bottom nav > ul > li:hover > a::after, header .wrap-bottom nav > ul > li:hover > .title > a::after {
  width: 100% }
    header .wrap-bottom nav > ul > li > .mega-menu-wrapper a, header .wrap-bottom nav > ul > li > ul a {
  line-height: 1.375;
  font-size: 0.8333333333333334rem }
    header .wrap-bottom nav > ul > li > ul {
  position: absolute;
  top: 100%;
  border-bottom-right-radius: 0.4**************7rem;
  border-bottom-left-radius: 0.4**************7rem;
  --tw-bg-opacity: 1;
  background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1));
  padding: 1.25rem;
  width: -moz-max-content;
  width: max-content }
    header .wrap-bottom nav > ul > li > ul > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.8333333333333334rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.8333333333333334rem * var(--tw-space-y-reverse)) }
    header .wrap-bottom nav > ul > li > ul {
  pointer-events: none;
  opacity: 0 }
      header .wrap-bottom nav > ul > li > ul li a {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
    header .wrap-bottom nav > ul > li:hover > .mega-menu-wrapper, header .wrap-bottom nav > ul > li:hover > ul {
  pointer-events: auto;
  opacity: 1 }
  header .wrap-bottom .mega-menu-wrapper {
    box-shadow: 0 0 1.66667rem 0 rgba(0, 0, 0, 0.08);
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    border-top-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
    --tw-border-opacity: 1;
    border-color: rgb(239 239 239 / var(--tw-border-opacity, 1));
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
    pointer-events: none;
    opacity: 0;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
    visibility: hidden }
    header .wrap-bottom .mega-menu-wrapper.initialized {
  visibility: visible }
  header .wrap-bottom .mega-menu-inner {
  display: grid;
  grid-template-columns: calc(360 / 1400 * 100%) 1fr }
  header .wrap-bottom .menu-right-inner {
  gap: 24px; }
  @media (min-width: 1200px) {

  header .wrap-bottom .menu-right-inner {
    gap: 2.083333333333333rem } }
  header .wrap-bottom .menu-right-inner {
  display: grid }
  header .wrap-bottom .menu-left, header .wrap-bottom .menu-right {
  padding-top: 2.083333333333333rem;
  padding-bottom: 2.083333333333333rem }
  header .wrap-bottom .menu-left {
  padding-left: 1.0416666666666665rem;
  padding-right: 1.0416666666666665rem;
  border-right-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(239 239 239 / var(--tw-border-opacity, 1)) }
    header .wrap-bottom .menu-left a {
  font-weight: 700;
  line-height: 1.3333;
  font-size: 0.9375rem;
  border-radius: 0.20833333333333334rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 1.0416666666666665rem;
  padding-right: 1.0416666666666665rem }
      header .wrap-bottom .menu-left a:hover {
  background-color: rgb(14 107 56 / 0.7);
  color: rgb(255 255 255 / 0.8) }
      header .wrap-bottom .menu-left a.active {
  --tw-bg-opacity: 1;
  background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
  header .wrap-bottom .menu-right {
  padding-left: 2.083333333333333rem;
  padding-right: 2.083333333333333rem }
  header .wrap-bottom .menu-item {
  flex: 1 1 auto }
    header .wrap-bottom .menu-item .title {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1));
  font-size: clamp(16px, calc(18/1920*100rem), calc(18/1920*100rem));
  display: flex;
  min-height: 2.5rem;
  align-items: center;
  border-bottom-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(239 239 239 / var(--tw-border-opacity, 1)) }
    header .wrap-bottom .menu-item ul {
  margin-top: 1.0416666666666665rem;
  list-style-type: disc;
  padding-left: 1.0416666666666665rem;
  --tw-text-opacity: 1;
  color: rgb(70 70 70 / var(--tw-text-opacity, 1)) }
    header .wrap-bottom .menu-item ul > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.46875rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.46875rem * var(--tw-space-y-reverse)) }
      header .wrap-bottom .menu-item ul li:hover > a, header .wrap-bottom .menu-item ul li:hover > .title > a, header .wrap-bottom .menu-item ul li[class*='current'] > a, header .wrap-bottom .menu-item ul li[class*='current'] > .title > a {
  text-decoration-line: underline }
  header .wrap-bottom .mega-menu-style-1 .menu-right-inner {
  grid-template-columns: calc(640 / 960 * 100%) 1fr }
    header .wrap-bottom .mega-menu-style-1 .menu-right-inner .menu-item:nth-of-type(1) ul {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  -moz-column-gap: 2.083333333333333rem;
       column-gap: 2.083333333333333rem;
  row-gap: 0.4**************7rem }
    header .wrap-bottom .mega-menu-style-1 .menu-right-inner .menu-item:nth-of-type(1) ul > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0 * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0 * var(--tw-space-y-reverse)) }
  header .wrap-bottom .mega-menu-style-2 .menu-right-inner {
  grid-template-columns: repeat(2, minmax(0, 1fr)) }
  header .wrap-bottom .mega-menu-style-2 .logo-item ul {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 1.0416666666666665rem;
  list-style-type: none }
  header .wrap-bottom .mega-menu-style-2 .logo-item ul > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0 * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0 * var(--tw-space-y-reverse)) }
  header .wrap-bottom .mega-menu-style-2 .logo-item ul {
  padding: 0;
  padding-left: 0;
  padding-right: 0 }
    header .wrap-bottom .mega-menu-style-2 .logo-item ul li a {
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(239 239 239 / var(--tw-border-opacity, 1));
  padding-top: 61.42857142857143%;
  border-radius: 0.20833333333333334rem }
    header .wrap-bottom .mega-menu-style-2 .logo-item ul li:hover a {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) }
    header .wrap-bottom .mega-menu-style-2 .logo-item ul li[class*='current'] a, header .wrap-bottom .mega-menu-style-2 .logo-item ul li:hover a {
  --tw-border-opacity: 1;
  border-color: rgb(14 107 56 / var(--tw-border-opacity, 1)) }
  header .wrap-bottom .mega-menu-style-2 .btn {
  margin-left: auto;
  margin-right: auto;
  margin-top: 1.0416666666666665rem }

@media (max-width: 1199.98px) {
  header .col-right {
    position: absolute;
    right: 0;
    top: 50%;
    --tw-translate-y: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    justify-content: center }
    header .col-right .wrap-bottom {
    display: none }
    header .col-right .wrap-top {
    gap: 0.625rem }
      header .col-right .wrap-top .separator, header .col-right .wrap-top .socials, header .col-right .wrap-top .auth-button, header .col-right .wrap-top .page-link-wrapper {
    display: none }
    header .col-right .contact-link span:nth-of-type(2) {
    display: none } }

@media (max-width: 575.98px) {
  header .contact-link {
    display: none } }

full-fill {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0; }

#burger {
  flex: 0 0 40px;
  width: 40px;
  height: 40px;
  color: #091C36; }
  #burger svg {
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
    width: 100% }
  #burger .line {
    fill: none;
    stroke-linecap: round;
    stroke-linejoin: round;
    stroke-width: 2;
    transition: stroke-dasharray 0.6s cubic-bezier(0.4, 0, 0.2, 1), stroke-dashoffset 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    stroke: #000 }
  #burger .line-top-bottom {
    stroke-dasharray: 12 63; }
  #burger.active svg {
    transform: rotate(-45deg); }
    #burger.active svg .line-top-bottom {
      stroke-dasharray: 20 300;
      stroke-dashoffset: -32.42; }

.mini-cart-wrapper {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 160;
  display: flex;
  height: 100%;
  width: 100%;
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  flex-direction: column;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  --tw-shadow: 0.20833333333333334rem 0.20833333333333334rem 1.6666666666666667rem 0 rgba(0, 0, 0, 0.08);
  --tw-shadow-colored: 0.20833333333333334rem 0.20833333333333334rem 1.6666666666666667rem 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  max-width: 35.41666666666667rem }
  .mini-cart-wrapper.active {
  --tw-translate-x: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }
    .mini-cart-wrapper.active + .mini-cart-overlay {
  pointer-events: auto;
  opacity: 1 }
  .mini-cart-wrapper .wrapper {
  display: flex;
  height: 100%;
  flex: 1 1 0%;
  flex-direction: column;
  overflow: hidden;
  padding-left: 0.8333333333333334rem;
  padding-right: 0.8333333333333334rem;
  padding-bottom: 1.5625rem }
  @media (min-width: 1200px) {

  .mini-cart-wrapper .wrapper {
    padding-left: 2.083333333333333rem;
    padding-right: 1.6666666666666667rem } }
  .mini-cart-wrapper .middle {
  margin-top: 0.625rem;
  display: flex;
  flex-direction: column;
  overflow: hidden }
  @media (min-width: 1200px) {

  .mini-cart-wrapper .middle {
    margin-top: 2.083333333333333rem } }
    .mini-cart-wrapper .middle > .title {
  font-size: clamp(22px, calc(24/1920*100rem), calc(24/1920*100rem));
  line-height: 1.25 }
    @media (min-width: 1200px) {

  .mini-cart-wrapper .middle > .title {
    font-size: 1.25rem } }
    .mini-cart-wrapper .middle > .title {
  font-weight: 700 }
    .mini-cart-wrapper .middle .list {
      overflow-x: inherit;
      margin-top: 1.0416666666666665rem }
    @media (min-width: 1200px) {

  .mini-cart-wrapper .middle .list {
    padding-right: 0.4**************7rem } }
  .mini-cart-wrapper .bottom {
  display: flex;
  flex-direction: column;
  padding-right: 0.4**************7rem }
  .mini-cart-wrapper .bottom-wrapper {
  flex: 1 1 0% }
  .mini-cart-wrapper .bottom-wrapper > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.625rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.625rem * var(--tw-space-y-reverse)) }
  .mini-cart-wrapper .bottom-wrapper {
  border-top-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-top-color: rgb(220 220 220 / var(--tw-border-opacity, 1));
  padding-top: 1.0416666666666665rem;
  padding-bottom: 1.0416666666666665rem }
  .mini-cart-wrapper .list::-webkit-scrollbar {
    width: clamp(5px, calc(5/1920*100rem), calc(5/1920*100rem)); }
  .mini-cart-wrapper .mini-cart-item {
  padding-bottom: 0.625rem }
    .mini-cart-wrapper .mini-cart-item + .mini-cart-item {
  border-top-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(209 209 209 / var(--tw-border-opacity, 1));
  padding-top: 0.625rem }
    .mini-cart-wrapper .mini-cart-item img {
  -o-object-fit: contain;
     object-fit: contain }

.backdrop-overlay {
  background-color: rgb(0 0 0 / 0.3);
  pointer-events: none;
  opacity: 0;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms }

.nav-mobile::before {
  content: '';
  background-image: url(../img/vertical-logo.svg);
  background-position: 0 var(--badge-progress);
  width: 15%;
  height: 100%;
  left: 0;
  top: 0;
  background-size: contain;
  position: absolute;
  --tw-brightness: brightness(0);
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
  pointer-events: none;
  opacity: 0.1 }

.nav-mobile {
  transition: all 0.7s cubic-bezier(0.53, -0.22, 0.35, 1.16);
  max-width: 500px;
  height: 100dvh;
  top: 0;
  pointer-events: none;
  opacity: 0;
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-bg-opacity: 1;
  background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
  .nav-mobile.active {
  --tw-translate-x: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  pointer-events: auto;
  opacity: 1 }
    .nav-mobile.active + .backdrop-overlay {
  pointer-events: auto;
  opacity: 1 }
  .nav-mobile .close-nav {
  display: flex;
  height: 40px;
  width: 100%;
  align-items: center;
  margin-right: 0.8333333333333334rem;
  margin-left: auto;
  gap: 0.4**************7rem;
  border-bottom-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(220 220 220 / var(--tw-border-opacity, 1));
  padding-left: 0.8333333333333334rem;
  padding-right: 0.8333333333333334rem;
  position: relative;
  z-index: 1;
  background-color: rgb(14 107 56 / 0.8) }
    .nav-mobile .close-nav i {
  font-size: 20px;
  font-weight: 400;
  display: flex;
  width: 32px;
  height: 32px;
  align-items: center;
  justify-content: center }
  .nav-mobile nav {
  padding-left: 2.083333333333333rem;
  padding-right: 2.083333333333333rem;
  padding-top: 24px;
  padding-bottom: 24px;
  width: 100%;
  height: 100% }
    .nav-mobile nav a {
  display: flex }
    .nav-mobile nav li[class*='current'] > a, .nav-mobile nav li[class*='current'] > .title > a {
  font-weight: 600 }
    .nav-mobile nav li[class*='current'] > a, .nav-mobile nav li[class*='current'] > .title > a, .nav-mobile nav li[class*='current'] > .title > i {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
    .nav-mobile nav li.toggle-dropdown > .title i {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }
    .nav-mobile nav li i {
  display: flex;
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms }
    .nav-mobile nav li > a, .nav-mobile nav li > .title > a {
  font-size: 18px;
  padding-top: 0.4**************7rem;
  padding-bottom: 0.4**************7rem }
    .nav-mobile nav > ul > li:first-child > a, .nav-mobile nav > ul > li:first-child > .title > a {
  font-size: 0 }
      .nav-mobile nav > ul > li:first-child > a::before, .nav-mobile nav > ul > li:first-child > .title > a::before {
        content: '\f015';
        font-family: 'Font Awesome 6 Pro';
        display: block;
        line-height: 1.3;
        font-size: 20px }
      @media (min-width: 576px) {

  .nav-mobile nav > ul > li:first-child > a::before, .nav-mobile nav > ul > li:first-child > .title > a::before {
    font-size: 24px } }
      .nav-mobile nav > ul > li:first-child > a::before, .nav-mobile nav > ul > li:first-child > .title > a::before {
  font-weight: 500 }
    .nav-mobile nav > ul > li > a, .nav-mobile nav > ul > li > .title > a {
  flex: 1 1 0%;
  font-size: 20px }
    @media (min-width: 576px) {

  .nav-mobile nav > ul > li > a, .nav-mobile nav > ul > li > .title > a {
    font-size: 24px } }
    .nav-mobile nav > ul > li > a, .nav-mobile nav > ul > li > .title > a {
  text-transform: uppercase }
    .nav-mobile nav > ul > li > ul {
  padding-left: 0.8333333333333334rem;
  display: none;
  border-radius: 0.20833333333333334rem;
  background-color: rgb(255 255 255 / 0.2) }
    .nav-mobile nav > ul > li li > ul {
  padding-left: 0.8333333333333334rem }
    .nav-mobile nav > ul > li li > .title > a {
  font-weight: 700 }
    .nav-mobile nav > ul > li li .title i {
  display: none }
    .nav-mobile nav > ul > li > .title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.4**************7rem }
  .nav-mobile .mega-menu-inner a {
  display: flex;
  min-height: 40px;
  align-items: center;
  padding-top: 0.20833333333333334rem;
  padding-bottom: 0.20833333333333334rem;
  font-size: 18px }
  .nav-mobile .mega-menu-inner [tab-item] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.20833333333333334rem }
    .nav-mobile .mega-menu-inner [tab-item]::after {
      content: "\f107";
      font-family: 'Font Awesome 6 Pro';
      display: flex;
      width: 40px;
      height: 40px;
      align-items: center;
      justify-content: center;
      transition-property: all;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 500ms }
  .nav-mobile .mega-menu-wrapper .container, .nav-mobile .mega-menu-wrapper header .wrap-bottom .mega-menu-inner, header .wrap-bottom .nav-mobile .mega-menu-wrapper .mega-menu-inner {
  height: 100% }
    .nav-mobile .mega-menu-wrapper .container .mega-menu-inner, .nav-mobile .mega-menu-wrapper header .wrap-bottom .mega-menu-inner .mega-menu-inner, header .wrap-bottom .nav-mobile .mega-menu-wrapper .mega-menu-inner .mega-menu-inner {
  display: flex;
  height: 100%;
  flex-direction: column }
      .nav-mobile .mega-menu-wrapper .container .mega-menu-inner .menu-left, .nav-mobile .mega-menu-wrapper header .wrap-bottom .mega-menu-inner .mega-menu-inner .menu-left, header .wrap-bottom .nav-mobile .mega-menu-wrapper .mega-menu-inner .mega-menu-inner .menu-left {
  flex: 1 1 0%;
  overflow: auto;
  overscroll-behavior: contain;
  transform: translate3d(0, 0, 0);
  will-change: transform }
  .nav-mobile .mega-menu-wrapper, .nav-mobile .menu-right-item {
  position: absolute;
  inset: 0;
  --tw-bg-opacity: 1;
  background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1));
  pointer-events: none;
  z-index: 2;
  overflow: hidden;
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  height: 100%;
  display: block !important }
    .nav-mobile .mega-menu-wrapper.active, .nav-mobile .menu-right-item.active {
  --tw-translate-x: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  pointer-events: auto }
  .nav-mobile .container, .nav-mobile header .wrap-bottom .mega-menu-inner, header .wrap-bottom .nav-mobile .mega-menu-inner {
  padding-left: 0;
  padding-right: 0 }
  .nav-mobile .close-button {
  display: flex;
  align-items: center;
  gap: 0.625rem }
  .nav-mobile .close-button:active {
  background-color: rgb(41 41 41 / 0.5) }
  .nav-mobile .close-button {
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.28571 }
  @media (min-width: 1200px) {

  .nav-mobile .close-button {
    font-size: 0.7291666666666667rem } }
  .nav-mobile .close-button {
  height: 40px;
  border-bottom-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(220 220 220 / var(--tw-border-opacity, 1));
  padding-left: 0.8333333333333334rem;
  padding-right: 0.8333333333333334rem }
    .nav-mobile .close-button i {
  font-size: 20px;
  font-weight: 400 }
  .nav-mobile .menu-left, .nav-mobile .menu-right-inner {
  padding-left: 2.083333333333333rem;
  padding-right: 2.083333333333333rem;
  padding-top: 1.0416666666666665rem;
  padding-bottom: 1.0416666666666665rem }
  .nav-mobile .menu-right-item {
  display: flex !important;
  flex-direction: column }
    .nav-mobile .menu-right-item .menu-right-wrapper {
  flex: 1 1 0%;
  overflow: auto;
  overscroll-behavior: contain;
  transform: translate3d(0, 0, 0);
  will-change: transform }
    .nav-mobile .menu-right-item .menu-right-inner {
  overflow: auto;
  overscroll-behavior: contain;
  transform: translate3d(0, 0, 0);
  will-change: transform }
  .nav-mobile .menu-item + .menu-item {
  margin-top: 1.0416666666666665rem }
  .nav-mobile .menu-item .title {
  border-bottom-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(220 220 220 / var(--tw-border-opacity, 1));
  font-size: 20px;
  font-weight: 700 }
  .nav-mobile .menu-item ul {
  margin-top: 1.0416666666666665rem }
    .nav-mobile .menu-item ul a {
  padding-top: 0.4**************7rem;
  padding-bottom: 0.4**************7rem }
  .nav-mobile .logo-item ul {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 0.4**************7rem }
    .nav-mobile .logo-item ul a {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-top: 61.42857142857143%;
  border-radius: 0.20833333333333334rem }
  .nav-mobile .mega-menu-style-2 .btn {
  margin-left: auto;
  margin-right: auto;
  margin-top: 1.0416666666666665rem }
  .nav-mobile #autoCloneHere-MainMenu {
  flex: 1 1 0% }
  .nav-mobile #autoClone-MainMenu {
  height: 100%;
  overflow: hidden }
  .nav-mobile #autoCloneHere-WrapTop {
  padding-left: 0.4**************7rem;
  padding-right: 0.4**************7rem }
    .nav-mobile #autoCloneHere-WrapTop .wrap-top {
  flex-wrap: wrap;
  justify-content: flex-start }
    .nav-mobile #autoCloneHere-WrapTop .highlight-bottom {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1)) }
    .nav-mobile #autoCloneHere-WrapTop .language, .nav-mobile #autoCloneHere-WrapTop .separator, .nav-mobile #autoCloneHere-WrapTop .contact-link {
  display: none }
    .nav-mobile #autoCloneHere-WrapTop .btn-auth {
  width: 50%;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
    .nav-mobile #autoCloneHere-WrapTop .auth-button {
  width: 100%;
  margin-top: 4px }
    .nav-mobile #autoCloneHere-WrapTop .auth-button > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(4px * var(--tw-space-x-reverse));
  margin-left: calc(4px * calc(1 - var(--tw-space-x-reverse))) }
    .nav-mobile #autoCloneHere-WrapTop .auth-button {
  order: 2 }
      .nav-mobile #autoCloneHere-WrapTop .auth-button.auth-logged .btn-auth {
  width: auto }
      .nav-mobile #autoCloneHere-WrapTop .auth-button .cart {
  flex: 1 1 0%;
  flex-grow: 1 }
        .nav-mobile #autoCloneHere-WrapTop .auth-button .cart .btn-auth {
  width: 100%;
  flex: 1 1 0% }
    .nav-mobile #autoCloneHere-WrapTop .page-link-wrapper {
  width: 100% }
    .nav-mobile #autoCloneHere-WrapTop .page-link-wrapper > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(4px * var(--tw-space-x-reverse));
  margin-left: calc(4px * calc(1 - var(--tw-space-x-reverse))) }
    .nav-mobile #autoCloneHere-WrapTop .page-link-wrapper {
  order: 1 }
    .nav-mobile #autoCloneHere-WrapTop .socials {
  order: 3;
  width: 100%;
  margin-top: 8px;
  justify-content: center }
    .nav-mobile #autoCloneHere-WrapTop .socials > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(8px * var(--tw-space-x-reverse));
  margin-left: calc(8px * calc(1 - var(--tw-space-x-reverse))) }
    .nav-mobile #autoCloneHere-WrapTop .page-link {
  height: 40px;
  flex-grow: 1;
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem)) }
    .nav-mobile #autoCloneHere-WrapTop .btn-auth {
  height: 40px;
  flex-grow: 1;
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem)) }
      .nav-mobile #autoCloneHere-WrapTop .btn-auth.login {
  background-color: rgb(41 41 41 / 0.5) }
      .nav-mobile #autoCloneHere-WrapTop .btn-auth.register {
  background-color: rgb(41 41 41 / 0.2) }
    .nav-mobile #autoCloneHere-WrapTop .social-icon {
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem)) }

@media (max-width: 575.98px) {
  .nav-mobile #autoCloneHere-WrapTop .social-icon.contact-link {
    display: flex } }
      .nav-mobile #autoCloneHere-WrapTop .social-icon i {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }

.global-breadcrumb {
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.28571 }

@media (min-width: 1200px) {

  .global-breadcrumb {
    font-size: 0.7291666666666667rem } }

.global-breadcrumb {
  display: flex;
  align-items: center;
  --tw-text-opacity: 1;
  color: rgb(129 129 129 / var(--tw-text-opacity, 1));
  min-height: clamp(42px, calc(42/1920*100rem), calc(42/1920*100rem));
  background-color: rgb(14 107 56 / 0.05) }
  .global-breadcrumb .rank-math-breadcrumb p {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  -moz-column-gap: 0;
       column-gap: 0 }
    .global-breadcrumb .rank-math-breadcrumb p .separator {
  padding-top: 0;
  padding-bottom: 0;
  display: flex;
  align-items: center;
  background-color: transparent;
  position: relative;
  font-size: 0 }
      .global-breadcrumb .rank-math-breadcrumb p .separator::before {
        content: "\f054";
        font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
        line-height: 1.28571 }
      @media (min-width: 1200px) {

  .global-breadcrumb .rank-math-breadcrumb p .separator::before {
    font-size: 0.7291666666666667rem } }
      .global-breadcrumb .rank-math-breadcrumb p .separator::before {
  font-family: 'Font Awesome 6 Pro';
  position: static;
  margin-left: 0.625rem;
  margin-right: 0.625rem }
    .global-breadcrumb .rank-math-breadcrumb p a {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }
      .global-breadcrumb .rank-math-breadcrumb p a:first-child {
  font-size: 0;
  line-height: 0 }
        .global-breadcrumb .rank-math-breadcrumb p a:first-child::before {
          content: '\f015';
          font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
          line-height: 1.28571 }
        @media (min-width: 1200px) {

  .global-breadcrumb .rank-math-breadcrumb p a:first-child::before {
    font-size: 0.7291666666666667rem } }
        .global-breadcrumb .rank-math-breadcrumb p a:first-child::before {
  font-family: 'Font Awesome 6 Pro' }
      .global-breadcrumb .rank-math-breadcrumb p a:hover {
  --tw-text-opacity: 1;
  color: rgb(189 189 189 / var(--tw-text-opacity, 1)) }
        .global-breadcrumb .rank-math-breadcrumb p a:hover::before {
  --tw-text-opacity: 1;
  color: rgb(189 189 189 / var(--tw-text-opacity, 1)) }

@media (min-width: 768px) {

  .main-banner {
    height: 33.333333333333336rem } }
  .main-banner.active {
  z-index: 10 }

@media (max-width: 767.98px) {
  .main-banner {
    flex-direction: column;
    --tw-bg-opacity: 1;
    background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1)) }
  .main-banner .wrapper {
    position: relative;
    width: 100% } }
  .main-banner .button-prev:not(.swiper-button-disabled), .main-banner .button-next:not(.swiper-button-disabled) {
  pointer-events: auto }

@media (max-width: 1199.98px) {
  .main-banner .arrow-button {
    display: none } }
  .main-banner .swiper-pagination {
  pointer-events: auto }
    .main-banner .swiper-pagination.swiper-pagination-lock {
  display: none }
  .main-banner .delay-item {
  --tw-translate-y: 0.8333333333333334rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 0;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms }

@media (max-width: 767.98px) {
  .main-banner .container-content {
    padding-top: 1.0416666666666665rem;
    padding-bottom: 1.6666666666666667rem } }
  .main-banner .swiper-slide-active .delay-item {
  --tw-translate-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 1 }
  .main-banner .title {
    text-shadow: 0.20833rem 0.15625rem 0.20833rem rgba(0, 0, 0, 0.5); }
  .main-banner .swiper {
  width: 100% }
  @media (min-width: 768px) {

  .main-banner .swiper {
    position: absolute;
    inset: 0 } }

@media (max-width: 767.98px) {
  .main-banner .img {
    position: static }
    .main-banner .img a {
    position: relative;
    display: block;
    height: 0;
    overflow: hidden;
    padding-top: 50% }
      .main-banner .img a img {
    position: absolute;
    inset: 0;
    -o-object-fit: cover;
       object-fit: cover } }
  .main-banner .checkbox-filter-wrapper {
    box-shadow: 0 0.20833rem 0.20833rem 0 rgba(0, 0, 0, 0.25); }
    .main-banner .checkbox-filter-wrapper .checkbox-value span {
  --tw-text-opacity: 1;
  color: rgb(129 129 129 / var(--tw-text-opacity, 1)) }
    .main-banner .checkbox-filter-wrapper .checkbox-value i {
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }
  .main-banner .search {
  position: relative }
    .main-banner .search input {
      box-shadow: 0 0.20833rem 0.20833rem 0 rgba(0, 0, 0, 0.25);
      display: block;
      width: 100%;
      border-radius: 0.20833333333333334rem;
      --tw-bg-opacity: 1;
      background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
      height: clamp(48px, calc(48/1920*100rem), calc(48/1920*100rem));
      padding-left: 0.8333333333333334rem;
      padding-right: 2.7083333333333335rem }

@media (max-width: 575.98px) {
  .main-banner .search input {
    font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
    line-height: 1.28571 }
  @media (min-width: 1200px) {

    .main-banner .search input {
      font-size: 0.7291666666666667rem } } }
    .main-banner .search button {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;
  width: clamp(48px, calc(48/1920*100rem), calc(48/1920*100rem));
  height: clamp(48px, calc(48/1920*100rem), calc(48/1920*100rem));
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  border-radius: 0.20833333333333334rem;
  pointer-events: none }
      .main-banner .search button:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
  .main-banner .checkbox-filter .checkbox-value {
  height: clamp(48px, calc(48/1920*100rem), calc(48/1920*100rem)) }
  .main-banner .filters {
  display: flex }
  @media (min-width: 1200px) {

  .main-banner .filters {
    margin-left: clamp(-4px, calc(-4/1920*100rem), calc(-4/1920*100rem));
    margin-right: clamp(-4px, calc(-4/1920*100rem), calc(-4/1920*100rem)) } }

@media (max-width: 1199.98px) {
  .main-banner .filters {
    margin-left: -4px;
    margin-right: -4px;
    flex-wrap: wrap;
    row-gap: 8px } }
    .main-banner .filters .checkbox-filter {
  flex: 1 1 0%;
  padding-left: clamp(4px, calc(4/1920*100rem), calc(4/1920*100rem));
  padding-right: clamp(4px, calc(4/1920*100rem), calc(4/1920*100rem)) }

@media (max-width: 1199.98px) {
  .main-banner .filters .checkbox-filter {
    flex: 1 1 auto;
    padding-left: 4px;
    padding-right: 4px;
    width: 33.333333% } }

@media (max-width: 575.98px) {
  .main-banner .filters .checkbox-filter {
    width: 50% } }

@media (max-width: 767.98px) {
  .page-banner .img a {
    padding-top: 50% } }

.about-11 {
  --spv: 3; }
  .about-11 .item .content {
    min-height: calc(113/293*100%); }
    .about-11 .item .content::before {
      content: '';
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.75) 100%);
      position: absolute;
      inset: 0;
      z-index: -1;
      border-radius: 0.8333333333333334rem }

.about-2 .swiper-column-auto {
  --mr: 0px; }

@media (max-width: 1023.98px) {
  .about-2 .swiper-column-auto {
    --spv: 2; } }

@media (max-width: 767.98px) {
  .about-2 .swiper-column-auto {
    --spv: 1.2; } }

.about-2 .item .img a::before {
  content: '';
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.6) 100%);
  position: absolute;
  inset: 0;
  z-index: 1;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms }

.about-2 .item .img a::after {
  content: '';
  background-color: rgb(0 0 0 / 0.4);
  position: absolute;
  inset: 0;
  z-index: 1;
  opacity: 0;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms }

.about-2 .item.active .img a::before {
  opacity: 0; }

.about-2 .item.active .img a::after {
  opacity: 1; }

@media (max-width: 767.98px) {
  .about-2 .item .img a::before {
    opacity: 0; }
  .about-2 .item .img a::after {
    opacity: 1; }
  .about-2 .item .hidden-content {
    display: block !important } }

.about-5 .item {
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  border-color: transparent }
  .about-5 .item.active {
  --tw-border-opacity: 1;
  border-color: rgb(244 118 33 / var(--tw-border-opacity, 1)) }

.about-7 {
  --mr: 0px;
  --spv: 4; }

@media (min-width: 768px) {
  .about-7 {
    --spv: 6; } }

@media (min-width: 1024px) {
  .about-7 {
    --spv: 8; } }
  .about-7 .thumb {
  padding-left: 2.083333333333333rem;
  padding-right: 2.083333333333333rem;
  position: relative;
  margin-top: 2.083333333333333rem;
  margin-bottom: 2.083333333333333rem }
    .about-7 .thumb::before {
      content: '';
      position: absolute;
      top: 50%;
      --tw-translate-y: -50%;
      transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      left: 0;
      width: 100%;
      background-color: rgb(255 255 255 / 0.5);
      height: clamp(2px, calc(2/1920*100rem), calc(2/1920*100rem)) }
    .about-7 .thumb .title {
  font-size: clamp(16px, calc(16/1920*100rem), calc(16/1920*100rem));
  line-height: 1.375 }
    @media (min-width: 1200px) {

  .about-7 .thumb .title {
    font-size: 0.8333333333333334rem } }
    .about-7 .thumb .title {
  font-weight: 400 }
      .about-7 .thumb .title::before {
        content: '';
        background: var(--Primary-1, linear-gradient(195deg, #0E6B38 5.3%, #0E6B38 89.89%));
        inset: 0;
        border-radius: 9999px;
        position: absolute;
        z-index: -1;
        transition-property: all;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 500ms }
    .about-7 .thumb .swiper-slide-thumb-active .title {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(244 118 33 / var(--tw-bg-opacity, 1));
  font-size: clamp(20px, calc(20/1920*100rem), calc(20/1920*100rem));
  line-height: 1.3 }
    @media (min-width: 1200px) {

  .about-7 .thumb .swiper-slide-thumb-active .title {
    font-size: 1.0416666666666667rem } }
    .about-7 .thumb .swiper-slide-thumb-active .title {
  font-weight: 700 }
      .about-7 .thumb .swiper-slide-thumb-active .title::before {
  opacity: 0;
  transition-delay: 100ms }

.cities-3 .ratio-frame {
  border-radius: 0.8333333333333334rem }

@media (max-width: 767.98px) {
  .cities-3 [class*="ratio-["] {
    padding-top: 80% } }

@media (max-width: 575.98px) {
  .cities-4 .office-gallery .swiper-column-auto {
    --spv: 1.2; } }

.cities-4 .office-gallery .img a::before {
  content: '';
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 100%);
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
  height: calc(144 / 372 * 100%);
  width: 100% }

.cities-4 .list-logos .img:hover {
  box-shadow: 0 0.20833rem 0.41667rem 0 rgba(14, 107, 56, 0.33);
  --tw-border-opacity: 1;
  border-color: rgb(14 107 56 / var(--tw-border-opacity, 1)) }

.cities-5 img {
  display: block;
  margin-left: auto !important;
  margin-right: auto !important; }

.cities-6 .item.active {
  --tw-border-opacity: 1;
  border-color: rgb(14 107 56 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  --tw-shadow: 0.20833333333333334rem 0.20833333333333334rem 1.6666666666666667rem 0 rgba(0, 0, 0, 0.08);
  --tw-shadow-colored: 0.20833333333333334rem 0.20833333333333334rem 1.6666666666666667rem 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) }
  .cities-6 .item.active .top {
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }
  .cities-6 .item.active .icon i:nth-of-type(1) {
  opacity: 0 }
  .cities-6 .item.active .icon i:nth-of-type(2) {
  opacity: 1 }

.cities-6 .icon i {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms }
  .cities-6 .icon i:nth-of-type(2) {
  position: absolute;
  top: 50%;
  left: 50%;
  --tw-translate-x: -50%;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 0 }

.consignment-2 .form-group input, .consignment-2 .form-group select, .consignment-2 .form-group textarea, .consignment-2 .form-group .fake-input {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1)) }

.consignment-2 .split-form {
  display: grid;
  gap: 1.0416666666666665rem }

@media (min-width: 576px) {

  .consignment-2 .split-form {
    grid-template-columns: repeat(2, minmax(0, 1fr)) } }
  .consignment-2 .split-form + .split-form {
  margin-top: 2.083333333333333rem }

.consignment-2 .file-upload {
  position: relative }
  .consignment-2 .file-upload .fake-input {
    background-image: url('data:image/svg+xml,<svg width="20" height="19" viewBox="0 0 20 19" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17.5 3.25C18.8672 3.25 20 4.38281 20 5.75V15.75C20 17.1562 18.8672 18.25 17.5 18.25H2.5C1.09375 18.25 0 17.1562 0 15.75V3.25C0 1.88281 1.09375 0.75 2.5 0.75H7.07031C7.73438 0.75 8.35938 1.02344 8.82812 1.49219L10.7422 3.25H17.5ZM18.125 15.75V5.75C18.125 5.4375 17.8125 5.125 17.5 5.125H10L7.5 2.82031C7.38281 2.70312 7.22656 2.625 7.07031 2.625H2.5C2.14844 2.625 1.875 2.9375 1.875 3.25V15.75C1.875 16.1016 2.14844 16.375 2.5 16.375H17.5C17.8125 16.375 18.125 16.1016 18.125 15.75ZM4.96094 8.875C4.29688 8.875 3.71094 8.32812 3.71094 7.625C3.71094 6.96094 4.25781 6.375 4.96094 6.375C5.625 6.375 6.21094 6.96094 6.21094 7.625C6.21094 8.32812 5.625 8.875 4.96094 8.875ZM11.9531 7.9375L16.1328 14.1875C16.25 14.3828 16.25 14.6172 16.1719 14.8125C16.0547 15.0078 15.8203 15.125 15.625 15.125H4.375C4.10156 15.125 3.90625 15.0078 3.75 14.8125C3.67188 14.5781 3.67188 14.3438 3.82812 14.1484L6.5625 10.3984C6.67969 10.2422 6.83594 10.125 7.07031 10.125C7.30469 10.125 7.46094 10.2422 7.57812 10.3984L8.47656 11.6094L10.8984 7.9375C11.0156 7.74219 11.2109 7.625 11.4453 7.625C11.6406 7.625 11.8359 7.74219 11.9531 7.9375Z" fill="url(%23paint0_linear_41285_24296)"/><defs><linearGradient id="paint0_linear_41285_24296" x1="15.3844" y1="-0.91279" x2="9.87079" y2="19.2618" gradientUnits="userSpaceOnUse"><stop stop-color="%230E6B38"/><stop offset="0.9936" stop-color="%230E6B38"/></linearGradient></defs></svg>');
    background-size: clamp(20px, calc(20/1920*100rem), calc(20/1920*100rem)) clamp(20px, calc(20/1920*100rem), calc(20/1920*100rem));
    background-position: center right 0.83333rem;
    background-repeat: no-repeat }
  .consignment-2 .file-upload input[type="file"] {
  position: absolute;
  inset: 0;
  z-index: 1;
  cursor: pointer;
  opacity: 0 }

.consignment-2 .form-submit {
  margin-top: 1.6666666666666667rem }

.news-list nav ul {
  display: flex;
  align-items: center;
  gap: 0.8333333333333334rem }

.news-list nav li.active a, .news-list nav li:hover a {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1)) }
  .news-list nav li.active a::before, .news-list nav li:hover a::before {
  width: 100% }

.news-list nav a {
  position: relative;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  display: block;
  padding-top: 0.4**************7rem;
  padding-bottom: 0.4**************7rem }
  .news-list nav a::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    --tw-bg-opacity: 1;
    background-color: rgb(244 118 33 / var(--tw-bg-opacity, 1));
    height: 0.052083333333333336rem;
    width: 0;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms }

.news-detail .list .bn-small {
  grid-template-columns: calc(100 / 320 * 100%) 1fr;
  gap: 1.0416666666666665rem }
  .news-detail .list .bn-small .title {
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.28571 }
  @media (min-width: 1200px) {

  .news-detail .list .bn-small .title {
    font-size: 0.7291666666666667rem } }
  .news-detail .list .bn-small .ctn {
  display: none }
  .news-detail .list .bn-small + .bn-small {
  margin-top: 0.625rem;
  border-top-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(220 220 220 / var(--tw-border-opacity, 1));
  padding-top: 0.625rem }

.home-1 .swiper-column-auto {
  --mr: 12px;
  --spv: 1.1; }

@media (min-width: 576px) {
  .home-1 .swiper-column-auto {
    --spv: 2; } }

@media (min-width: 1024px) {
  .home-1 .swiper-column-auto {
    --spv: 3;
    --mr: 16px; } }

@media (min-width: 1200px) {
  .home-1 .swiper-column-auto {
    --spv: 4.3962;
    --mr: calc(24/1920*100rem); } }

.home-2 {
  --mr: 0px;
  --spv: 3; }

@media (min-width: 768px) {
  .home-2 {
    --spv: 4; } }

@media (min-width: 1200px) {
  .home-2 {
    --spv: 5; } }

.home-11 {
  --margin: calc(156/1920*100rem); }
  .home-11 .top {
  padding-bottom: var(--margin) }
  .home-11 .bottom {
  margin-top: calc(var(--margin) * -1) }
  .home-11 .swiper .item {
    box-shadow: 0.20833rem 0.20833rem 0.83333rem 0 rgba(0, 0, 0, 0.08); }

@media (max-width: 767.98px) {
  .home-11 .swiper-column-auto {
    --spv: 1.3; } }

@media (max-width: 575.98px) {
  .home-11 .swiper-column-auto {
    --spv: 1.1; } }

.home-6 .img:hover {
  box-shadow: 0 0 0.83333rem 0 rgba(14, 107, 56, 0.16); }

.home-6 .swiper {
  margin-top: -1.25rem;
  margin-bottom: -1.25rem;
  padding-top: 1.25rem;
  padding-bottom: 1.25rem }

@media (max-width: 1023.98px) {
  .home-6 .col-right {
    padding-left: 2.5rem;
    padding-right: 2.5rem } }

@media (max-width: 575.98px) {
  .home-6 .col-right {
    padding-left: 2.083333333333333rem;
    padding-right: 2.083333333333333rem } }

.home-6 .col-right .swiper.swiper-initialized {
  overflow: visible }

.home-6 .col-right .swiper-slide {
  width: 100% }
  .home-6 .col-right .swiper-slide a::before {
    content: '';
    position: absolute;
    inset: 0;
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
    opacity: 0;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 500ms;
    z-index: 1 }

.home-6 .col-right .slide-next-1 a::before {
  opacity: 0.5 }

.home-6 .col-right .slide-next-2 a::before {
  opacity: 0.75 }

.home-6 .swiper-column-auto {
  --spv: 3;
  --mr: 12px; }

@media (min-width: 576px) {
  .home-6 .swiper-column-auto {
    --spv: 4; } }

@media (min-width: 1200px) {
  .home-6 .swiper-column-auto {
    --mr: calc(16/1920*100rem); } }

.home-6 .counter {
  font-family: Anton, sans-serif;
  font-weight: 400 }

.home-8 .swiper-slide {
  padding-top: 0.8333333333333334rem }

.home-8 .swiper-column-auto {
  --mr: calc(20/1920*100rem); }

@media (max-width: 1023.98px) {
  .home-8 .swiper-column-auto {
    --spv: 3.5; } }

@media (max-width: 767.98px) {
  .home-8 .swiper-column-auto {
    --spv: 2.5; } }

@media (max-width: 575.98px) {
  .home-8 .swiper-column-auto {
    --spv: 2.2; } }
  @media (min-width: 1024px) {

  .home-8 .swiper-column-auto .swiper-slide {
    width: clamp(180px, calc(180/1920*100rem), calc(180/1920*100rem)) } }

.home-grid-item {
  --mr: 0px;
  --spv: 2; }

@media (min-width: 768px) {
  .home-grid-item {
    --spv: 3; } }

@media (min-width: 1024px) {
  .home-grid-item {
    --spv: 4; } }

@media (min-width: 1200px) {
  .home-grid-item {
    --spv: 6; } }
  .home-grid-item .img:hover {
    box-shadow: 0 0.20833rem 0.41667rem 0 rgba(14, 107, 56, 0.33); }

.home-7 img.lozad {
  transition: 0s; }

@media (max-width: 1023.98px) {
  .home-7 .col-right {
    margin-left: auto;
    width: 90%;
    margin-bottom: 2.083333333333333rem;
    border-radius: 0.8333333333333334rem }
    .home-7 .col-right .img img {
    border-radius: 0.8333333333333334rem } }

.district-1 select {
  border-radius: 0.20833333333333334rem;
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(14 107 56 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(129 129 129 / var(--tw-text-opacity, 1));
  height: clamp(40px, calc(48/1920*100rem), calc(48/1920*100rem));
  width: 100%;
  flex: 1 1 auto }

@media (min-width: 576px) {

  .district-1 select {
    max-width: clamp(273px, calc(273/1920*100rem), calc(273/1920*100rem)) } }

.distric-2 li {
  border-bottom-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  border-color: transparent;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }
  .distric-2 li:hover {
  --tw-border-opacity: 1;
  border-color: rgb(14 107 56 / var(--tw-border-opacity, 1)) }
  .distric-2 li a {
  font-weight: 500;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }
    .distric-2 li a:hover {
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }
  .distric-2 li span {
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1)) }

@media (max-width: 1023.98px) {
  .product-detail-1-1 .rate-page {
    padding-top: 0;
    padding-bottom: 0 } }

.product-detail-1-1 .rate-page .container, .product-detail-1-1 .rate-page header .wrap-bottom .mega-menu-inner, header .wrap-bottom .product-detail-1-1 .rate-page .mega-menu-inner {
  padding-left: 0;
  padding-right: 0 }

.product-detail-1-1 .img .content::before {
  content: '';
  border-radius: 16px;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.5) 100%);
  position: absolute;
  inset: 0;
  z-index: -1 }

.product-detail-1-1 .img .content + .play-icon {
  opacity: 0.5 }

.product-detail-1-1 .share {
  left: -4.**************6rem;
  top: 0 }

@media (min-width: 1200px) {

  .product-detail-1-1 .share {
    position: absolute } }
  .product-detail-1-1 .share .share-wrapper > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(clamp(10px, calc(10/1920*100rem), calc(10/1920*100rem)) * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(clamp(10px, calc(10/1920*100rem), calc(10/1920*100rem)) * var(--tw-space-y-reverse)) }

@media (max-width: 1199.98px) {
  .product-detail-1-1 .share .share-wrapper {
    margin-top: 0.8333333333333334rem;
    display: flex;
    gap: 0.625rem }
  .product-detail-1-1 .share .share-wrapper > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0 * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0 * var(--tw-space-y-reverse)) } }
  .product-detail-1-1 .share a {
  width: 2.083333333333333rem;
  height: 2.083333333333333rem;
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(239 239 239 / var(--tw-border-opacity, 1));
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.20833333333333334rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }
    .product-detail-1-1 .share a:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(244 118 33 / var(--tw-bg-opacity, 1)) }
      .product-detail-1-1 .share a:hover i {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }
  .product-detail-1-1 .share i {
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }

.product-detail-1-2 [class*='box-content'] + [class*='box-content'] {
  margin-top: 2.083333333333333rem;
  border-top-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(14 107 56 / var(--tw-border-opacity, 1));
  padding-top: 2.083333333333333rem }

.product-detail-1-2 .box-tab .map-ratio {
  border-radius: 0.8333333333333334rem }

.product-detail-1-2 .box-tab ul {
  display: flex;
  gap: 0.625rem;
  overflow: auto;
  white-space: nowrap;
  overscroll-behavior: contain;
  transform: translate3d(0, 0, 0);
  will-change: transform }

@media (max-width: 575.98px) {
  .product-detail-1-2 .box-tab li {
    flex: 1 1 0% } }

.product-detail-1-2 .box-tab li a {
  border-radius: 0.20833333333333334rem;
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(220 220 220 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  display: block;
  padding-top: 0.4**************7rem;
  padding-bottom: 0.4**************7rem;
  padding-left: 0.625rem;
  padding-right: 0.625rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }

@media (min-width: 576px) {

  .product-detail-1-2 .box-tab li a {
    padding-left: 1.25rem;
    padding-right: 1.25rem } }

.product-detail-1-2 .box-tab li.active a, .product-detail-1-2 .box-tab li:hover a {
  --tw-border-opacity: 1;
  border-color: rgb(244 118 33 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1)) }

.product-detail-1-2 .box-content .description table {
  display: block }

.product-detail-1-2 .box-content .description tbody {
  display: block }
  .product-detail-1-2 .box-content .description tbody img {
  border-radius: 0.8333333333333334rem }
  .product-detail-1-2 .box-content .description tbody tr, .product-detail-1-2 .box-content .description tbody td {
    border: 0 !important; }
  .product-detail-1-2 .box-content .description tbody td {
  padding: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important }
  .product-detail-1-2 .box-content .description tbody tr {
  padding: 1.0416666666666665rem !important;
  gap: 24px }
  @media (min-width: 1200px) {

  .product-detail-1-2 .box-content .description tbody tr {
    gap: 2.083333333333333rem } }
  .product-detail-1-2 .box-content .description tbody tr {
  display: grid;
  align-items: center;
  border-radius: 0.8333333333333334rem;
  background-color: rgb(14 107 56 / 0.05) }
  @media (min-width: 768px) {

  .product-detail-1-2 .box-content .description tbody tr {
    grid-template-columns: repeat(2, minmax(0, 1fr)) } }

.product-detail-1-2 .box-content .description td {
  text-align: justify;
  font-weight: 500 }

.product-detail-1-2 .box-content .description h3 {
  font-size: clamp(20px, calc(20/1920*100rem), calc(20/1920*100rem));
  line-height: 1.3 }

@media (min-width: 1200px) {

  .product-detail-1-2 .box-content .description h3 {
    font-size: 1.0416666666666667rem } }

.product-detail-1-2 .box-content .description h3 {
  font-weight: 700 }

.product-detail-1-2 .bottom-layout-table {
  font-size: clamp(18px, calc(18/1920*100rem), calc(18/1920*100rem));
  line-height: 1.33333 }

@media (min-width: 1200px) {

  .product-detail-1-2 .bottom-layout-table {
    font-size: 0.9375rem } }

.product-detail-1-2 .bottom-layout-table {
  text-align: justify;
  font-weight: 700 }
  .product-detail-1-2 .bottom-layout-table table {
  width: 100% }
    .product-detail-1-2 .bottom-layout-table table td, .product-detail-1-2 .bottom-layout-table table tr {
  border-top-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  border-bottom-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(217 217 217 / var(--tw-border-opacity, 1));
  padding-top: 0.625rem;
  padding-bottom: 0.625rem }
    .product-detail-1-2 .bottom-layout-table table tr td:last-child {
  text-align: right }

.pd-sidebar {
  top: 5.833333333333333rem;
  position: sticky }
  .pd-sidebar .box + .box {
  margin-top: 1.0416666666666665rem;
  border-top-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(239 239 239 / var(--tw-border-opacity, 1));
  padding-top: 1.0416666666666665rem }
  .pd-sidebar .box .btn {
  font-weight: 400 }
  .pd-sidebar .box-square .square i {
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }
  .pd-sidebar .box-contact .btn i {
  font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
  line-height: 1.28571 }
  @media (min-width: 1200px) {

  .pd-sidebar .box-contact .btn i {
    font-size: 0.7291666666666667rem } }
  .pd-sidebar .box-contact .btn i {
  font-weight: 300 }
  .pd-sidebar .supports .swiper-column-auto {
    --mr: calc(8/1920*100rem);
    --spv: 3; }

.box-list-item-icon .icon-box {
  width: 1.6666666666666667rem;
  height: 1.6666666666666667rem;
  flex: none }
  .box-list-item-icon .icon-box img {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
  padding: 10% }

.recruit-list-4 table {
  margin-top: 2.083333333333333rem;
  width: 100% }
  .recruit-list-4 table thead {
  --tw-bg-opacity: 1;
  background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1)) }

@media (max-width: 767.98px) {
  .recruit-list-4 table thead {
    display: none } }
  .recruit-list-4 table th {
  font-size: clamp(20px, calc(20/1920*100rem), calc(20/1920*100rem));
  line-height: 1.3 }
  @media (min-width: 1200px) {

  .recruit-list-4 table th {
    font-size: 1.0416666666666667rem } }
  .recruit-list-4 table th {
  padding-top: 0.5208333333333333rem;
  padding-bottom: 0.5208333333333333rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }

@media (max-width: 767.98px) {
  .recruit-list-4 table tbody {
    display: block;
    width: 100% }
  .recruit-list-4 table tbody > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.0416666666666665rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.0416666666666665rem * var(--tw-space-y-reverse)) } }
  .recruit-list-4 table tbody tr {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }
    @media (min-width: 768px) {

  .recruit-list-4 table tbody tr:nth-of-type(even) {
    --tw-bg-opacity: 1;
    background-color: rgb(246 246 246 / var(--tw-bg-opacity, 1)) }
  .recruit-list-4 table tbody tr:hover {
    background-color: rgb(14 107 56 / 0.1) }
  .recruit-list-4 table tbody tr td:nth-of-type(2) {
    padding-left: 1.0416666666666665rem;
    padding-right: 1.0416666666666665rem;
    text-align: left } }

@media (max-width: 767.98px) {
  .recruit-list-4 table tbody tr {
    display: grid;
    width: 100% }
    .recruit-list-4 table tbody tr td {
    display: flex;
    align-items: baseline;
    justify-content: space-between;
    padding-left: 0.625rem;
    padding-right: 0.625rem }
      .recruit-list-4 table tbody tr td:nth-of-type(1) {
    display: none }
      .recruit-list-4 table tbody tr td:nth-of-type(2) {
    order: 1;
    --tw-bg-opacity: 1;
    background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1));
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
    font-weight: 700;
    text-align: center }
        .recruit-list-4 table tbody tr td:nth-of-type(2)::before {
    display: none }
      .recruit-list-4 table tbody tr td:nth-of-type(3) {
    order: 3;
    border-top-width: 0px }
      .recruit-list-4 table tbody tr td:nth-of-type(4) {
    order: 4;
    border-top-width: 0px }
      .recruit-list-4 table tbody tr td:nth-of-type(5) {
    order: 5;
    border-top-width: 0px }
      .recruit-list-4 table tbody tr td::before {
        content: attr(data-title); } }
  .recruit-list-4 table tbody td {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  cursor: pointer;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms }
    .recruit-list-4 table tbody td:first-child {
  font-weight: 700 }

@media (min-width: 768px) {
  .recruit-list-4 table tbody .download-cell:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(14 107 56 / var(--tw-bg-opacity, 1)) }
    .recruit-list-4 table tbody .download-cell:hover .download {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1)) } }

.recruit-list-4 th, .recruit-list-4 td {
  border-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem));
  --tw-border-opacity: 1;
  border-color: rgb(220 220 220 / var(--tw-border-opacity, 1)) }

.recruit-detail .item.deadline .label {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1)) }

@media (min-width: 576px) {

  .sm\:body-14 {
    font-size: clamp(14px, calc(14/1920*100rem), calc(14/1920*100rem));
    line-height: 1.28571 }

  .sm\:body-16 {
    font-size: clamp(16px, calc(16/1920*100rem), calc(16/1920*100rem));
    line-height: 1.375 }

  .sm\:body-18 {
    font-size: clamp(18px, calc(18/1920*100rem), calc(18/1920*100rem));
    line-height: 1.33333 }

  .sm\:subheader-20 {
    font-size: clamp(20px, calc(20/1920*100rem), calc(20/1920*100rem));
    line-height: 1.3 }

  .sm\:subheader-24 {
    font-size: clamp(22px, calc(24/1920*100rem), calc(24/1920*100rem));
    line-height: 1.25 }
  @media (min-width: 1200px) {

    .sm\:body-14 {
      font-size: 0.7291666666666667rem }

    .sm\:body-16 {
      font-size: 0.8333333333333334rem }

    .sm\:body-18 {
      font-size: 0.9375rem }

    .sm\:subheader-20 {
      font-size: 1.0416666666666667rem }

    .sm\:subheader-24 {
      font-size: 1.25rem } } }

@media (min-width: 1024px) {

  .lg\:base-gap {
    gap: 24px; }

  @media (min-width: 1200px) {

    .lg\:base-gap {
      gap: 2.083333333333333rem } }

  .lg\:subheader-20 {
    font-size: clamp(20px, calc(20/1920*100rem), calc(20/1920*100rem));
    line-height: 1.3 }

  .lg\:subheader-24 {
    font-size: clamp(22px, calc(24/1920*100rem), calc(24/1920*100rem));
    line-height: 1.25 }
  @media (min-width: 1200px) {

    .lg\:subheader-20 {
      font-size: 1.0416666666666667rem }

    .lg\:subheader-24 {
      font-size: 1.25rem } } }

.\[\&_i\]\:header-32 i {
  font-size: 1.6666666666666667rem;
  line-height: 1.25 }

.before\:static::before {
  content: var(--tw-content);
  position: static }

.before\:flex-1::before {
  content: var(--tw-content);
  flex: 1 1 0% }

.after\:pointer-events-none::after {
  content: var(--tw-content);
  pointer-events: none }

.after\:static::after {
  content: var(--tw-content);
  position: static }

.after\:my-5::after {
  content: var(--tw-content);
  margin-top: 1.0416666666666665rem;
  margin-bottom: 1.0416666666666665rem }

.after\:h-0\.5::after {
  content: var(--tw-content);
  height: 0.10416666666666667rem }

.after\:w-15::after {
  content: var(--tw-content);
  width: 3.125rem }

.after\:flex-1::after {
  content: var(--tw-content);
  flex: 1 1 0% }

.after\:bg-primary-2::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(244 118 33 / var(--tw-bg-opacity, 1)) }

.after\:content-\[attr\(data-suffix\)\]::after {
  --tw-content: attr(data-suffix);
  content: var(--tw-content) }

.first\:border-t:first-child {
  border-top-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem)) }

.first\:pt-0:first-child {
  padding-top: 0 }

.last\:border-b-0:last-child {
  border-bottom-width: 0px }

.odd\:bg-primary-1\/5:nth-child(odd) {
  background-color: rgb(14 107 56 / 0.05) }

.even\:bg-primary-1\/5:nth-child(even) {
  background-color: rgb(14 107 56 / 0.05) }

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }

.hover\:border-primary-1:hover {
  --tw-border-opacity: 1;
  border-color: rgb(14 107 56 / var(--tw-border-opacity, 1)) }

.hover\:bg-neutral-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 239 239 / var(--tw-bg-opacity, 1)) }

.hover\:bg-neutral-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(246 246 246 / var(--tw-bg-opacity, 1)) }

.hover\:bg-primary-2:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(244 118 33 / var(--tw-bg-opacity, 1)) }

.hover\:text-neutral-300:hover {
  --tw-text-opacity: 1;
  color: rgb(189 189 189 / var(--tw-text-opacity, 1)) }

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) }

.group:last-child .group-last\:border-b-0 {
  border-bottom-width: 0px }

.group:hover .group-hover\:top-0 {
  top: 0 }

.group:hover .group-hover\:font-bold {
  font-weight: 700 }

.group:hover .group-hover\:text-neutral-300 {
  --tw-text-opacity: 1;
  color: rgb(189 189 189 / var(--tw-text-opacity, 1)) }

.group:hover .group-hover\:text-primary-1 {
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }

.group:hover .group-hover\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) }

.group:hover .group-hover\:underline {
  text-decoration-line: underline }

.group:hover .group-hover\:opacity-0 {
  opacity: 0 }

.group.active .group-\[\.active\]\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }

.group.active .group-\[\.active\]\:border-neutral-300 {
  --tw-border-opacity: 1;
  border-color: rgb(189 189 189 / var(--tw-border-opacity, 1)) }

.group.active .group-\[\.active\]\:bg-primary-2 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 118 33 / var(--tw-bg-opacity, 1)) }

.group.active .group-\[\.active\]\:pb-4 {
  padding-bottom: 0.8333333333333334rem }

.group.active .group-\[\.active\]\:font-bold {
  font-weight: 700 }

.group.active .group-\[\.active\]\:text-neutral-300 {
  --tw-text-opacity: 1;
  color: rgb(189 189 189 / var(--tw-text-opacity, 1)) }

.group.active .group-\[\.active\]\:text-primary-1 {
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }

.group.active .group-\[\.active\]\:text-primary-2 {
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1)) }

.rem\:top-\[110px\] {
  top: 5.729166666666667rem }

.rem\:top-\[98px\] {
  top: 5.104166666666667rem }

.rem\:mt-12 {
  margin-top: 2.5rem }

.rem\:mt-2\.5 {
  margin-top: 0.5208333333333333rem }

.rem\:mt-8 {
  margin-top: 1.6666666666666667rem }

.rem\:mt-\[120px\] {
  margin-top: 6.25rem }

.rem\:mt-\[5px\] {
  margin-top: 0.2604166666666667rem }

.rem\:mt-\[60px\] {
  margin-top: 3.125rem }

.rem\:max-h-\[240px\] {
  max-height: 12.5rem }

.rem\:max-h-\[392px\] {
  max-height: 20.416666666666668rem }

.rem\:w-\[200px\] {
  width: 10.416666666666668rem }

.rem\:max-w-\[379px\] {
  max-width: 19.739583333333336rem }

.rem\:max-w-\[420px\] {
  max-width: 21.875rem }

.rem\:py-2\.5 {
  padding-top: 0.5208333333333333rem;
  padding-bottom: 0.5208333333333333rem }

.rem\:py-\[7px\] {
  padding-top: 0.36**************7rem;
  padding-bottom: 0.36**************7rem }

.rem\:pb-\[15px\] {
  padding-bottom: 0.78125rem }

.rem\:text-\[22px\] {
  font-size: 1.1**************5rem }

.rem\:text-\[40px\] {
  font-size: 2.0833333333333335rem }

.rem\:text-\[48px\] {
  font-size: 2.5rem }

.rem\:text-\[58px\] {
  font-size: 3.0208333333333335rem }

.rem\:tracking-\[0\.88px\] {
  letter-spacing: 0.0**************34rem }

.rem\:blur-\[20px\] {
  --tw-blur: blur(1.0416666666666667rem);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) }

.rem\:backdrop-blur-\[10px\] {
  --tw-backdrop-blur: blur(0.5208333333333334rem);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia) }

.clamp\:mb-\[-1px\] {
  margin-bottom: clamp(-1px, calc(-1/1920*100rem), calc(-1/1920*100rem)) }

.clamp\:size-10 {
  width: clamp(39.99999999999999px, calc(39.99999999999999/1920*100rem), calc(39.99999999999999/1920*100rem));
  height: clamp(39.99999999999999px, calc(39.99999999999999/1920*100rem), calc(39.99999999999999/1920*100rem)) }

.clamp\:min-w-5 {
  min-width: clamp(19.999999999999996px, calc(19.999999999999996/1920*100rem), calc(19.999999999999996/1920*100rem)) }

.clamp\:min-w-\[160px\] {
  min-width: clamp(160px, calc(160/1920*100rem), calc(160/1920*100rem)) }

.clamp\:max-w-\[1004px\] {
  max-width: clamp(1004px, calc(1004/1920*100rem), calc(1004/1920*100rem)) }

.clamp\:max-w-\[184px\] {
  max-width: clamp(184px, calc(184/1920*100rem), calc(184/1920*100rem)) }

.clamp\:gap-\[8px\] {
  gap: clamp(8px, calc(8/1920*100rem), calc(8/1920*100rem)) }

@media (min-width: 576px) {

  .sm\:hidden {
    display: none }

  .sm\:w-1\/3 {
    width: 33.333333% }

  .sm\:w-20 {
    width: 4.**************6rem }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr)) }

  .sm\:gap-5 {
    gap: 1.0416666666666665rem }

  .sm\:px-10 {
    padding-left: 2.083333333333333rem;
    padding-right: 2.083333333333333rem }

  .sm\:px-6 {
    padding-left: 1.25rem;
    padding-right: 1.25rem }

  .sm\:py-9 {
    padding-top: 1.875rem;
    padding-bottom: 1.875rem } }

@media (min-width: 768px) {

  .md\:order-none {
    order: 0 }

  .md\:mx-10 {
    margin-left: 2.083333333333333rem;
    margin-right: 2.083333333333333rem }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr)) }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr)) }

  .md\:grid-cols-\[calc\(360\/960\*100\%\)_1fr\] {
    grid-template-columns: calc(360 / 960 * 100%) 1fr }

  .md\:gap-5 {
    gap: 1.0416666666666665rem }

  .md\:space-y-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.6666666666666667rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.6666666666666667rem * var(--tw-space-y-reverse)) }

  .md\:p-10 {
    padding: 2.083333333333333rem }

  .group:nth-child(even) .md\:group-even\:order-2 {
    order: 2 }

  .md\:rem\:px-\[60px\] {
    padding-left: 3.125rem;
    padding-right: 3.125rem } }

@media (min-width: 1024px) {

  .lg\:col-span-1 {
    grid-column: span 1 / span 1 }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3 }

  .lg\:col-span-4 {
    grid-column: span 4 / span 4 }

  .lg\:col-span-5 {
    grid-column: span 5 / span 5 }

  .lg\:col-span-6 {
    grid-column: span 6 / span 6 }

  .lg\:col-span-7 {
    grid-column: span 7 / span 7 }

  .lg\:col-span-8 {
    grid-column: span 8 / span 8 }

  .lg\:col-span-9 {
    grid-column: span 9 / span 9 }

  .lg\:mx-0 {
    margin-left: 0;
    margin-right: 0 }

  .lg\:ml-unset {
    margin-left: unset }

  .lg\:mt-13 {
    margin-top: 2.7083333333333335rem }

  .lg\:mt-14 {
    margin-top: 2.9**************5rem }

  .lg\:mt-6 {
    margin-top: 1.25rem }

  .lg\:block {
    display: block }

  .lg\:w-1\/5 {
    width: 20% }

  .lg\:max-w-full {
    max-width: 100% }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr)) }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr)) }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr)) }

  .lg\:grid-cols-\[calc\(420\/1260\*100\%\)_1fr\] {
    grid-template-columns: calc(420 / 1260 * 100%) 1fr }

  .lg\:grid-cols-\[calc\(544\/1400\*100\%\)_1fr\] {
    grid-template-columns: calc(544 / 1400 * 100%) 1fr }

  .lg\:grid-cols-\[calc\(660\/1320\*100\%\)_1fr\] {
    grid-template-columns: calc(660 / 1320 * 100%) 1fr }

  .lg\:grid-cols-\[calc\(960\/1400\*100\%\)_1fr\] {
    grid-template-columns: calc(960 / 1400 * 100%) 1fr }

  .lg\:gap-0 {
    gap: 0 }

  .lg\:gap-x-10 {
    -moz-column-gap: 2.083333333333333rem;
         column-gap: 2.083333333333333rem }

  .lg\:p-12 {
    padding: 2.5rem }

  .lg\:pl-10 {
    padding-left: 2.083333333333333rem }

  .lg\:pr-10 {
    padding-right: 2.083333333333333rem }

  .lg\:text-left {
    text-align: left }

  .lg\:rem\:max-w-\[528px\] {
    max-width: 27.5rem }

  .lg\:rem\:text-\[128px\] {
    font-size: 6.666666666666667rem }

  .lg\:rem\:text-\[60px\] {
    font-size: 3.125rem } }

@media (min-width: 1200px) {

  .xl\:top-1\/2 {
    top: 50% }

  .xl\:col-span-3 {
    grid-column: span 3 / span 3 }

  .xl\:col-span-4 {
    grid-column: span 4 / span 4 }

  .xl\:col-span-8 {
    grid-column: span 8 / span 8 }

  .xl\:col-span-9 {
    grid-column: span 9 / span 9 }

  .xl\:-mr-10 {
    margin-right: -2.083333333333333rem }

  .xl\:-mr-2 {
    margin-right: -0.4**************7rem }

  .xl\:\!hidden {
    display: none !important }

  .xl\:hidden {
    display: none }

  .xl\:w-13 {
    width: 2.7083333333333335rem }

  .xl\:-translate-y-1\/2 {
    --tw-translate-y: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }

  .xl\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr)) }

  .xl\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr)) }

  .xl\:justify-between {
    justify-content: space-between }

  .xl\:gap-0 {
    gap: 0 }

  .xl\:gap-15 {
    gap: 3.125rem }

  .xl\:gap-8 {
    gap: 1.6666666666666667rem }

  .xl\:gap-\[calc\(120\/1400\*100\%\)\] {
    gap: calc(120 / 1400 * 100%) }

  .xl\:gap-\[calc\(60\/1400\*100\%\)\] {
    gap: calc(60 / 1400 * 100%) }

  .xl\:gap-\[calc\(80\/1260\*100\%\)\] {
    gap: calc(80 / 1260 * 100%) }

  .xl\:space-y-10 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(2.083333333333333rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(2.083333333333333rem * var(--tw-space-y-reverse)) }

  .xl\:overflow-visible {
    overflow: visible }

  .xl\:border-b {
    border-bottom-width: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem)) }

  .xl\:py-20 {
    padding-top: 4.**************6rem;
    padding-bottom: 4.**************6rem }

  .xl\:pb-20 {
    padding-bottom: 4.**************6rem }

  .xl\:pl-10 {
    padding-left: 2.083333333333333rem }

  .xl\:pl-20 {
    padding-left: 4.**************6rem }

  .xl\:pl-3 {
    padding-left: 0.625rem }

  .xl\:pl-30 {
    padding-left: 6.25rem }

  .xl\:pl-8 {
    padding-left: 1.6666666666666667rem }

  .xl\:pr-1 {
    padding-right: 0.20833333333333334rem }

  .xl\:pr-15 {
    padding-right: 3.125rem }

  .xl\:pr-20 {
    padding-right: 4.**************6rem }

  .xl\:pr-30 {
    padding-right: 6.25rem }

  .xl\:pt-20 {
    padding-top: 4.**************6rem }

  .xl\:pt-4 {
    padding-top: 0.8333333333333334rem }

  .xl\:pt-\[90px\] {
    padding-top: 90px }

  .xl\:text-primary-1 {
    --tw-text-opacity: 1;
    color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }

  .group:hover .xl\:group-hover\:-translate-y-4 {
    --tw-translate-y: -0.8333333333333334rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) }

  .xl\:rem\:h-\[42px\] {
    height: 2.1875rem }

  .xl\:rem\:max-w-\[360px\] {
    max-width: 18.75rem }

  .xl\:rem\:max-w-\[477px\] {
    max-width: 24.84375rem }

  .xl\:rem\:max-w-\[700px\] {
    max-width: 36.**************6rem }

  .xl\:rem\:gap-x-\[60px\] {
    -moz-column-gap: 3.125rem;
         column-gap: 3.125rem }

  .xl\:clamp\:mr-\[1px\] {
    margin-right: clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem)) }

  .xl\:clamp\:space-x-\[1px\] > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem)) * var(--tw-space-x-reverse));
    margin-left: calc(clamp(1px, calc(1/1920*100rem), calc(1/1920*100rem)) * calc(1 - var(--tw-space-x-reverse))) } }

@media (min-width: 1440px) {

  .\32xl\:gap-\[120px\] {
    gap: 120px } }

@media (max-width: 767.98px) {

  .-md\:flex-col {
    flex-direction: column } }

@media (max-width: 1023.98px) {

  .-lg\:order-2 {
    order: 2 }

  .-lg\:\!mx-\[-12px\] {
    margin-left: -12px !important;
    margin-right: -12px !important }

  .-lg\:max-w-\[80\%\] {
    max-width: 80% }

  .-lg\:flex-col {
    flex-direction: column } }

@media (max-width: 1199.98px) {

  .-xl\:relative {
    position: relative }

  .-xl\:bottom-0 {
    bottom: 0 } }

.\[\&_a\]\:h-full a {
  height: 100% }

.\[\&_i\]\:text-primary-1 i {
  --tw-text-opacity: 1;
  color: rgb(14 107 56 / var(--tw-text-opacity, 1)) }

.\[\&_i\]\:text-primary-2 i {
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1)) }

.\[\&_img\]\:size-full img {
  width: 100%;
  height: 100% }

.\[\&_img\]\:h-10 img {
  height: 2.083333333333333rem }

.\[\&_img\]\:rounded-2 img {
  border-radius: 0.4**************7rem }

.\[\&_img\]\:rounded-4 img {
  border-radius: 0.8333333333333334rem }

.\[\&_img\]\:object-cover img {
  -o-object-fit: cover;
     object-fit: cover }

.\[\&_img\]\:p-\[18\%\] img {
  padding: 18% }

.\[\&_img\]\:p-\[19\%\] img {
  padding: 19% }

.\[\&_span\]\:text-primary-2 span {
  --tw-text-opacity: 1;
  color: rgb(244 118 33 / var(--tw-text-opacity, 1)) }

.\[\&_video\]\:size-full video {
  width: 100%;
  height: 100% }

.\[\&_video\]\:object-cover video {
  -o-object-fit: cover;
     object-fit: cover }

/*# sourceMappingURL=main.min.css.map */
