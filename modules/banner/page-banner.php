<?php
$terms = get_queried_object();
$id_category = $terms->term_id;
$taxonomy = $terms->taxonomy;
$name = $terms->name;
if ($id_category) {
	$id = $taxonomy . '_' . $id_category;
} else {
	$id = get_the_ID();
}
$is_single = is_single();
if ($is_single) {
	$name = get_the_title();
}
if (is_singular('recruitment')) {
	$taxonomies = get_post_taxonomies($id);
	$post_categories = get_post_primary_category($id, $taxonomies[0]);
	$primary_category = $post_categories['primary_category'];
	if ($primary_category) {
		$id = $primary_category->taxonomy . '_' . $primary_category->term_id;
	}
}
$banners = get_field('banner_select_page', $id);
$current_page_ID = get_the_ID();
?>

<?php if ($banners) : ?>

	<div class="page-banner">
		<div class="img">
			<a class="ratio-[560/1920] bg-neutral-50">

				<?php
				$thumb_id = get_post_thumbnail_id($banners[0]);

				// <PERSON><PERSON><PERSON> hàm custom_lozad_image để hiển thị hình ảnh
				echo custom_lozad_image($thumb_id, 'full');
				?>
			</a>
		</div>
	</div>
	<!--  -->
<?php endif; ?>