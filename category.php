<?php get_header(); ?>

<?php
$terms = get_queried_object();
$taxonomy = $terms->taxonomy;
$current_parent = $terms->parent;
$taxonomyID = $terms->term_taxonomy_id;
$acf_key = $taxonomy . '_' . $taxonomyID;


$depthParentID = get_term_depth($taxonomy, 0, $taxonomyID);
$has_child = get_terms(array(
    'taxonomy'  => $taxonomy,
    'hide_empty' => false,
    'parent' => $depthParentID,
));
$parent_term = get_term($depthParentID, $taxonomy);


$paged = (get_query_var('paged')) ? absint(get_query_var('paged')) : 1;
$post_type = get_taxonomy($taxonomy)->object_type[0];
$args = array(
    'post_type' => $post_type,
    'posts_per_page' => 10,
    'cat' => $taxonomyID,
    'paged' => $paged,
    'orderby' => 'date',
    'order' => 'DESC',
);
$the_query = new WP_Query($args);
$count = count($the_query->posts);

?>



<section class="news-list section">
    <div class="container">
        <div class="top flex items-center justify-between flex-wrap gap-4">
            <h1 class="block-title">Tin tức - Sự kiện</h1>
            <nav>
                <ul>
                    <li class="<?php if ($taxonomyID === $depthParentID) : ?> active <?php endif; ?>"><a href="<?= get_term_link($depthParentID) ?>" title="<?php _e('Tất cả', 'canhcamtheme'); ?>"><?php _e('Tất cả', 'canhcamtheme'); ?></a></li>
                    <?php foreach ($has_child as $child_term): ?>
                        <li class="<?php if ($child_term->term_id == $taxonomyID) : ?> active <?php endif; ?>"><a href="<?= get_term_link($child_term->term_id) ?>" title="<?php _e('Tất cả', 'canhcamtheme'); ?>"><?= $child_term->name ?></a></li>
                    <?php endforeach; ?>
                </ul>
            </nav>
        </div>
        <div class="list space-y-[24px] xl:space-y-10 mt-10">
            <?php if ($the_query->have_posts()) : ?>
                <?php
                $key_top = 0;
                ?>
                <div class="grid lg:grid-cols-2 base-gap">
                    <?php if ($the_query->have_posts()) : while ($the_query->have_posts()) : $the_query->the_post(); ?>
                            <?php
                            $key_top++;
                            ?>
                            <?php if ($key_top === 1) : ?>
                                <?= get_template_part('/components/boxNews/boxNews-big', '', ['id' => get_the_ID()]) ?>
                            <?php endif; ?>
                            <?php if ($key_top === 2) : ?>
                                <div class="small space-y-4">
                                <?php endif; ?>
                                <?php if ($key_top > 1 &&  $key_top < 5) : ?>
                                    <?= get_template_part('/components/boxNews/boxNews-small', '', ['id' => get_the_ID()]) ?>
                                <?php endif; ?>
                                <?php if ($key_top === 4 || $key_top > 1 &&  $key_top < 5 && $key_top === $count) : ?>
                                </div>
                            <?php endif; ?>
                    <?php endwhile;
                    endif;
                    wp_reset_postdata(); ?>

                </div>
                <?php
                $key_bottom = 0
                ?>
                <?php if ($count > 4) : ?>
                    <div class="grid sm:grid-cols-2 lg:grid-cols-3 base-gap">
                        <?php if ($the_query->have_posts()) : while ($the_query->have_posts()) : $the_query->the_post(); ?>
                                <?php
                                $key_bottom++;
                                ?>
                                <?php if ($key_bottom > 4) : ?>
                                    <?= get_template_part('/components/boxNews/boxNews-1', '', ['id' => get_the_ID()]) ?>
                                <?php endif; ?>
                        <?php endwhile;
                        endif;
                        wp_reset_postdata(); ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
        <?= wp_bootstrap_pagination(array("custom_query" => $the_query)) ?>
    </div>
</section>
<?php get_footer(); ?>