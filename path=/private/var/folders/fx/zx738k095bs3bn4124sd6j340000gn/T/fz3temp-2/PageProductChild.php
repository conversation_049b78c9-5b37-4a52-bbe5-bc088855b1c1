<?php

/*
Template Name: Page Product Child
*/

// Get sort parameter from URL
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'default';

?>

<div class="filter flex items-center gap-2 sm:gap-5 mt-10 flex-wrap">
    <div class="label font-bold text-black">Sắp xếp theo</div>
    <select name="sort" id="sort" onchange="window.location.href = '<?php echo esc_js(add_query_arg('sort', this.value, remove_query_arg('paged'))); ?>'">
        <option value="default" <?php selected($sort, 'default'); ?>>Mặc định</option>
        <option value="price-asc" <?php selected($sort, 'price-asc'); ?>>Gi<PERSON> từ thấp đến cao</option>
        <option value="price-desc" <?php selected($sort, 'price-desc'); ?>><PERSON><PERSON><PERSON> từ cao đến thấp</option>
    </select>
</div> 