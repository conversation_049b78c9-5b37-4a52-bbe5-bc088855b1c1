                // <PERSON><PERSON> lý hiển thị cho Hạng tòa nhà và Hướng tòa nhà
                if ($thong_so['item_key'] == 'hang-toa-nha' && !empty($value) && is_numeric($value)) {
                    $post = get_post($value);
                    if ($post) {
                        $value = $post->post_title;
                    }
                } elseif ($thong_so['item_key'] == 'huong-toa-nha' && !empty($value)) {
                    if (is_array($value)) {
                        $titles = array();
                        foreach ($value as $post_id) {
                            if (is_numeric($post_id)) {
                                $post = get_post($post_id);
                                if ($post) {
                                    $titles[] = $post->post_title;
                                }
                            }
                        }
                        $value = !empty($titles) ? implode(', ', $titles) : $value;
                    } elseif (is_numeric($value)) {
                        $post = get_post($value);
                        if ($post) {
                            $value = $post->post_title;
                        }
                    }
                } 