<?php
get_header();

global $post;
$post_id = $post->ID;
$post_type = get_post_type($post_id);
$city_id = get_post_meta($post_id, '_office_city', true);
$district_id = get_post_meta($post_id, '_office_district', true);
$ward_id = get_post_meta($post_id, '_office_ward', true);
$street_id = get_post_meta($post_id, '_office_street', true);
$house_number = get_post_meta($post_id, '_office_house_number', true);


$full_address = '';
if (!empty($house_number)) {
    $full_address .= $house_number;
}
if (!empty($street_id)) {
    $full_address .= (!empty($full_address) ? ' - ' : '') . get_the_title($street_id);
}
if (!empty($ward_id)) {
    $full_address .= (!empty($full_address) ? ' - ' : '') . get_the_title($ward_id);
}
if (!empty($district_id)) {
    $full_address .= (!empty($full_address) ? ' - ' : '') . get_the_title($district_id);
}
if (!empty($city_id)) {
    $full_address .= (!empty($full_address) ? ' - ' : '') . get_the_title($city_id);
}

$office_district = get_post_meta($post_id, '_office_district', true);

// Query các văn phòng cho thuê cùng quận/huyện
$related_offices = new WP_Query(array(
    'post_type' => 'real-estate',
    'posts_per_page' => 8,
    // 'post__not_in' => array($post_id), // Loại trừ bài viết hiện tại
    'meta_query' => array(
        array(
            'key' => '_office_district',
            'value' => $office_district,
            'compare' => '='
        )
    )
));

$hangtoanha = get_field('dynamic_field_thong_so_toa_nha_hang-toa-nha', $post_id);
$related_offices_hangtoanha = null;
if ($hangtoanha) {
    // Query các văn phòng cho thuê cùng quận/huyện
    $related_offices_hangtoanha = new WP_Query(array(
        'post_type' => 'real-estate',
        'posts_per_page' => 8,
        // 'post__not_in' => array($post_id), // Loại trừ bài viết hiện tại
        'meta_query' => array(
            array(
                'key' => 'dynamic_field_thong_so_toa_nha_hang-toa-nha',
                'value' => $hangtoanha,
                'compare' => '='
            )
        )
    ));
}


?>


<div class="sticky-nav">
    <div class="container">
        <nav>
            <div class="scroll-prev"><i class="fa-solid fa-chevron-left"></i></div>
            <ul>
                <li><a href="#section-1">Initializing...</a></li>
            </ul>
            <div class="scroll-next"><i class="fa-solid fa-chevron-right"></i></div>
        </nav>
    </div>
</div>

<?php get_template_part('components/product/product-detail-1', '', array('post_id' => $post_id, 'full_address' => $full_address)); ?>
<?php get_template_part('components/product/product-detail-1-2', '', array('post_id' => $post_id, 'full_address' => $full_address, 'post_type' => $post_type)); ?>



<?php if ($related_offices->have_posts()) : ?>

    <section class="product-detail-1-3 scroll-section bg-primary-1/5 section xl:py-20" data-title="<?php echo __('Tòa nhà cùng khu vực', 'canhcamtheme'); ?>">
        <div class="container">
            <div class="swiper-column-auto category-item allow-touchMove auto-4-column">
                <div class="wrap-top-nav">
                    <h2 class="block-title"><?php echo __('Tòa nhà cùng khu vực', 'canhcamtheme'); ?></h2>
                    <div class="arrow-button close-arrow">
                        <div class="button-prev"></div>
                        <div class="button-next"></div>
                    </div>
                </div>
                <div class="swiper mt-10">
                    <div class="swiper-wrapper">
                        <?php
                        while ($related_offices->have_posts()) : $related_offices->the_post();
                            $post_id = get_the_ID();
                            echo '<div class="swiper-slide">';
                            get_template_part('components/boxProduct/box-rent-1', '', array('post_id' => $post_id));
                            echo '</div>';
                        endwhile;
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </section>


<?php
endif;
wp_reset_postdata();
?>


<?php
if ($related_offices_hangtoanha->have_posts()):

?>
    <section class="product-detail-1-4 scroll-section section xl:py-20" data-title="<?php echo __('Tòa nhà cùng phân khúc', 'canhcamtheme'); ?>">
        <div class="container">
            <div class="swiper-column-auto category-item allow-touchMove auto-4-column">
                <div class="wrap-top-nav">
                    <h2 class="block-title"><?php echo __('Tòa nhà cùng phân khúc', 'canhcamtheme'); ?></h2>
                    <div class="arrow-button close-arrow">
                        <div class="button-prev"></div>
                        <div class="button-next"></div>
                    </div>
                </div>
                <div class="swiper mt-10">
                    <div class="swiper-wrapper">
                        <?php
                        while ($related_offices_hangtoanha->have_posts()) : $related_offices_hangtoanha->the_post();
                            $post_id = get_the_ID();
                            echo '<div class="swiper-slide">';
                            get_template_part('components/boxProduct/box-rent-1', '', array('post_id' => $post_id));
                            echo '</div>';
                        endwhile;
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php

endif;
wp_reset_postdata();
?>




<?php get_template_part('components/product/box-footer-product'); ?>

<?php
get_footer();
?>