<?php
// ini_set('display_errors', 'Off');
// ini_set('error_reporting', E_ALL);
define('GENERATE_VERSION', '1.0.4');
$GLOBALS['CANHCAM'] = array();
// File header.php
// $body_class = get_field('add_class_body', get_the_ID());
// $body_class .= isset($GLOBALS['CANHCAM']['bg_body']) ? $GLOBALS['CANHCAM']['bg_body'] : '';

// File Template
// $GLOBALS['CANHCAM']['bg_body'] = 'has-menu-background';





if (class_exists('CanhCam_Licsence_Class')) {
    require get_template_directory() . '/inc/function-acf.php';
    require get_template_directory() . '/inc/function-root.php';
    require get_template_directory() . '/inc/function-custom.php';
    require get_template_directory() . '/inc/function-field.php';
    require get_template_directory() . '/inc/function-setup.php';
    require get_template_directory() . '/inc/function-ajax.php';
    require get_template_directory() . '/inc/function-pagination.php';
    require get_template_directory() . '/inc/function-form.php';
    require get_template_directory() . '/inc/function-account.php';
    require get_template_directory() . '/inc/function-product.php';
    require get_template_directory() . '/inc/function-office.php';
    require get_template_directory() . '/inc/function-import.php';
    require get_template_directory() . '/inc/function-breadcrumbs.php';
    require get_template_directory() . '/inc/function-filter.php';
}


if (is_admin()) {
    $userID    = get_current_user_id();
    $user_meta = get_userdata($userID);
    $user_role = $user_meta->roles;
    if ($userID) {
        // Disable wp core Update
        define('WP_AUTO_UPDATE_CORE', false);
        // Disable Theme/Plugin Update

        // add_filter('auto_update_core', '__return_false');
        // add_filter('auto_update_plugin', '__return_false');
        // add_filter('auto_update_theme', '__return_false');


        // Disable Theme/Plugin Editor

        // define('DISALLOW_FILE_MODS', true);
        // define('DISALLOW_FILE_EDIT', true);

        add_action('admin_menu', 'foxtail_remove_menus');
        // Remove
        add_action('admin_menu', 'foxtail_remove_unnecessary_wordpress_menus', 999);

        // Remove banner category from menu
        add_action('admin_menu', 'hide_banner_category_menu');
    }
    // Xóa dấu // trước các trang bạn muốn ẩn trong wp-admin
    function foxtail_remove_menus()
    {
        // remove_menu_page('plugins.php');               // Plugins
        // remove_menu_page('edit.php?post_type=acf-field-group');               // ACF
        // remove_menu_page('tools.php');               // Tool
        remove_menu_page('widgets.php');               // Tool
        remove_menu_page('edit-comments.php');               // Tool
    }
    function foxtail_remove_unnecessary_wordpress_menus()
    {
        remove_menu_page('edit.php?post_type=acf');
    }
    function hide_banner_category_menu()
    {
        remove_submenu_page('edit.php?post_type=banner', 'edit-tags.php?taxonomy=banner-cate&amp;post_type=banner');
    }
}




// Add custom filter for post title
add_filter('posts_where', function ($where, $query) {
    global $wpdb;
    if ($title = $query->get('post_title_like')) {
        $where .= ' AND ' . $wpdb->posts . '.post_title LIKE \'%' . esc_sql($wpdb->esc_like($title)) . '%\'';
    }
    return $where;
}, 10, 2);
