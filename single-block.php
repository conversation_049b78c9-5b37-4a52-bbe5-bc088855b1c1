<?php


global $post;
$post_id = $post->ID;
$district_id = get_field('district_id', $post_id);
$content_office = get_field('content_office', 'option');
get_header();


// Query for office-rent
$office_rent_args = array(
    'post_type' => 'office-rent',
    'posts_per_page' => 12,
    'post_status' => 'publish',
    'orderby' => 'date',
    'order' => 'DESC',
    'meta_query' => array(
        array(
            'key' => '_office_block',
            'value' => $post_id, // ID của quận cần lọc
            'compare' => '='
        )
    )
);
$office_rent_query = new WP_Query($office_rent_args);

// Query for office-rent-full
$office_rent_full_args = array(
    'post_type' => 'office-rent-full',
    'posts_per_page' => 12,
    'post_status' => 'publish',
    'orderby' => 'date',
    'order' => 'DESC',
    'meta_query' => array(
        array(
            'key' => '_office_block',
            'value' => $post_id,
            'compare' => '='
        )
    )
);
$office_rent_full_query = new WP_Query($office_rent_full_args);

// Query for business-space
$business_space_args = array(
    'post_type' => 'business-space',
    'posts_per_page' => 12,
    'post_status' => 'publish',
    'orderby' => 'date',
    'order' => 'DESC',
    'meta_query' => array(
        array(
            'key' => '_office_block',
            'value' => $post_id,
            'compare' => '='
        )
    )
);
$business_space_query = new WP_Query($business_space_args);

// Query for real-estate
$real_estate_args = array(
    'post_type' => 'real-estate',
    'posts_per_page' => 12,
    'post_status' => 'publish',
    'orderby' => 'date',
    'order' => 'DESC',
    'meta_query' => array(
        array(
            'key' => '_office_block',
            'value' => $post_id,
            'compare' => '='
        )
    )
);
$real_estate_query = new WP_Query($real_estate_args);

// Get streets by district_id
$streets = get_posts(array(
    'post_type' => 'street',
    'posts_per_page' => -1,
    'orderby' => 'title',
    'order' => 'ASC',
    'suppress_filters' => false,
    'meta_query' => array(
        array(
            'key' => 'district_id',
            'value' => $district_id
        )
    )
));

// // Prepare the list of streets with their post counts
// $streets_with_counts = array();

// foreach ($streets as $street) {
//     $street_id = $street->ID;

//     // Count posts for each post type
//     $office_rent_count = new WP_Query(array(
//         'post_type' => 'office-rent',
//         'posts_per_page' => -1,
//         'fields' => 'ids', // Only get post IDs for better performance
//         'meta_query' => array(
//             array(
//                 'key' => '_office_block',
//                 'value' => $street_id,
//                 'compare' => '='
//             )
//         )
//     ));

//     $office_rent_full_count = new WP_Query(array(
//         'post_type' => 'office-rent-full',
//         'posts_per_page' => -1,
//         'fields' => 'ids',
//         'meta_query' => array(
//             array(
//                 'key' => '_office_block',
//                 'value' => $street_id,
//                 'compare' => '='
//             )
//         )
//     ));

//     $business_space_count = new WP_Query(array(
//         'post_type' => 'business-space',
//         'posts_per_page' => -1,
//         'fields' => 'ids',
//         'meta_query' => array(
//             array(
//                 'key' => '_office_block',
//                 'value' => $street_id,
//                 'compare' => '='
//             )
//         )
//     ));

//     $real_estate_count = new WP_Query(array(
//         'post_type' => 'real-estate',
//         'posts_per_page' => -1,
//         'fields' => 'ids',
//         'meta_query' => array(
//             array(
//                 'key' => '_office_block',
//                 'value' => $street_id,
//                 'compare' => '='
//             )
//         )
//     ));

//     // Calculate total count across all post types
//     $total_count = $office_rent_count->found_posts +
//         $office_rent_full_count->found_posts +
//         $business_space_count->found_posts +
//         $real_estate_count->found_posts;

//     // Only add streets that have at least one post
//     if ($total_count > 0) {
//         $streets_with_counts[] = array(
//             'id' => $street_id,
//             'title' => $street->post_title,
//             'count' => $total_count,
//             'permalink' => get_permalink($street_id)
//         );
//     }

//     // Clean up queries to free memory
//     wp_reset_postdata();
// }

// // Sort streets by count (highest first)
// usort($streets_with_counts, function ($a, $b) {
//     return $b['count'] - $a['count'];
// });

// Get wards by district_id
$wards = get_posts(array(
    'post_type' => 'ward',
    'posts_per_page' => -1,
    'orderby' => 'title',
    'order' => 'ASC',
    'suppress_filters' => false,
    'meta_query' => array(
        array(
            'key' => 'district_id',
            'value' => $district_id
        )
    )
));

// // Prepare the list of wards with their post counts
// $wards_with_counts = array();

// foreach ($wards as $ward) {
//     $ward_id = $ward->ID;

//     // Count posts for each post type
//     $office_rent_count = new WP_Query(array(
//         'post_type' => 'office-rent',
//         'posts_per_page' => -1,
//         'fields' => 'ids', // Only get post IDs for better performance
//         'meta_query' => array(
//             array(
//                 'key' => '_office_ward',
//                 'value' => $ward_id,
//                 'compare' => '='
//             )
//         )
//     ));

//     $office_rent_full_count = new WP_Query(array(
//         'post_type' => 'office-rent-full',
//         'posts_per_page' => -1,
//         'fields' => 'ids',
//         'meta_query' => array(
//             array(
//                 'key' => '_office_ward',
//                 'value' => $ward_id,
//                 'compare' => '='
//             )
//         )
//     ));

//     $business_space_count = new WP_Query(array(
//         'post_type' => 'business-space',
//         'posts_per_page' => -1,
//         'fields' => 'ids',
//         'meta_query' => array(
//             array(
//                 'key' => '_office_ward',
//                 'value' => $ward_id,
//                 'compare' => '='
//             )
//         )
//     ));

//     $real_estate_count = new WP_Query(array(
//         'post_type' => 'real-estate',
//         'posts_per_page' => -1,
//         'fields' => 'ids',
//         'meta_query' => array(
//             array(
//                 'key' => '_office_ward',
//                 'value' => $ward_id,
//                 'compare' => '='
//             )
//         )
//     ));

//     // Calculate total count across all post types
//     $total_count = $office_rent_count->found_posts +
//         $office_rent_full_count->found_posts +
//         $business_space_count->found_posts +
//         $real_estate_count->found_posts;

//     // Only add wards that have at least one post
//     if ($total_count > 0) {
//         $wards_with_counts[] = array(
//             'id' => $ward_id,
//             'title' => $ward->post_title,
//             'count' => $total_count,
//             'permalink' => get_permalink($ward_id)
//         );
//     }

//     // Clean up queries to free memory
//     wp_reset_postdata();
// }

// // Sort wards by count (highest first)
// usort($wards_with_counts, function ($a, $b) {
//     return $b['count'] - $a['count'];
// });



// Get blocks with associated properties
$blocks = get_posts(array(
    'post_type' => 'block',
    'posts_per_page' => -1,
    'orderby' => 'title',
    'order' => 'ASC',
    'suppress_filters' => false
));

$blocks_with_counts = array();

foreach ($blocks as $block) {
    $block_id = $block->ID;

    // Count posts for each post type
    $office_rent_count = new WP_Query(array(
        'post_type' => 'office-rent',
        'posts_per_page' => -1,
        'fields' => 'ids', // Only get post IDs for better performance
        'meta_query' => array(
            array(
                'key' => '_office_block',
                'value' => $block_id,
                'compare' => '='
            )
        )
    ));

    $office_rent_full_count = new WP_Query(array(
        'post_type' => 'office-rent-full',
        'posts_per_page' => -1,
        'fields' => 'ids',
        'meta_query' => array(
            array(
                'key' => '_office_block',
                'value' => $block_id,
                'compare' => '='
            )
        )
    ));

    $business_space_count = new WP_Query(array(
        'post_type' => 'business-space',
        'posts_per_page' => -1,
        'fields' => 'ids',
        'meta_query' => array(
            array(
                'key' => '_office_block',
                'value' => $block_id,
                'compare' => '='
            )
        )
    ));

    $real_estate_count = new WP_Query(array(
        'post_type' => 'real-estate',
        'posts_per_page' => -1,
        'fields' => 'ids',
        'meta_query' => array(
            array(
                'key' => '_office_block',
                'value' => $block_id,
                'compare' => '='
            )
        )
    ));

    // Calculate total count across all post types
    $total_count = $office_rent_count->found_posts +
        $office_rent_full_count->found_posts +
        $business_space_count->found_posts +
        $real_estate_count->found_posts;

    // Only add blocks that have at least one post
    if ($total_count > 0) {
        $blocks_with_counts[] = array(
            'id' => $block_id,
            'title' => $block->post_title,
            'count' => $total_count,
            'permalink' => get_permalink($block_id)
        );
    }

    // Clean up queries to free memory
    wp_reset_postdata();
}

// Sort blocks by count (highest first)
usort($blocks_with_counts, function ($a, $b) {
    return $b['count'] - $a['count'];
});


?>

<div class="scroll-section filter-nav" data-title="Tìm kiếm">
    <?php echo display_rating_html(get_the_ID(), true); ?>


    <section class="section">
        <div class="container">
            <h1 class="block-title">Sản phẩm cho thuê tại <?php echo get_the_title($post_id); ?></h1>
        </div>
    </section>

    <?php if ($office_rent_query->have_posts()) : ?>
        <section class="scroll-section district-1 odd:bg-primary-1/5 section pt-10 xl:pb-20">
            <div class="container">
                <h2 class="block-title"><?php echo __('Văn phòng cho thuê', 'canhcamtheme'); ?></h2>
                <div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 base-gap mt-10">
                    <?php
                    while ($office_rent_query->have_posts()) : $office_rent_query->the_post();
                        $post_id = get_the_ID();
                        get_template_part('components/boxProduct/box-rent-1', '', array('post_id' => $post_id));
                    endwhile;
                    ?>
                </div>
                <div class="wp-pagination">
                    <a class="btn btn-pageination mx-auto mt-4 btn-primary "><span>Xem thêm</span><i class="fa-solid fa-right-long"></i></a>
                </div>
            </div>
        </section>
    <?php endif; ?>

    <?php if ($office_rent_full_query->have_posts()) : ?>
        <section class="scroll-section district-1 odd:bg-primary-1/5 section pt-10 xl:pb-20">
            <div class="container">
                <h2 class="block-title"><?php echo __('Văn phòng cho thuê trọn gói', 'canhcamtheme'); ?></h2>
                <div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 base-gap mt-10">
                    <?php
                    while ($office_rent_full_query->have_posts()) : $office_rent_full_query->the_post();
                        $post_id = get_the_ID();
                        get_template_part('components/boxProduct/box-rent-2', '', array('post_id' => $post_id));
                    endwhile;
                    ?>
                </div>
                <div class="wp-pagination">
                    <a class="btn btn-pageination mx-auto mt-4 btn-primary "><span>Xem thêm</span><i class="fa-solid fa-right-long"></i></a>
                </div>
            </div>
        </section>
    <?php endif; ?>

    <?php if ($business_space_query->have_posts()) : ?>
        <section class="scroll-section district-1 odd:bg-primary-1/5 section pt-10 xl:pb-20">
            <div class="container">
                <h2 class="block-title"><?php echo __('Mặt bằng kinh doanh', 'canhcamtheme'); ?></h2>
                <div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 base-gap mt-10">
                    <?php
                    while ($business_space_query->have_posts()) : $business_space_query->the_post();
                        $post_id = get_the_ID();
                        get_template_part('components/boxProduct/box-rent-1', '', array('post_id' => $post_id));
                    endwhile;
                    ?>
                </div>
                <div class="wp-pagination">
                    <a class="btn btn-pageination mx-auto mt-4 btn-primary "><span>Xem thêm</span><i class="fa-solid fa-right-long"></i></a>
                </div>
            </div>
        </section>
    <?php endif; ?>

    <?php if ($real_estate_query->have_posts()) : ?>
        <section class="scroll-section district-1 odd:bg-primary-1/5 section pt-10 xl:pb-20">
            <div class="container">
                <h2 class="block-title"><?php echo __('Bất động sản', 'canhcamtheme'); ?></h2>
                <div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 base-gap mt-10">
                    <?php
                    while ($real_estate_query->have_posts()) : $real_estate_query->the_post();
                        $post_id = get_the_ID();
                        get_template_part('components/boxProduct/box-rent-1', '', array('post_id' => $post_id));
                    endwhile;
                    ?>
                </div>
                <div class="wp-pagination">
                    <a class="btn btn-pageination mx-auto mt-4 btn-primary "><span>Xem thêm</span><i class="fa-solid fa-right-long"></i></a>
                </div>
            </div>
        </section>
    <?php endif; ?>


    <?php if ($content_office): ?>
        <section class="cites-5 section">
            <div class="container">
                <div class="section bg-primary-1/5 rounded-4 px-6 md:rem:px-[60px]">
                    <div class="wrap-show-content">
                        <div class="show-content expand-content rem:max-h-[240px] overflow-hidden">
                            <article class="prose font-medium">
                                <?php echo $content_office; ?>
                            </article>
                        </div>
                        <a class="btn btn-expand mx-auto mt-4 btn-primary "><span><?php echo __('Xem thêm', 'canhcamtheme'); ?></span><span><?php echo __('Thu gọn', 'canhcamtheme'); ?></span><i class="fa-light fa-plus"></i></a>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>



    <section class="distric-2 section">
        <!-- <div class="scroll-section distric-item odd:bg-primary-1/5 section xl:py-20" data-title="Tìm văn phòng theo đường liên quan">
            <div class="container">
                <h2 class="block-title"><?php echo __('Tìm văn phòng theo đường liên quan', 'canhcamtheme'); ?></h2>
                <div class="list keep-ul-disc">
                    <ul class="grid grid-cols-2 lg:grid-cols-4 base-gap gap-y-4 mt-10">
                        <?php foreach ($streets_with_counts as $street) : ?>
                            <li>
                                <a href="<?php echo $street['permalink']; ?>">
                                    <?php echo $street['title']; ?> <span>(<?php echo $street['count']; ?>)</span>
                                </a>
                            </li>
                        <?php endforeach; ?>

                        <?php if (empty($streets_with_counts)) : ?>
                            <li><?php _e('Không tìm thấy đường phố nào', 'canhcamtheme'); ?></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>

        <div class="scroll-section distric-item odd:bg-primary-1/5 section xl:py-20" data-title="Tìm văn phòng theo phường liên quan">
            <div class="container">
                <h2 class="block-title"><?php echo __('Tìm văn phòng theo phường liên quan', 'canhcamtheme'); ?></h2>
                <div class="list keep-ul-disc">
                    <ul class="grid grid-cols-2 lg:grid-cols-4 base-gap gap-y-4 mt-10">
                        <?php foreach ($wards_with_counts as $ward) : ?>
                            <li>
                                <a href="<?php echo $ward['permalink']; ?>">
                                    <?php echo $ward['title']; ?> <span>(<?php echo $ward['count']; ?>)</span>
                                </a>
                            </li>
                        <?php endforeach; ?>

                        <?php if (empty($wards_with_counts)) : ?>
                            <li><?php _e('Không tìm thấy phường nào', 'canhcamtheme'); ?></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div> -->

        <div class="scroll-section distric-item odd:bg-primary-1/5 section xl:py-20" data-title="Tìm văn phòng theo khu vực">
            <div class="container">
                <h2 class="block-title"><?php echo __('Tìm văn phòng theo khu vực', 'canhcamtheme'); ?></h2>
                <div class="list keep-ul-disc">
                    <ul class="grid grid-cols-2 lg:grid-cols-4 base-gap gap-y-4 mt-10">
                        <?php

                        foreach ($blocks_with_counts as $block) : ?>
                            <li>
                                <a href="<?php echo $block['permalink']; ?>">
                                    <?php echo $block['title']; ?> <span>(<?php echo $block['count']; ?>)</span>
                                </a>
                            </li>
                        <?php endforeach; ?>

                        <?php if (empty($blocks_with_counts)) : ?>
                            <li><?php _e('Không tìm thấy khu vực nào', 'canhcamtheme'); ?></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </section>




    <?php get_template_part('components/product/box-footer-product'); ?>

</div>

<?php

wp_reset_postdata();
get_footer(); ?>