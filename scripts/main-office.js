// favorite.js
jQuery(document).ready(function ($) {
  // Initialize favorites
  // updateFavoriteUI();

  // Initialize compares
  if (typeof compareData !== "undefined") {
    updateCompareUI();
  }

  // Add to cart functionality
  $(document).on("click", ".office-add-to-cart", function (e) {
    e.preventDefault();

    var $this = $(this);
    var postId = $this.data("id");

    if (!postId) {
      return;
    }

    // Check if user is logged in
    if (!favoriteData.is_logged_in) {
      FE.toast.warning("Vui lòng đăng nhập để thêm vào giỏ hàng", {
        duration: 3000,
      });
      return;
    }

    $.ajax({
      url: favoriteData.ajax_url,
      type: "POST",
      data: {
        action: "add_to_cart",
        post_id: postId,
      },
      beforeSend: function () {
        $this.addClass("btn-loading");
      },
      success: function (response) {
        if (response.success) {
          // Show success notification
          FE.toast.success("<PERSON><PERSON> thêm vào giỏ hàng", {
            duration: 3000,
          });

          // Update cart counter if it exists
          if (response.data && response.data.cart_count) {
            updateCartCounter(response.data.cart_count);
          }
        } else if (response.data && response.data.message) {
          // Handle error messages
          FE.toast.error(response.data.message, {
            duration: 3000,
          });
        } else {
          // Generic error message
          FE.toast.error("Đã xảy ra lỗi khi thêm vào giỏ hàng", {
            duration: 3000,
          });
        }
      },
      error: function (xhr) {
        try {
          var response = JSON.parse(xhr.responseText);
          if (response.data && response.data.message) {
            FE.toast.error(response.data.message, {
              duration: 3000,
            });
          }
        } catch (e) {
          FE.toast.error("Đã xảy ra lỗi khi thêm vào giỏ hàng", {
            duration: 3000,
          });
        }
      },
      complete: function () {
        $this.removeClass("btn-loading");
      },
    });
  });

  // Function to update cart counter
  function updateCartCounter(count) {
    $(".cart-counter").text(count);

    // Show/hide cart button based on whether there are items in cart
    if (count > 0) {
      $(".cart-button").removeClass("hidden");
    } else {
      $(".cart-button").addClass("hidden");
    }
  }

  // Xóa sản phẩm khỏi giỏ hàng
  $(document).on("click", ".office-remove-cart", function (e) {
    e.preventDefault();

    var $this = $(this);
    var postId = $this.data("id");

    if (!postId) {
      return;
    }

    $.ajax({
      url: favoriteData.ajax_url,
      type: "POST",
      data: {
        action: "remove_from_cart",
        post_id: postId,
      },
      beforeSend: function () {
        $this.addClass("btn-loading");
      },
      success: function (response) {
        if (response.success) {
          // Hiển thị thông báo
          FE.toast.info(
            response.data.message || "Đã xóa sản phẩm khỏi giỏ hàng",
            {
              duration: 3000,
            }
          );

          // Cập nhật số lượng giỏ hàng
          if (response.data.cart_count !== undefined) {
            updateCartCounter(response.data.cart_count);
          }

          // Xóa phần tử sản phẩm khỏi trang (nếu đang ở trang giỏ hàng)
          var $productItem = $this.closest(".product-item");
          if ($productItem.length) {
            $productItem.fadeOut(300, function () {
              $(this).remove();

              // Kiểm tra nếu không còn sản phẩm nào
              if ($(".product-item").length === 0) {
                $(".cart-container").replaceWith(
                  '<div class="empty-cart py-10">' +
                    favoriteData.cart_empty +
                    "</div>"
                );
              }
            });
          }
        } else {
          // Hiển thị lỗi
          FE.toast.error(
            response.data.message || "Không thể xóa sản phẩm khỏi giỏ hàng",
            {
              duration: 3000,
            }
          );
        }
      },
      error: function () {
        FE.toast.error("Đã xảy ra lỗi khi xóa sản phẩm khỏi giỏ hàng", {
          duration: 3000,
        });
      },
      complete: function () {
        $this.removeClass("btn-loading");
      },
    });
  });

  // Xóa tất cả sản phẩm khỏi giỏ hàng
  $(document).on("click", ".clear-cart", function (e) {
    e.preventDefault();

    if (!confirm("Bạn có chắc chắn muốn xóa tất cả sản phẩm khỏi giỏ hàng?")) {
      return;
    }

    var $this = $(this);

    $.ajax({
      url: favoriteData.ajax_url,
      type: "POST",
      data: {
        action: "clear_cart",
      },
      beforeSend: function () {
        $this.addClass("btn-loading");
      },
      success: function (response) {
        if (response.success) {
          // Hiển thị thông báo
          FE.toast.info(
            response.data.message || "Đã xóa tất cả sản phẩm khỏi giỏ hàng",
            {
              duration: 3000,
            }
          );

          // Cập nhật số lượng giỏ hàng
          updateCartCounter(0);

          // Cập nhật giao diện giỏ hàng
          $(".cart-container")
            .parent()
            .html(
              '<div class="empty-cart text-center py-10">' +
                '<div class="empty-cart-icon mb-4"><i class="fa-solid fa-shopping-cart fa-4x text-neutral-300"></i></div>' +
                '<p class="text-lg mb-6">Giỏ hàng của bạn đang trống.</p>' +
                '<a href="/san-pham" class="btn btn-primary">Tiếp tục xem sản phẩm</a>' +
                "</div>"
            );
        } else {
          // Hiển thị lỗi
          FE.toast.error(response.data.message || "Không thể xóa giỏ hàng", {
            duration: 3000,
          });
        }
      },
      error: function () {
        FE.toast.error("Đã xảy ra lỗi khi xóa giỏ hàng", {
          duration: 3000,
        });
      },
      complete: function () {
        $this.removeClass("btn-loading");
      },
    });
  });

  // Toggle favorite
  $(document).on("click", ".favorite", function (e) {
    e.preventDefault();

    var $this = $(this);
    var postId = $this.data("id");

    if (!postId) {
      return;
    }

    // Check if user is logged in
    if (!favoriteData.is_logged_in) {
      FE.toast.warning("Vui lòng đăng nhập để thêm sản phẩm yêu thích", {
        duration: 3000,
      });
      return;
    }

    $.ajax({
      url: favoriteData.ajax_url,
      type: "POST",
      data: {
        action: "toggle_favorite",
        post_id: postId,
      },
      beforeSend: function () {
        $this.addClass("loading");
      },
      success: function (response) {
        if (response.success) {
          // Update UI
          $this.toggleClass("is-favorite", response.data.is_favorite);

          // Update global favorites data
          favoriteData.favorites = response.data.favorites;

          // Show notification using FE.toast
          showFavoriteNotification(response.data.is_favorite);
        }
      },
      complete: function () {
        $this.removeClass("loading");
      },
    });
  });

  // Toggle compare
  $(document).on("click", ".office-compare", function (e) {
    e.preventDefault();

    var $this = $(this);
    var postId = $this.data("id");

    if (!postId) {
      return;
    }

    $.ajax({
      url: compareData.ajax_url,
      type: "POST",
      data: {
        action: "toggle_compare",
        post_id: postId,
      },
      beforeSend: function () {
        $this.addClass("btn-loading");
      },
      success: function (response) {
        if (response.success) {
          // Update UI
          $this.toggleClass("is-compare", response.data.is_compare);

          // Update global compare data
          compareData.compares = response.data.compares;
          compareData.post_type = response.data.post_type;

          // Show notification
          showCompareNotification(response.data.is_compare);
          if ($("body").hasClass("page-template-PageCompare")) {
            window.location.reload();
          }
          // Update UI
          updateCompareUI();

          // Update the compare counter if it exists
          updateCompareCounter();
        } else if (response.data && response.data.message) {
          // Handle error messages when success is false
          FE.toast.error(response.data.message, {
            duration: 3000,
          });
        } else {
          // Generic error message if no specific message is provided
          FE.toast.error("Đã xảy ra lỗi khi thêm vào danh sách so sánh", {
            duration: 3000,
          });
        }
      },
      error: function (xhr) {
        try {
          var response = JSON.parse(xhr.responseText);
          if (response.data && response.data.message) {
            FE.toast.error(response.data.message, {
              duration: 3000,
            });
          }
        } catch (e) {
          FE.toast.error("Đã xảy ra lỗi khi thêm vào danh sách so sánh", {
            duration: 3000,
          });
        }
      },
      complete: function () {
        $this.removeClass("btn-loading");
      },
    });
  });

  // Clear all compares
  $(document).on("click", ".clear-compares", function (e) {
    e.preventDefault();

    $.ajax({
      url: compareData.ajax_url,
      type: "POST",
      data: {
        action: "clear_compares",
      },
      success: function (response) {
        if (response.success) {
          // Update global compare data
          compareData.compares = [];
          compareData.post_type = false;

          // Update UI
          updateCompareUI();

          // Update the compare counter if it exists
          updateCompareCounter();

          // Show notification
          FE.toast.info("Đã xóa tất cả mục khỏi danh sách so sánh", {
            duration: 3000,
          });

          // If on compare page, redirect to home
          if (window.location.href.indexOf("/so-sanh") > -1) {
            window.location.href = "/";
          }
        }
      },
    });
  });

  // Function to update UI for all favorite buttons
  function updateFavoriteUI() {
    $(".favorite").each(function () {
      var postId = parseInt($(this).data("id"));

      if (
        postId &&
        Array.isArray(favoriteData.favorites) &&
        favoriteData.favorites.includes(postId)
      ) {
        $(this).addClass("is-favorite");
      } else {
        $(this).removeClass("is-favorite");
      }
    });
  }

  // Function to update UI for all compare buttons
  function updateCompareUI() {
    $(".office-compare").each(function () {
      var postId = parseInt($(this).data("id"));
      var $this = $(this);

      if (!$("body").hasClass("page-template-PageCompare")) {
        if (postId && compareData.compares.includes(postId)) {
          $this.addClass("is-compare");
          $this.html('<i class="fa-solid fa-trash-can"></i>Xóa');
        } else {
          $this.removeClass("is-compare");
          $this.html('<i class="fa-solid fa-circle-plus"></i>So sánh');
        }
      }

      // If button post type is not in the list or different from current compare type, disable it
      var buttonPostType = $(this).data("type");
      if (
        compareData.compares.length > 0 &&
        compareData.post_type &&
        buttonPostType !== compareData.post_type
      ) {
        $(this).attr("title", "Chỉ có thể so sánh các sản phẩm cùng loại");
      } else {
        $(this).attr("title", "");
      }
    });

    // Update compare counter
    updateCompareCounter();
  }

  // Function to update the compare counter
  function updateCompareCounter() {
    var counter = compareData.compares.length;
    $(".compare-counter").text(counter);

    // Show/hide compare button based on whether there are items to compare
    if (counter > 0) {
      $(".compare-button").removeClass("hidden");
    } else {
      $(".compare-button").addClass("hidden");
    }
  }

  // Function to show notification for favorites
  function showFavoriteNotification(isFavorite) {
    if (isFavorite) {
      FE.toast.success("Đã thêm vào danh sách yêu thích", {
        duration: 3000,
      });
    } else {
      FE.toast.info("Đã xóa khỏi danh sách yêu thích", {
        duration: 3000,
      });
    }
  }

  // Function to show notification for compare
  function showCompareNotification(isCompare) {
    if (isCompare) {
      FE.toast.success("<a href='/so-sanh' >Xem danh sách so sánh</a>", {
        duration: 3000,
      });
    } else {
      FE.toast.info("Đã xóa khỏi danh sách so sánh", {
        duration: 3000,
      });
    }
  }

  $(document).on("click", ".btn-search", function (e) {
    e.preventDefault();
    e.stopPropagation();

    handleSubmitForm();
  });

  function handleSubmitForm() {
    var url = decodeURIComponent(FE.filters.getSearchUrl());
    // console.log('🟩 ~ handleSubmitForm ~ url:', url);

    // Redirect to the search URL only if URL is defined
    if (url && url !== "undefined") {
      window.location.href = url;
    }
  }

  $("#filter-form").on("submit", function (e) {
    e.preventDefault();
  });
  $("#filter-form").on("keydown", function (e) {
    const isEnter = e.key === "Enter";
    if (isEnter) {
      handleSubmitForm();
    }
  });

  $(document).on("click", ".btn-contact-compare", function (e) {
    e.preventDefault();
    var $this = $(this);
    var postTitle = $this.data("title");
    var postImage = $this.data("img");

    $("#popup-contact").find('input[name="post_title"]').attr("value", "");

    $("#popup-contact").find(".img img").attr("src", postImage);
    // $("#popup-contact").find(".content .block-title").text(postTitle);
    $("#popup-contact").find(".content .sub-title").text(postTitle);

    $("#popup-contact")
      .find('input[name="post_title"]')
      .attr("value", postTitle);
  });

  $(document).on("click", ".btn-contact-cart", function (e) {
    e.preventDefault();

    // Get all product titles from cart items
    var titles = [];
    $(".product-item").each(function () {
      titles.push($(this).data("name"));
    });

    // Join titles with comma
    var allTitles = titles.join(", ");

    $("#popup-contact")
      .find('input[name="post_title"]')
      .attr("value", allTitles);
    $("#popup-contact").find(".content .block-title").text("Bạn quan tâm đến");
    $("#popup-contact").find(".content .sub-title").text(allTitles);
  });

  const mutation = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.target.classList.contains("checkbox-item")) {
        const hasAtleastOneChecked =
          $(".checkbox-item:not(.select-category).active").length > 0;
        if (hasAtleastOneChecked) {
          $(".btn-search").removeAttr("disabled");
        } else {
          $(".btn-search").attr("disabled", "disabled");
        }
      }
    });
  });
  let keyupTimeout = null;
  $(".search input").on("keyup", function (e) {
    const isHaveValue = $(this).val() !== "";
    if (keyupTimeout) {
      clearTimeout(keyupTimeout);
    }
    keyupTimeout = setTimeout(() => {
      if (isHaveValue) {
        if ($(".btn-search").attr("disabled")) {
          $(".btn-search").removeAttr("disabled");
        }
      } else {
        $(".btn-search").attr("disabled", "disabled");
      }
    }, 250);
  });
  mutation.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
  });

  rangeSliders.forEach((slider) => {
    slider.on("update", function (values) {
      const parent = $(slider.target).closest(".price-range");
      const inputForm = parent.find(".input-from");
      const inputTo = parent.find(".input-to");
      if (inputForm.val() !== "" || inputTo.val() !== "") {
        if ($(".btn-search").attr("disabled")) {
          $(".btn-search").removeAttr("disabled");
        }
      } else {
        $(".btn-search").attr("disabled", "disabled");
      }
    });
  });

  // ... existing code ...

  /**
   * ===================================================================
   * CHỨC NĂNG ĐÁNH GIÁ (RATING) ĐỚN GIẢN
   * ===================================================================
   */
  jQuery(document).ready(function ($) {
    // Xử lý hover effect cho rating stars
    $('.rate-page[data-interactive="true"] .stars .fa-star').on(
      "mouseenter",
      function () {
        const rating = $(this).data("rating");
        const $stars = $(this).closest(".stars").find(".fa-star");

        // Reset tất cả stars
        $stars.removeClass("hover");

        // Highlight stars từ 1 đến rating hiện tại
        for (let i = 1; i <= rating; i++) {
          $stars.filter(".star-" + i).addClass("hover");
        }
      }
    );

    // Xóa hover effect khi rời chuột
    $('.rate-page[data-interactive="true"] .stars').on(
      "mouseleave",
      function () {
        $(this).find(".fa-star").removeClass("hover");
      }
    );

    // Xử lý click vào rating stars
    $('.rate-page[data-interactive="true"] .stars .fa-star').on(
      "click",
      function () {
        const rating = $(this).data("rating");
        const $rateContainer = $(this).closest(".rate-page");
        const postId = $rateContainer.data("post-id");

        // Kiểm tra rating < 4 thì alert và không cho rate
        if (rating < 4) {
          alert(
            "Chúng tôi chỉ chấp nhận đánh giá từ 4 sao trở lên. Cảm ơn bạn đã quan tâm!"
          );
          return;
        }

        // Gửi AJAX request
        submitRating(postId, rating, $rateContainer);
      }
    );

    // Function gửi rating
    function submitRating(postId, rating, $container) {
      // Disable tương tác tạm thời
      $container.find(".fa-star").css("pointer-events", "none");

      $.ajax({
        url: ratingData.ajax_url,
        type: "POST",
        data: {
          action: "submit_rating",
          post_id: postId,
          rating: rating,
        },
        success: function (response) {
          if (response.success) {
            // Hiển thị thông báo thành công
            alert(response.data.message);

            // Cập nhật hiển thị rating
            updateRatingDisplay(response.data.stats, $container);

            // Disable rating cho post này
            $container.removeAttr("data-interactive");
            $container.find(".fa-star").css("cursor", "default");

            // Thêm thông báo đã đánh giá
            // $container
            //   .find(".wrapper")
            //   .append(
            //     '<div class="rated-message text-green-600 text-sm ml-3"><i class="fa-solid fa-check"></i> Đã đánh giá</div>'
            //   );
          } else {
            alert(response.data.message);
            // Enable lại tương tác nếu có lỗi
            $container.find(".fa-star").css("pointer-events", "auto");
          }
        },
        error: function () {
          alert("Có lỗi xảy ra. Vui lòng thử lại.");
          // Enable lại tương tác nếu có lỗi
          $container.find(".fa-star").css("pointer-events", "auto");
        },
      });
    }

    // Cập nhật hiển thị rating
    function updateRatingDisplay(stats, $container) {
      // Cập nhật số sao hiển thị
      const $stars = $container.find(".stars .fa-star");
      $stars.removeClass("active");

      for (let i = 1; i <= Math.floor(stats.average); i++) {
        $stars.filter(".star-" + i).addClass("active");
      }

      // Cập nhật text
      $container.find(".current").text(stats.average.toFixed(1));
      $container.find(".rating-count").text(stats.total);
    }

    // CSS cho hover effect
    $("<style>")
      .prop("type", "text/css")
      .html(
        `
          .rate-page .stars .fa-star {
              transition: color 0.3s ease;
          }
          .rate-page .stars .fa-star.hover {
              color: #FFBF00 !important;
          }
          .rate-page .stars .fa-star.active {
              color: #FFBF00;
          }
          .rate-page[data-interactive="true"] .stars .fa-star:hover {
              color: #FFBF00 !important;
          }
      `
      )
      .appendTo("head");
  });
});
