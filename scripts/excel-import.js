jQuery(document).ready(function($) {
    // Handle the import button click
    $('#import-button').on('click', function() {
        var filePath = $('#file_path').val();
        var importType = $('#import_type').val();
        
        // Show progress bar
        $('#import-progress').removeClass('hidden');
        $('.progress-status').text('Đang xử lý dữ liệu...');
        $('.progress-bar-inner').css('width', '30%');
        
        // Disable the button during import
        $(this).prop('disabled', true);
        
        // Clear previous results
        $('#import-result').empty();
        
        // Make the AJAX request
        $.ajax({
            url: excel_import_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'import_excel_first_row',
                nonce: excel_import_ajax.nonce,
                file_path: filePath,
                import_type: importType
            },
            dataType: 'json',
            success: function(response) {
                // Update progress
                $('.progress-bar-inner').css('width', '100%');
                
                if (response.success) {
                    // Show success message and data
                    $('.progress-status').text('Import hoàn tất!');
                    $('#import-result').html(response.data.html);
                } else {
                    // Show error message
                    $('.progress-status').text('Import thất bại!');
                    $('#import-result').html('<div class="notice notice-error inline"><p>' + response.data.message + '</p></div>');
                }
                
                // Re-enable the button
                $('#import-button').prop('disabled', false);
            },
            error: function(xhr, status, error) {
                // Handle AJAX errors
                $('.progress-bar-inner').css('width', '100%');
                $('.progress-status').text('Có lỗi xảy ra!');
                $('#import-result').html('<div class="notice notice-error inline"><p>Lỗi AJAX: ' + error + '</p></div>');
                $('#import-button').prop('disabled', false);
            }
        });
    });

    // Xử lý nút đóng kết quả
    $(document).on('click', '.close-import-result', function() {
        $(this).closest('.import-ajax-result').slideUp(function() {
            $(this).html('');
        });
    });
});