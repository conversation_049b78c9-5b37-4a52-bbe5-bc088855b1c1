jQuery(function ($) {
	$('.project-detail-4').each(function () {
		const $this = $(this);
		const formTitle = $this.data('title');
		const form = $this.find('.form-wrap');
		form.find('input[name="projectName"]').val(formTitle);
	});
	$('.format-price input').each(function () {
		const $this = $(this);
		if (typeof Cleave === 'function') {
			console.log($this);
			const cleave = new Cleave($this.get(0), {
				numeral: true,
				// delimiter: ".",
				numeralDecimalMark: ',',
				delimiter: '.',
				numeralThousandsGroupStyle: 'thousand',
				onValueChanged: function (value) {
					console.log(value);
				},
			});
		}
	});
});
