$(document).ready(function () {
	$('.city-item').click(function () {
		$('.select-district').addClass('loading');
		if (!$(this).hasClass('active')) {
			// console.log($(this).data('value'));
			$.ajax({
				url: '/wp-admin/admin-ajax.php',
				type: 'POST',
				data: {
					action: 'get_district_by_city_id',
					city_id: $(this).data('value'),
				},
				success: function (response) {
					$('.select-district').removeClass('loading');
					$('.select-district .inner-checkbox-wrapper').html(response);
				},
			});
		} else {
			$('.select-district').removeClass('loading');
			$('.select-district .inner-checkbox-wrapper').html('');
		}
	});
	// Lấy phường/ xã theo quận/ huyện
	$(document).on('click', '.district-item', function () {
		const $this = $(this);
		const districtId = $this.data('value');
		$('.select-ward').addClass('loading');
		$('.select-street').addClass('loading');
		$('.select-block').addClass('loading');

		if ($this.hasClass('active')) {
			$.ajax({
				url: '/wp-admin/admin-ajax.php',
				type: 'POST',
				data: {
					action: 'get_address_by_district_id',
					district_id: districtId,
				},
				success: function (response) {
					const data = JSON.parse(response);
					$('.select-ward').removeClass('loading');
					$('.select-ward .inner-checkbox-wrapper').html(data.ward);
					$('.select-street').removeClass('loading');
					$('.select-street .inner-checkbox-wrapper').html(data.street);
					$('.select-block').removeClass('loading');
					$('.select-block .inner-checkbox-wrapper').html(data.block);
				},
			});
		} else {
			$('.select-ward').removeClass('loading');
			$('.select-street').removeClass('loading');
			$('.select-block').removeClass('loading');

			$('.select-ward .inner-checkbox-wrapper').html('');

			$('.select-street .inner-checkbox-wrapper').html('');

			$('.select-block .inner-checkbox-wrapper').html('');
		}
	});
});
