<?php get_header(); ?>

<?php
global $post;
$taxonomy = get_post_taxonomies($post);
$term = wp_get_post_terms($post->ID, $taxonomy);
?>

<section class="news-detail section xl:py-20">
    <div class="container">
        <div class="article-header">
            <h1 class="title small-block-title">
                <?= get_the_title($id) ?>
            </h1>
            <div class="article-info flex items-center gap-4 mt-6">
                <?php if ($primary_category) : ?>
                    <div class="category text-primary-2">
                        <?= $primary_category->name ?>
                    </div>
                <?php endif; ?>
                <time class="body-14 text-neutral-500 block md:mx-10">
                    <?= dateFormatOnLayout($id) ?>
                </time>
                <div class="line flex-1 bg-neutral-200 w-full h-0.5"></div>
            </div>
            <div class="share-wrapper absolute top-0 -left-18 h-full">
                <div class="share sticky top-28"><a id="facebook-share" class="size-12 rounded-full border border-neutral-200 flex items-center justify-center hover:scale-105 hover:shadow-md hover:bg-primary-2 hover:text-white"><i class="fa-brands fa-facebook-f"></i></a></div>
            </div>
            <div class="article-content mt-8">
                <div class="prose body-16"><?= the_content() ?></div>
            </div>
        </div>
    </div>
</section>
<?php get_footer(); ?>