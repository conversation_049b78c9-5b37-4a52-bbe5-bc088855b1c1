<?php
global $post, $wpdb;
$post_id = $post->ID;
$district_id = get_field('district_id', $post_id);
$content_office = get_field('content_office', 'option');
get_header();

// Define post types to be used throughout the page
$post_types = ['office-rent', 'office-rent-full', 'business-space', 'real-estate'];

// OPTIMIZED: Run one query per post type with pagination instead of querying all posts
function get_ward_posts($post_type, $ward_id, $posts_per_page = 12)
{
    return new WP_Query([
        'post_type' => $post_type,
        'posts_per_page' => $posts_per_page,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC',
        'meta_query' => [
            [
                'key' => '_office_ward',
                'value' => $ward_id,
                'compare' => '='
            ]
        ]
    ]);
}

// Get post queries for each post type
$office_rent_query = get_ward_posts('office-rent', $post_id);
$office_rent_full_query = get_ward_posts('office-rent-full', $post_id);
$business_space_query = get_ward_posts('business-space', $post_id);
$real_estate_query = get_ward_posts('real-estate', $post_id);

// Get all location data with optimized queries using the function from function-office.php
$wards_with_counts = get_locations_with_counts('ward', 'district_id', $district_id, $post_types);
$blocks_with_counts = get_locations_with_counts('block', '', '', $post_types);
?>

<div class="scroll-section filter-nav" data-title="Tìm kiếm">
    <?php echo display_rating_html(get_the_ID(), true); ?>


    <section class="section">
        <div class="container">
            <h1 class="block-title">Sản phẩm cho thuê tại <?php echo get_the_title($post_id); ?></h1>
        </div>
    </section>

    <?php if ($office_rent_query->have_posts()) : ?>
        <section class="scroll-section district-1 odd:bg-primary-1/5 section pt-10 xl:pb-20">
            <div class="container">
                <h2 class="block-title"><?php echo __('Văn phòng cho thuê', 'canhcamtheme'); ?></h2>
                <div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 base-gap mt-10">
                    <?php
                    while ($office_rent_query->have_posts()) : $office_rent_query->the_post();
                        $post_id = get_the_ID();
                        get_template_part('components/boxProduct/box-rent-1', '', array('post_id' => $post_id));
                    endwhile;
                    ?>
                </div>
                <div class="wp-pagination">
                    <a class="btn btn-pageination mx-auto mt-4 btn-primary "><span>Xem thêm</span><i class="fa-solid fa-right-long"></i></a>
                </div>
            </div>
        </section>
    <?php endif; ?>

    <?php if ($office_rent_full_query->have_posts()) : ?>
        <section class="scroll-section district-1 odd:bg-primary-1/5 section pt-10 xl:pb-20">
            <div class="container">
                <h2 class="block-title"><?php echo __('Văn phòng cho thuê trọn gói', 'canhcamtheme'); ?></h2>
                <div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 base-gap mt-10">
                    <?php
                    while ($office_rent_full_query->have_posts()) : $office_rent_full_query->the_post();
                        $post_id = get_the_ID();
                        get_template_part('components/boxProduct/box-rent-2', '', array('post_id' => $post_id));
                    endwhile;
                    ?>
                </div>
                <div class="wp-pagination">
                    <a class="btn btn-pageination mx-auto mt-4 btn-primary "><span>Xem thêm</span><i class="fa-solid fa-right-long"></i></a>
                </div>
            </div>
        </section>
    <?php endif; ?>

    <?php if ($business_space_query->have_posts()) : ?>
        <section class="scroll-section district-1 odd:bg-primary-1/5 section pt-10 xl:pb-20">
            <div class="container">
                <h2 class="block-title"><?php echo __('Mặt bằng kinh doanh', 'canhcamtheme'); ?></h2>
                <div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 base-gap mt-10">
                    <?php
                    while ($business_space_query->have_posts()) : $business_space_query->the_post();
                        $post_id = get_the_ID();
                        get_template_part('components/boxProduct/box-rent-1', '', array('post_id' => $post_id));
                    endwhile;
                    ?>
                </div>
                <div class="wp-pagination">
                    <a class="btn btn-pageination mx-auto mt-4 btn-primary "><span>Xem thêm</span><i class="fa-solid fa-right-long"></i></a>
                </div>
            </div>
        </section>
    <?php endif; ?>

    <?php if ($real_estate_query->have_posts()) : ?>
        <section class="scroll-section district-1 odd:bg-primary-1/5 section pt-10 xl:pb-20">
            <div class="container">
                <h2 class="block-title"><?php echo __('Bất động sản', 'canhcamtheme'); ?></h2>
                <div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 base-gap mt-10">
                    <?php
                    while ($real_estate_query->have_posts()) : $real_estate_query->the_post();
                        $post_id = get_the_ID();
                        get_template_part('components/boxProduct/box-rent-1', '', array('post_id' => $post_id));
                    endwhile;
                    ?>
                </div>
                <div class="wp-pagination">
                    <a class="btn btn-pageination mx-auto mt-4 btn-primary "><span>Xem thêm</span><i class="fa-solid fa-right-long"></i></a>
                </div>
            </div>
        </section>
    <?php endif; ?>


    <?php if ($content_office): ?>
        <section class="cites-5 section">
            <div class="container">
                <div class="section bg-primary-1/5 rounded-4 px-6 md:rem:px-[60px]">
                    <div class="wrap-show-content">
                        <div class="show-content expand-content rem:max-h-[240px] overflow-hidden">
                            <article class="prose font-medium">
                                <?php echo $content_office; ?>
                            </article>
                        </div>
                        <a class="btn btn-expand mx-auto mt-4 btn-primary "><span><?php echo __('Xem thêm', 'canhcamtheme'); ?></span><span><?php echo __('Thu gọn', 'canhcamtheme'); ?></span><i class="fa-light fa-plus"></i></a>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>


    <section class="distric-2 section">
        <!-- <div class="scroll-section distric-item odd:bg-primary-1/5 section xl:py-20" data-title="Tìm văn phòng theo đường liên quan">
            <div class="container">
                <h2 class="block-title"><?php echo __('Tìm văn phòng theo đường liên quan', 'canhcamtheme'); ?></h2>
                <div class="list keep-ul-disc">
                    <ul class="grid grid-cols-2 lg:grid-cols-4 base-gap gap-y-4 mt-10">
                        <?php foreach ($streets_with_counts as $street) : ?>
                            <li>
                                <a href="<?php echo $street['permalink']; ?>">
                                    <?php echo $street['title']; ?> <span>(<?php echo $street['count']; ?>)</span>
                                </a>
                            </li>
                        <?php endforeach; ?>

                        <?php if (empty($streets_with_counts)) : ?>
                            <li><?php _e('Không tìm thấy đường phố nào', 'canhcamtheme'); ?></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div> -->

        <div class="scroll-section distric-item odd:bg-primary-1/5 section xl:py-20" data-title="Tìm văn phòng theo phường liên quan">
            <div class="container">
                <h2 class="block-title"><?php echo __('Tìm văn phòng theo phường liên quan', 'canhcamtheme'); ?></h2>
                <div class="list keep-ul-disc">
                    <ul class="grid grid-cols-2 lg:grid-cols-4 base-gap gap-y-4 mt-10">
                        <?php foreach ($wards_with_counts as $ward) : ?>
                            <li>
                                <a href="<?php echo $ward['permalink']; ?>">
                                    <?php echo $ward['title']; ?> <span>(<?php echo $ward['count']; ?>)</span>
                                </a>
                            </li>
                        <?php endforeach; ?>

                        <?php if (empty($wards_with_counts)) : ?>
                            <li><?php _e('Không tìm thấy phường nào', 'canhcamtheme'); ?></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>

        <div class="scroll-section distric-item odd:bg-primary-1/5 section xl:py-20" data-title="Tìm văn phòng theo khu vực">
            <div class="container">
                <h2 class="block-title"><?php echo __('Tìm văn phòng theo khu vực', 'canhcamtheme'); ?></h2>
                <div class="list keep-ul-disc">
                    <ul class="grid grid-cols-2 lg:grid-cols-4 base-gap gap-y-4 mt-10">
                        <?php foreach ($blocks_with_counts as $block) : ?>
                            <li>
                                <a href="<?php echo $block['permalink']; ?>">
                                    <?php echo $block['title']; ?> <span>(<?php echo $block['count']; ?>)</span>
                                </a>
                            </li>
                        <?php endforeach; ?>

                        <?php if (empty($blocks_with_counts)) : ?>
                            <li><?php _e('Không tìm thấy khu vực nào', 'canhcamtheme'); ?></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <?php get_template_part('components/product/box-footer-product'); ?>
</div>

<?php
wp_reset_postdata();
get_footer();
?>