<?php get_header(); ?>
<?php
get_template_part('./modules/banner/page-banner');
?>

<?php
global $post;
$taxonomy = get_post_taxonomies($post);
$term = wp_get_post_terms($post->ID, $taxonomy);
$cats = array_map(function ($o) {
    return $o->term_id;
}, $term);
$the_query = new WP_query(get_other_post_type($post, $cats, 5));
$post_categories = get_post_primary_category($id, $taxonomy[0]);
$primary_category = $post_categories['primary_category'];
$recruit_options = get_field('recruit_options', 'options');
$form_shortcode = $recruit_options['form_shortcode'];

$job_overall = get_field('job_overall', $post);
$deadline = get_field('deadline', $post);
$job_description = get_field('job_description', $post);
$file = get_field('file', $post);
?>

<section class="recruit-detail section xl:py-20">
    <div class="container">
        <h1 class="block-title"><?= the_title() ?></h1>
        <div class="grid grid-cols-12 base-gap mt-10">
            <div class="col-left xl:col-span-9 col-span-full space-y-10">
                <div class="box bg-neutral-50 grid md:grid-cols-[calc(360/960*100%)_1fr] p-5 md:p-10 base-gap">
                    <div class="img">
                        <span class="ratio-[300/360]"><?= custom_get_post_thumbnail($post->ID) ?></span>
                    </div>
                    <div class="content">
                        <div class="title subheader-24 font-bold text-primary-1 mb-6"><?php _e('Tổng quan công việc', 'canhcamtheme'); ?></div>
                        <div class="infos">
                            <?php foreach ($job_overall as $value): ?>
                                <div class="item py-3 border-b border-neutral-200 first:border-t flex items-center gap-8">
                                    <div class="label font-bold clamp:min-w-[160px]"><?= $value['label'] ?></div>
                                    <div class="ctn flex-1"><?= $value['content'] ?></div>
                                </div>
                            <?php endforeach; ?>
                            <?php if ($deadline) : ?>
                                <div class="item py-3 border-b border-neutral-200 first:border-t flex items-center gap-8 deadline">
                                    <div class="label font-bold clamp:min-w-[160px]"><?php _e('Hạn nộp hồ sơ', 'canhcamtheme'); ?></div>
                                    <div class="ctn flex-1"><?= dateFormatOnLayout($deadline) ?></div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php foreach ($job_description as $value): ?>
                    <div class="box p-5 md:p-10 bg-neutral-50">
                        <div class="title subheader-24 font-bold text-primary-1 mb-6">
                            <h2><?= $value['title'] ?></h2>
                        </div>
                        <div class="description simple-prose">
                            <?= $value['description'] ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <div class="col-right xl:col-span-3 col-span-full">
                <div class="wrapper sticky top-20 space-y-10">
                    <?php if ($form_shortcode || $file) : ?>
                        <div class="box-apply p-6 bg-neutral-50 space-y-3">
                            <?php if ($form_shortcode) : ?>
                                <a class="btn btn-primary w-full" data-fancybox data-src="#popup-form" data-popup="popup-form"><?php _e('ỨNG TUYỂN NGAY', 'canhcamtheme'); ?> <i class="fa-regular fa-plus"></i></a>
                            <?php endif; ?>
                            <?php if ($file) : ?>
                                <a href="<?= $file['url'] ?>" target="_blank" class="btn btn-primary btn-light w-full"><i class="fa-regular fa-file-arrow-down"></i> <?php _e('TẢI FORM ỨNG', 'canhcamtheme'); ?> </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    <div class="other-recruit">
                        <div class="title subheader-24 font-bold text-white bg-primary-1 py-5 px-6">
                            <?php _e('Vị trí tuyển dụng khác', 'canhcamtheme'); ?>
                        </div>
                        <div class="list">
                            <?php if ($the_query->have_posts()) : while ($the_query->have_posts()) : $the_query->the_post(); ?>
                                    <?php
                                    $job_deadline = get_field('deadline', get_the_ID());
                                    ?>
                                    <div class="item p-6 border-x border-neutral-100 border-b relative hover:bg-neutral-100 transition-all"><a
                                            href="<?= get_the_permalink() ?>"
                                            title="<?= the_title() ?>"
                                            class="absolute inset-0 z-1"></a>
                                        <div class="label font-bold subheader-20"><?= the_title() ?></div>
                                        <?php if ($job_deadline) : ?>
                                            <div class="deadline text-primary-1 flex flex-wrap gap-2 mt-4">
                                                <i class="fa-regular fa-calendar-star"></i> <?php _e('Hạn nộp hồ sơ:', 'canhcamtheme'); ?>
                                                <time class="text-neutral-950"><?= dateFormatOnLayout($job_deadline) ?></time>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                            <?php endwhile;
                            endif;
                            wp_reset_postdata(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php if ($form_shortcode) : ?>

    <div class="hidden">
        <div id="popup-form">
            <div class="block-title text-center text-primary-1"><?php _e('Ứng tuyển ngay', 'canhcamtheme'); ?></div>
            <div class="job-title subheader-20 font-Fira text-center font-medium my-2"><?= the_title() ?></div>
            <!-- <div class="form-wrap grid gap-3 sm:grid-cols-2 md:gap-5">
            <div class="form-group">
                <input placeholder="First Name...">
            </div>
            <div class="form-group">
                <input placeholder="Last Name...">
            </div>
            <div class="form-group col-span-full">
                <input placeholder="Phone...">
            </div>
            <div class="form-group col-span-full">
                <input placeholder="Email...">
            </div>
            <div class="form-group col-span-full form-upload">
                <label class="file-upload-type">Attach file 1 ( Rar,Zip,Doc,Docx,Pdf, &lt;1MB )<span class="wpcf7-form-control-wrap" data-name="fileUpload">
                        <input class="wpcf7-form-control wpcf7-file wpcf7-validates-as-required" size="40" accept=".rar,.zip,.doc,.docx,.pdf" aria-required="true" aria-invalid="false" type="file" name="fileUpload"></span></label>
            </div>
            <div class="form-group col-span-full">
                <textarea placeholder="Messenger..."></textarea>
            </div>
            <div class="form-notice body-16">By clicking <strong>Submit</strong>, you agree to CLV <a>privacy policy</a> and <a>terms of use</a>, and in particular, you expressly agree to the transfer of your personal information for the purposes described in that policy.</div>
            <div class="form-submit button text-center col-span-full">
                <button class="btn btn-primary trigger-hover mx-auto">Submit<i class="fa-solid fa-arrow-up-right"></i></button>
            </div>
        </div> -->
            <?= do_shortcode($form_shortcode) ?>
        </div>
    </div>
<?php endif; ?>
<?php get_footer(); ?>


<script>
    window.addEventListener('load', function() {
        const title = "<?= the_title() ?>";
        jQuery(function($) {
            $('.wpcf7-form').each(function() {
                $(this).find('input[name="jobPosition"]').val(title);
            })

            function formatBytes(bytes, decimals = 2) {
                if (!+bytes) return '0 Bytes';
                const k = 1024;
                const dm = decimals < 0 ? 0 : decimals;
                const sizes = [
                    'Bytes',
                    'KiB',
                    'MiB',
                    'GiB',
                    'TiB',
                    'PiB',
                    'EiB',
                    'ZiB',
                    'YiB',
                ];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
            }
            $(document).on('click', '.file-clear', function() {
                $(this)
                    .closest('.form-group')
                    .find('.file-info')
                    .slideUp('500', function() {
                        $(this).remove();
                    });

                $(this).closest('.form-group').find('input[type="file"]').val('');
            });

            $(document).on('change', '.form-upload input[type="file"]', function(event) {
                $(this).closest('.form-group').find('.file-info').remove();
                const errorMessage = event.target.validationMessage
                // console.log("🟩 ~ $ ~ errorMessage:", errorMessage)
                var file = event.target.files[0];
                var size = formatBytes(file.size);
                var name = file.name;
                var type = file.type;
                $(this).closest('.form-group').append("<div class='file-info'></div>");
                $(this)
                    .closest('.form-group')
                    .find('.file-info')
                    .append(`<div class="file-name">${name}</div>`);

                $(this)
                    .closest('.form-group')
                    .find('.file-info')
                    .append(`<div class="file-size">${size}</div>`);

                $(this)
                    .closest('.form-group')
                    .find('.file-info')
                    .append(`<div class="file-type">${type}</div>`);

                $(this)
                    .closest('.form-group')
                    .find('.file-info')
                    .append(
                        `<div class="file-clear"><svg width='22' height='22' viewBox='0 0 22 22' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M20.6875 20.75C20.3125 21.125 19.625 21.125 19.25 20.75L11 12.4375L2.6875 20.75C2.3125 21.125 1.625 21.125 1.25 20.75C0.875 20.375 0.875 19.6875 1.25 19.3125L9.5625 11L1.25 2.75C0.875 2.375 0.875 1.6875 1.25 1.3125C1.625 0.9375 2.3125 0.9375 2.6875 1.3125L11 9.625L19.25 1.3125C19.625 0.9375 20.3125 0.9375 20.6875 1.3125C21.0625 1.6875 21.0625 2.375 20.6875 2.75L12.375 11L20.6875 19.3125C21.0625 19.6875 21.0625 20.375 20.6875 20.75Z' fill='#012D22'/></svg></div>`
                    );
                if (errorMessage) {
                    $(this)
                        .closest('.form-group')
                        .find('.file-info')
                        .append(`<div class="file-error">${errorMessage}</div>`);
                }
            });

            var wpcf7Elm = document.querySelectorAll('.wpcf7');
            wpcf7Elm.forEach(function() {
                this.addEventListener('wpcf7mailsent', function(event) {
                    // const findField = event.detail.inputs.find(({
                    //     type
                    // }) => type === 'file');
                    // console.log("🟩 ~ this.addEventListener ~ findField:", findField)
                    $('.form-group').find('.file-info').remove();

                    $('.wpcf7-form').each(function() {
                        $(this).find('input[name="jobPosition"]').val(title);
                    })
                }, false);
            })
        })
    })
</script>