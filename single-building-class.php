<?php


global $post;


$post_id = $post->ID;


get_header();

$postTypes = ['office-rent', 'office-rent-full', 'business-space', 'real-estate'];

$paged = (get_query_var('paged')) ? get_query_var('paged') : 1;

// Get sort parameter from URL
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'default';

// Set up query arguments based on sort parameter
$query_args = array(
    'post_type' => $postTypes,
    'posts_per_page' => 24,
    'paged' => $paged,
    'suppress_filters' => false,
    'meta_query' => array(
        'relation' => 'OR',
        array(
            'key' => 'dynamic_field_thong_so_toa_nha_hang-toa-nha',
            'value' => $post_id
        ),
        array(
            'key' => 'dynamic_field_ts_toa_nha_tron_goi_hang-toa-nha',
            'value' => $post_id
        )
        
    )
);

// Apply sorting based on URL parameter
if ($sort == 'price-asc') {
    $query_args['meta_key'] = 'price_product';
    $query_args['orderby'] = 'meta_value_num';
    $query_args['order'] = 'ASC';
} elseif ($sort == 'price-desc') {
    $query_args['meta_key'] = 'price_product';
    $query_args['orderby'] = 'meta_value_num';
    $query_args['order'] = 'DESC';
} else {
    $query_args['orderby'] = 'title';
    $query_args['order'] = 'ASC';
}

$office_rent_query = new WP_Query($query_args);

// // Get wards by district_id
// $wards = get_posts(array(
//     'post_type' => 'ward',
//     'posts_per_page' => -1,
//     'orderby' => 'title',
//     'order' => 'ASC',
//     'suppress_filters' => false,
//     'meta_query' => array(
//         array(
//             'key' => 'district_id',
//             'value' => $district_id
//         )
//     )
// ));

// // Prepare the list of wards with their post counts
// $wards_with_counts = array();

// foreach ($wards as $ward) {
//     $ward_id = $ward->ID;

//     // Count posts for each post type
//     $office_rent_count = new WP_Query(array(
//         'post_type' => $chu_de,
//         'posts_per_page' => -1,
//         'fields' => 'ids', // Only get post IDs for better performance
//         'meta_query' => array(
//             array(
//                 'key' => '_office_ward',
//                 'value' => $ward_id,
//                 'compare' => '='
//             )
//         )
//     ));

//     // Calculate total count across all post types
//     $total_count = $office_rent_count->found_posts;

//     // Only add wards that have at least one post
//     if ($total_count > 0) {
//         $wards_with_counts[] = array(
//             'id' => $ward_id,
//             'title' => $ward->post_title,
//             'count' => $total_count,
//             'permalink' => get_permalink($ward_id)
//         );
//     }

//     // Clean up queries to free memory
//     wp_reset_postdata();
// }

// // Sort wards by count (highest first)
// usort($wards_with_counts, function ($a, $b) {
//     return $b['count'] - $a['count'];
// });



// // Get blocks with associated properties
// $blocks = get_posts(array(
//     'post_type' => 'block',
//     'posts_per_page' => -1,
//     'orderby' => 'title',
//     'order' => 'ASC',
//     'suppress_filters' => false
// ));

// $blocks_with_counts = array();

// foreach ($blocks as $block) {
//     $block_id = $block->ID;

//     // Count posts for each post type
//     $office_rent_count = new WP_Query(array(
//         'post_type' => $chu_de,
//         'posts_per_page' => -1,
//         'fields' => 'ids', // Only get post IDs for better performance
//         'meta_query' => array(
//             array(
//                 'key' => '_office_block',
//                 'value' => $block_id,
//                 'compare' => '='
//             )
//         )
//     ));


//     // Calculate total count across all post types
//     $total_count = $office_rent_count->found_posts;

//     // Only add blocks that have at least one post
//     if ($total_count > 0) {
//         $blocks_with_counts[] = array(
//             'id' => $block_id,
//             'title' => $block->post_title,
//             'count' => $total_count,
//             'permalink' => get_permalink($block_id)
//         );
//     }

//     // Clean up queries to free memory
//     wp_reset_postdata();
// }

// // Sort blocks by count (highest first)
// usort($blocks_with_counts, function ($a, $b) {
//     return $b['count'] - $a['count'];
// });


// // Get streets by district_id
// $streets = get_posts(array(
//     'post_type' => 'street',
//     'posts_per_page' => -1,
//     'orderby' => 'title',
//     'order' => 'ASC',
//     'suppress_filters' => false,
//     'meta_query' => array(
//         array(
//             'key' => 'district_id',
//             'value' => $district_id
//         )
//     )
// ));

// // Prepare the list of streets with their post counts
// $streets_with_counts = array();

// foreach ($streets as $street) {
//     $street_id = $street->ID;

//     // Count posts for each post type
//     $office_rent_count = new WP_Query(array(
//         'post_type' => $chu_de,
//         'posts_per_page' => -1,
//         'fields' => 'ids', // Only get post IDs for better performance
//         'meta_query' => array(
//             array(
//                 'key' => '_office_street',
//                 'value' => $street_id,
//                 'compare' => '='
//             )
//         )
//     ));


//     // Calculate total count across all post types
//     $total_count = $office_rent_count->found_posts;

//     // Only add streets that have at least one post
//     if ($total_count > 0) {
//         $streets_with_counts[] = array(
//             'id' => $street_id,
//             'title' => $street->post_title,
//             'count' => $total_count,
//             'permalink' => get_permalink($street_id)
//         );
//     }

//     // Clean up queries to free memory
//     wp_reset_postdata();
// }

// // Sort streets by count (highest first)
// usort($streets_with_counts, function ($a, $b) {
//     return $b['count'] - $a['count'];
// });


?>

<div class="scroll-section filter-nav bg-primary-1/5 py-10" data-title="Tìm kiếm">
    <div class="container">

        <?php get_template_part('components/filter/filter-archive'); ?>
    </div>
</div>


<div class="rate-page">
    <div class="container">
        <div class="wrapper flex items-center gap-3">
            <div class="stars text-[#FFBF00] flex items-center gap-2"><i class="fa-solid fa-star"></i><i class="fa-solid fa-star"></i><i class="fa-solid fa-star"></i><i class="fa-solid fa-star"></i><i class="fa-solid fa-star"></i>
            </div>
            <div class="content">
                <p> <strong class="current">5.0 </strong>trên <strong class="total">5</strong> với xếp
                    hạng</p>
            </div>
        </div>
    </div>
</div>
<section class="scroll-section district-1 section pt-10 xl:pb-20" data-title="Tổng quan">
    <div class="container">
        <h2 class="block-title"><?php echo get_the_title($post_id); ?></h2>
        <div class="description mt-5 font-medium space-y-4">
            <?php echo get_the_excerpt($post_id); ?>
        </div>
        <div class="filter flex items-center gap-2 sm:gap-5 mt-10 flex-wrap">
            <div class="label font-bold text-black">Sắp xếp theo</div>
            <select name="sort" id="sort" onchange="window.location.href = '<?php echo esc_js(add_query_arg(array('paged' => false), get_permalink())); ?>?sort=' + this.value">
                <option value="default" <?php selected($sort, 'default'); ?>>Mặc định</option>
                <option value="price-asc" <?php selected($sort, 'price-asc'); ?>>Giá từ thấp đến cao</option>
                <option value="price-desc" <?php selected($sort, 'price-desc'); ?>>Giá từ cao đến thấp</option>
            </select>
        </div>
        <div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 base-gap mt-10">
            <?php
            if ($office_rent_query->have_posts()) :
                while ($office_rent_query->have_posts()) : $office_rent_query->the_post();
                    $post_id = get_the_ID();
                    if (get_post_type($post_id) == 'office-rent-full') {
                        get_template_part('components/boxProduct/box-rent-2', '', array('post_id' => $post_id));
                    } else {
                        get_template_part('components/boxProduct/box-rent-1', '', array('post_id' => $post_id));
                    }
                endwhile;
            else :
                echo '<div class="col-span-full text-center">' . __('Không tìm thấy kết quả nào', 'canhcamtheme') . '</div>';
            endif;
            ?>

        </div>
        <?php echo wp_bootstrap_pagination(array("custom_query" => $office_rent_query)); ?>
    </div>
</section>


<?php
if (get_the_content($post->ID)) {
?>
    <section class="cites-5 section">
        <div class="container">
            <div class="section bg-primary-1/5 rounded-4 px-6 md:rem:px-[60px]">
                <div class="wrap-show-content">
                    <div class="show-content expand-content rem:max-h-[240px] overflow-hidden">
                        <article class="prose font-medium">
                            <?php echo get_the_content($post->ID); ?>
                        </article>
                    </div><a class="btn btn-expand mx-auto mt-4 btn-primary "><span><?php echo __('Xem thêm', 'canhcamtheme'); ?></span><span><?php echo __('Thu gọn', 'canhcamtheme'); ?></span><i class="fa-light fa-plus"></i></a>
                </div>
            </div>
        </div>
    </section>
<?php
}
?>


<?php get_template_part('components/product/box-footer-product'); ?>



<?php
get_footer();
